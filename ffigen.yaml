# Run with `dart run ffigen --config simplified-ffigen.yaml`.
name: FlutterBleBindings
description: |
  Bindings for Flutter BLE adapter and scanner.
  
  Regenerate bindings with `dart run ffigen --config simplified-ffigen.yaml`.
output: 'lib/flutter_ble_bindings_generated.dart'
headers:
  entry-points:
    - 'src/topping_controller/devices/flutter/FlutterBleScanner.h'
    - 'src/topping_controller/devices/flutter/FlutterBleAdapter.h'
    - 'src/topping_controller/devices/flutter/FlutterGatt.h'
  include-directives:
    - 'src/topping_controller/devices/flutter/FlutterBleScanner.h'
    - 'src/topping_controller/devices/flutter/FlutterBleAdapter.h'
    - 'src/topping_controller/devices/flutter/FlutterGatt.h'

preamble: |
  // ignore_for_file: always_specify_types
  // ignore_for_file: camel_case_types
  // ignore_for_file: non_constant_identifier_names
comments:
  style: any
  length: full
