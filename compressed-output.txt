This file is a merged representation of a subset of the codebase, containing specifically included files and files not matching ignore patterns, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed, content has been compressed (code blocks are separated by ⋮---- delimiter).

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
4. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: lib/**/*.dart, pubspec.*, android/app/src/**/*.kt, ios/Runner/**/*.swift, test/**/*.dart
- Files matching these patterns are excluded: **/build/**, **/ios/.symlinks/**, **/Pods/**, **/*.xcworkspace, **/.dart_tool/**, **/.idea/**, **/*.g.dart, **/*.freezed.dart, **/generated_plugins/**
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Empty lines have been removed from all files
- Content has been compressed - code blocks are separated by ⋮---- delimiter
- Files are sorted by Git change count (files with more changes are at the bottom)

Additional Info:
----------------

================================================================
Directory Structure
================================================================
lib/
  bluetooth/
    connection/
      ble_connector.dart
    ffi/
      ble_ffi_initializer.dart
      ble_ffi_registry.dart
      ble_native_notifier.dart
    gatt/
      characteristic_handler.dart
    scanner/
      ble_scanner.dart
      controller_scanner_bindings.dart
    ble_bindings.dart
    ble_notification_helper.dart
    ble_operation_manager.dart
  config/
    ble_config.dart
  device/
    d900/
      d900_device_bindings.dart
      d900_device_manager.dart
    dx5/
      dx5ii_device_bindings.dart
      dx5ii_device_manager.dart
    device_factory.dart
    topping_device_manager.dart
  event/
    connection_state_event.dart
  examples/
    enhanced_device_example.dart
  gaia/
    rwcp/
      RWCP.dart
      RWCPClient.dart
      RWCPListener.dart
      Segment.dart
    ConfirmationType.dart
    GAIA.dart
    GaiaPacketBLE.dart
    OpCodes.dart
    ResumePoints.dart
    UpgradeStartCFMStatus.dart
    VMUPacket.dart
  interfaces/
    bluetooth_operations.dart
    device_events.dart
    device_operations.dart
  mappers/
    ffi_mapper.dart
  model/
    base/
      topping_device_callback.dart
      topping_device_settings.dart
      topping_device_state.dart
      topping_verify_result_type.dart
    bluetooth/
      ble_device.dart
      gatt_characteristic.dart
    d900/
      d900_callback.dart
      d900_settings.dart
      d900_verify_result_type.dart
    dx5ii/
      dx5ii_callback.dart
      dx5ii_settings.dart
      dx5ii_verify_result_type.dart
    enums/
      ble_connection_state.dart
      device_mode_type.dart
    ffi/
      ffi_ble_scanner_functions.dart
      ffi_bluetooth_profile_state.dart
      ffi_connection_state.dart
      ffi_d900_device_callback.dart
      ffi_d900_settings.dart
      ffi_dx5ii_device_callback.dart
      ffi_dx5ii_scan_result.dart
      ffi_dx5ii_settings.dart
      ffi_gatt_characteristic.dart
      ffi_gatt_functions.dart
      ffi_scan_callback_t.dart
      ffi_scan_result.dart
  registry/
    current_connecting_device.dart
    device_data_manager.dart
    service_registry.dart
  service/
    ota_server.dart
  ui/
    ota_view.dart
  utils/
    ble_uuid_util.dart
    constants.dart
    errors.dart
    log_util.dart
    string_utils.dart
pubspec.lock
pubspec.yaml

================================================================
Files
================================================================

================
File: lib/bluetooth/scanner/controller_scanner_bindings.dart
================
import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';
import 'package:topping_ble_control/utils/log_util.dart';
final class FFIControllerScanResult extends Struct {
  external Pointer<Utf8> name;
  @Int64()
  external int device;
  @Int32()
  external int rssi;
}
final class FFIControllerScannerCallback extends Struct {
  external Pointer<
      NativeFunction<
          Void Function(
              Int64, Pointer<FFIControllerScanResult>, IntPtr)>>
  on_scan_results;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_scan_failed;
}
typedef NativeControllerScannerCreate = Int64 Function(
    Int64 flutterObject, Pointer<FFIControllerScannerCallback> callback);
typedef DartControllerScannerCreate = int Function(
    int flutterObject, Pointer<FFIControllerScannerCallback> callback);
typedef NativeControllerScannerDestroy = Void Function(Int64 nativeObject);
typedef DartControllerScannerDestroy = void Function(int nativeObject);
typedef NativeControllerScannerStartScan = Void Function(Int64 nativeObject);
typedef DartControllerScannerStartScan = void Function(int nativeObject);
typedef NativeControllerScannerStopScan = Void Function(Int64 nativeObject);
typedef DartControllerScannerStopScan = void Function(int nativeObject);
class ControllerScannerBindings {
  static ControllerScannerBindings? _instance;
  static ControllerScannerBindings get instance {
    _instance ??= ControllerScannerBindings._();
    return _instance!;
  }
  late final DynamicLibrary _dylib;
  late final DartControllerScannerCreate _controllerScannerCreate;
  late final DartControllerScannerDestroy _controllerScannerDestroy;
  late final DartControllerScannerStartScan _controllerScannerStartScan;
  late final DartControllerScannerStopScan _controllerScannerStopScan;
  ControllerScannerBindings._() {
    _dylib = Platform.isAndroid
        ? DynamicLibrary.open("libtopping_controller.so")
        : DynamicLibrary.process();
    _loadFunctions();
  }
  void _loadFunctions() {
    _controllerScannerCreate = _dylib
        .lookup<NativeFunction<NativeControllerScannerCreate>>(
        'controller_scanner_create')
        .asFunction<DartControllerScannerCreate>();
    _controllerScannerDestroy = _dylib
        .lookup<NativeFunction<NativeControllerScannerDestroy>>(
        'controller_scanner_destory')
        .asFunction<DartControllerScannerDestroy>();
    _controllerScannerStartScan = _dylib
        .lookup<NativeFunction<NativeControllerScannerStartScan>>(
        'controller_scanner_startScan')
        .asFunction<DartControllerScannerStartScan>();
    _controllerScannerStopScan = _dylib
        .lookup<NativeFunction<NativeControllerScannerStopScan>>(
        'controller_scanner_stopScan')
        .asFunction<DartControllerScannerStopScan>();
  }
  int createScanner(
      int flutterObject, Pointer<FFIControllerScannerCallback> callback) {
    Log.d(
        "ControllerScannerBindings: Creating native scanner instance, flutterObject: $flutterObject");
    return _controllerScannerCreate(flutterObject, callback);
  }
  void destroyScanner(int nativeObject) {
    Log.d(
        "ControllerScannerBindings: Destroying native scanner instance, nativeObject: $nativeObject");
    _controllerScannerDestroy(nativeObject);
  }
  void startScan(int nativeObject) {
    Log.d(
        "ControllerScannerBindings: Starting native scan, nativeObject: $nativeObject");
    _controllerScannerStartScan(nativeObject);
  }
  void stopScan(int nativeObject) {
    Log.d(
        "ControllerScannerBindings: Stopping native scan, nativeObject: $nativeObject");
    _controllerScannerStopScan(nativeObject);
  }
}

================
File: lib/device/topping_device_manager.dart
================
import 'dart:async';
import 'package:rxdart/rxdart.dart';
import 'package:topping_ble_control/bluetooth/ble_operation_manager.dart';
import 'package:topping_ble_control/event/connection_state_event.dart';
import 'package:topping_ble_control/model/base/topping_device_state.dart';
import 'package:topping_ble_control/model/bluetooth/ble_device.dart';
import 'package:topping_ble_control/model/base/topping_verify_result_type.dart';
import 'package:topping_ble_control/model/dx5ii/dx5ii_settings.dart';
import 'package:topping_ble_control/model/enums/device_mode_type.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import '../model/enums/ble_connection_state.dart';
import '../registry/current_connecting_device.dart';
import '../registry/device_data_manager.dart';
import 'device_factory.dart';
abstract class ToppingDeviceManager implements DeviceManager {
  late final StreamController<List<BleDevice>> scanResultsController;
  late final StreamController<ConnectionStateEvent> deviceStateController;
  late final StreamController<bool> isScanningController;
  late final StreamController<ToppingVerifyResultType> verifyResultController;
  late final StreamController<bool> powerStateController;
  late final StreamController<String> deviceNameController;
  late final StreamController<int> volumeController;
  late final StreamController<bool> muteController;
  late final StreamController<int> inputTypeController;
  late final StreamController<int> outputTypeController;
  late final StreamController<bool> headphoneEnabledController;
  late final StreamController<int> headphoneGainController;
  late final StreamController<int> displayModeController;
  late final StreamController<int> themeController;
  late final StreamController<int> powerTriggerController;
  late final StreamController<int> balanceController;
  late final StreamController<int> filterController;
  late final StreamController<int> decodeModeController;
  late final StreamController<bool> audioBluetoothController;
  late final StreamController<bool> bluetoothAptxController;
  late final StreamController<bool> relayController;
  late final StreamController<int> multifunctionKeyController;
  late final StreamController<int> usbModeController;
  late final StreamController<int> screenBrightnessController;
  late final StreamController<int> languageController;
  late final StreamController<int> samplingRateController;
  late final StreamController<Dx5iiSettings> settingsController;
  final BleOperationManager _bleManager = BleOperationManager();
  StreamSubscription? _isScanningBlueSubscription;
  StreamSubscription? _scanResultsBlueSubscription;
  @override
  Stream<List<BleDevice>> get scanResults => scanResultsController.stream;
  @override
  Stream<bool> get isScanning => isScanningController.stream;
  @override
  Stream<ConnectionStateEvent> get deviceState => deviceStateController.stream;
  @override
  Stream<ToppingVerifyResultType> get verifyResult =>
      verifyResultController.stream;
  @override
  Stream<bool> get powerState => powerStateController.stream;
  @override
  Stream<String> get deviceName => deviceNameController.stream;
  @override
  Stream<int> get volume => volumeController.stream;
  @override
  Stream<bool> get mute => muteController.stream;
  @override
  Stream<int> get inputType => inputTypeController.stream;
  @override
  Stream<int> get outputType => outputTypeController.stream;
  @override
  Stream<bool> get headphoneEnabled => headphoneEnabledController.stream;
  @override
  Stream<int> get headphoneGain => headphoneGainController.stream;
  @override
  Stream<int> get displayMode => displayModeController.stream;
  @override
  Stream<int> get theme => themeController.stream;
  @override
  Stream<int> get powerTrigger => powerTriggerController.stream;
  @override
  Stream<int> get balance => balanceController.stream;
  @override
  Stream<int> get filter => filterController.stream;
  @override
  Stream<int> get decodeMode => decodeModeController.stream;
  @override
  Stream<bool> get audioBluetooth => audioBluetoothController.stream;
  @override
  Stream<bool> get bluetoothAptx => bluetoothAptxController.stream;
  @override
  Stream<bool> get relay => relayController.stream;
  @override
  Stream<int> get multifunctionKey => multifunctionKeyController.stream;
  @override
  Stream<int> get usbMode => usbModeController.stream;
  @override
  Stream<int> get screenBrightness => screenBrightnessController.stream;
  @override
  Stream<int> get language => languageController.stream;
  @override
  Stream<int> get samplingRate => samplingRateController.stream;
  @override
  Stream<Dx5iiSettings>? get settings => settingsController.stream;
  final _resetSettingsSubject = PublishSubject<void>();
  final _restoreFactorySettingsSubject = PublishSubject<void>();
  @override
  Stream<void> get onResetSettings => _resetSettingsSubject.stream;
  @override
  Stream<void> get onRestoreFactorySettings =>
      _restoreFactorySettingsSubject.stream;
  void initializeControllers() {
    scanResultsController = StreamController<List<BleDevice>>.broadcast();
    deviceStateController = StreamController<ConnectionStateEvent>.broadcast();
    isScanningController = StreamController<bool>.broadcast();
    verifyResultController =
        StreamController<ToppingVerifyResultType>.broadcast();
    powerStateController = StreamController<bool>.broadcast();
    deviceNameController = StreamController<String>.broadcast();
    volumeController = StreamController<int>.broadcast();
    muteController = StreamController<bool>.broadcast();
    inputTypeController = StreamController<int>.broadcast();
    outputTypeController = StreamController<int>.broadcast();
    headphoneEnabledController = StreamController<bool>.broadcast();
    headphoneGainController = StreamController<int>.broadcast();
    displayModeController = StreamController<int>.broadcast();
    themeController = StreamController<int>.broadcast();
    powerTriggerController = StreamController<int>.broadcast();
    balanceController = StreamController<int>.broadcast();
    filterController = StreamController<int>.broadcast();
    decodeModeController = StreamController<int>.broadcast();
    audioBluetoothController = StreamController<bool>.broadcast();
    bluetoothAptxController = StreamController<bool>.broadcast();
    relayController = StreamController<bool>.broadcast();
    multifunctionKeyController = StreamController<int>.broadcast();
    usbModeController = StreamController<int>.broadcast();
    screenBrightnessController = StreamController<int>.broadcast();
    languageController = StreamController<int>.broadcast();
    samplingRateController = StreamController<int>.broadcast();
    settingsController = StreamController<Dx5iiSettings>.broadcast();
  }
  void initializeBleListeners() {
    _scanResultsBlueSubscription = _bleManager.scanner.scanResults.listen((
      results,
    ) {
      if (!scanResultsController.isClosed) scanResultsController.add(results);
    });
    _isScanningBlueSubscription = FlutterBluePlus.isScanning.listen((state) {
      if (!isScanningController.isClosed) isScanningController.add(state);
    });
  }
  void triggerResetSettings() {
    _resetSettingsSubject.add(null);
  }
  void triggerRestoreFactorySettings() {
    _restoreFactorySettingsSubject.add(null);
  }
  void closeAllStreams() {
    scanResultsController.close();
    deviceStateController.close();
    isScanningController.close();
    verifyResultController.close();
    powerStateController.close();
    deviceNameController.close();
    volumeController.close();
    muteController.close();
    inputTypeController.close();
    outputTypeController.close();
    headphoneEnabledController.close();
    headphoneGainController.close();
    displayModeController.close();
    themeController.close();
    powerTriggerController.close();
    balanceController.close();
    filterController.close();
    decodeModeController.close();
    audioBluetoothController.close();
    bluetoothAptxController.close();
    relayController.close();
    multifunctionKeyController.close();
    usbModeController.close();
    screenBrightnessController.close();
    languageController.close();
    samplingRateController.close();
    settingsController.close();
    _resetSettingsSubject.close();
    _restoreFactorySettingsSubject.close();
  }
  BleOperationManager get bleManager => _bleManager;
  @override
  void startScan({
    List<DeviceModeType> deviceTypes = const [DeviceModeType.dx5],
  }) {
    Log.i('开始扫描设备');
    _bleManager.startScan(deviceTypes: deviceTypes);
  }
  @override
  void stopScan() {
    Log.i("停止扫描设备");
    _bleManager.stopScan();
  }
  ToppingVerifyResultType mapVerifyTypeToEnum(int type) {
    return ToppingVerifyResultTypeExtension.fromValue(type);
  }
  void safeAddEvent<T>(StreamController<T> controller, T event) {
    try {
      if (!controller.isClosed) {
        controller.add(event);
      }
    } catch (e, s) {
      Log.e('添加事件到控制器错误: $e\n$s');
    }
  }
  @override
  void setUsbType(int type) {
    Log.i("setUsbType方法未实现");
  }
  @override
  void enableUsbDsdPassthrough(bool enable) {
    Log.i("enableUsbDsdPassthrough方法未实现");
  }
  @override
  void setIisPhase(int phase) {
    Log.i("setIisPhase方法未实现");
  }
  @override
  void setIisDsdChannel(int channel) {
    Log.i("setIisDsdChannel方法未实现");
  }
  @override
  void setUsbSelect(int type) {
    Log.i("setUsbSelect方法未实现");
  }
  @override
  void enableUsbDsd(bool enable) {
    Log.i("enableUsbDsd方法未实现");
  }
  @override
  void setIisChannel(int channel) {
    Log.i("setIisChannel方法未实现");
  }
  dynamic get deviceBindings {
    throw UnimplementedError('子类必须提供设备绑定实例');
  }
  String get deviceTypeName {
    return "通用";
  }
  Future<void> connectDevice(int deviceHandle) async {
    Log.i(
      "$deviceTypeName 连接时间打印 --- 准备连接设备，句柄: $deviceHandle, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    CurrentConnectingDevice().handle = deviceHandle;
    String? deviceName;
    BleDevice? device = DeviceDataManager().getDeviceByHandle(deviceHandle);
    if (device == null) {
      Log.w("$deviceTypeName 通过句柄找不到设备，尝试使用其他方式查找");
      var allDevices = DeviceDataManager().getAllDevices();
      if (allDevices.isNotEmpty) {
        device = allDevices.first;
        Log.i(
          "$deviceTypeName 使用可用设备: ${device.name}, 句柄: ${device.nativeHandle}",
        );
        deviceHandle = device.nativeHandle;
        CurrentConnectingDevice().handle = deviceHandle;
      } else {
        Log.e("$deviceTypeName 找不到任何可用设备，无法连接");
        return;
      }
    }
    deviceName = device.name;
    if (device.flutterDevice == null) {
      Log.w("$deviceTypeName 设备没有Flutter设备对象，尝试查找匹配项");
      var allDevices = DeviceDataManager().getAllDevices();
      for (var d in allDevices) {
        if (d.flutterDevice != null) {
          Log.i("$deviceTypeName 找到带Flutter设备对象的设备: ${d.name}");
          device.flutterDevice = d.flutterDevice;
          DeviceDataManager().registerDevice(device);
          break;
        }
      }
    }
    final connectingEvent = ConnectionStateEvent(
      BleConnectionState.connecting,
      deviceHandle,
      deviceName: deviceName,
    );
    if (!deviceStateController.isClosed) {
      deviceStateController.add(connectingEvent);
    }
    Log.i(
      "$deviceTypeName 发送连接中事件完成, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    Log.i(
      "$deviceTypeName 调用底层连接函数, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    try {
      await bleManager.connect(deviceHandle: deviceHandle).catchError((error) {
        Log.e("$deviceTypeName 连接设备时发生错误: $error");
        final disconnectedEvent = ConnectionStateEvent(
          BleConnectionState.disconnected,
          deviceHandle,
          deviceName: deviceName,
        );
        if (!deviceStateController.isClosed) {
          deviceStateController.add(disconnectedEvent);
        }
      });
      connectNative(deviceHandle);
      Log.i(
        "$deviceTypeName 底层连接函数调用完成, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
      );
    } catch (e) {
      Log.e("$deviceTypeName 连接设备时发生错误: $e");
      final disconnectedEvent = ConnectionStateEvent(
        BleConnectionState.disconnected,
        deviceHandle,
        deviceName: deviceName,
      );
      if (!deviceStateController.isClosed) {
        deviceStateController.add(disconnectedEvent);
      }
    }
  }
  void connectNative(int deviceHandle) {
    throw UnimplementedError('子类必须实现原生连接方法');
  }
  @override
  void dispose() {
    Log.i("释放ToppingDeviceManager资源");
    _isScanningBlueSubscription?.cancel();
    _scanResultsBlueSubscription?.cancel();
    closeAllStreams();
  }
  @override
  void enableHeadphone(bool enable) {
    Log.i("enableHeadphone方法是DX5II特有的，当前设备不支持");
  }
  @override
  void setHeadphoneGain(int gainType) {
    Log.i("setHeadphoneGain方法是DX5II特有的，当前设备不支持");
  }
}

================
File: lib/event/connection_state_event.dart
================
import '../model/enums/ble_connection_state.dart';
class ConnectionStateEvent {
  final BleConnectionState state;
  final int? deviceHandle;
  final String? deviceName;
  ConnectionStateEvent(this.state, this.deviceHandle, {this.deviceName});
}

================
File: lib/examples/enhanced_device_example.dart
================
import 'package:get/get.dart';
import 'package:topping_ble_control/device/device_factory.dart';
import 'package:topping_ble_control/interfaces/device_operations.dart';
import 'package:topping_ble_control/model/enums/device_mode_type.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import 'package:topping_ble_control/device/d900/d900_device_manager.dart';
class EnhancedDeviceExample {
  final DeviceFactory _deviceFactory = DeviceFactory();
  DeviceManager? get currentDeviceManager =>
      _deviceFactory.currentDeviceManager;
  bool get isD900Device =>
      currentDeviceManager?.getConnectedDevice()?.deviceType ==
      DeviceModeType.dx9;
  Future<void> _executeDeviceAction(
    String actionName,
    Future<void> Function(DeviceManager) action,
  ) async {
    final manager = currentDeviceManager;
    if (manager == null) {
      Log.w("无法执行 $actionName：设备未连接");
      Get.snackbar('错误', '设备未连接');
      return;
    }
    try {
      Log.i("执行操作: $actionName");
      await action(manager);
      Log.i("操作 $actionName 已完成");
    } catch (e) {
      Log.e("执行操作 $actionName 时出错: $e");
      Get.snackbar('错误', '操作失败: $e');
    }
  }
  void setVolume(int volume) {
    int adjustedVolume = volume;
    if (isD900Device && volume > -8) {
      adjustedVolume = -8;
    }
    int positiveVolume = -adjustedVolume;
    _executeDeviceAction("设置音量为 $adjustedVolume (发送值: $positiveVolume)", (
      manager,
    ) async {
      manager.setVolume(positiveVolume);
      return;
    });
  }
  void toggleMute(bool muteState) {
    _executeDeviceAction("设置静音为 $muteState", (manager) async {
      manager.setMute(muteState);
      return;
    });
  }
  void setInput(dynamic inputEnumValue) {
    if (inputEnumValue == null) return;
    int inputValue = inputEnumValue.index;
    _executeDeviceAction("设置输入为 $inputValue", (manager) async {
      manager.setInputType(inputValue);
      return;
    });
  }
  void setOutput(dynamic outputEnumValue) {
    if (outputEnumValue == null) return;
    int outputValue = outputEnumValue.index;
    _executeDeviceAction("设置输出为 $outputValue", (manager) async {
      manager.setOutputType(outputValue);
      return;
    });
  }
  void toggleHeadphone(bool enable) {
    _executeDeviceAction("${enable ? '启用' : '禁用'}耳机", (manager) async {
      manager.enableHeadphone(enable);
      return;
    });
  }
  void setHeadphoneGain(dynamic gainEnumValue) {
    if (gainEnumValue == null) return;
    int gainValue = gainEnumValue.index;
    _executeDeviceAction("设置耳机增益为 $gainValue", (manager) async {
      manager.setHeadphoneGain(gainValue);
      return;
    });
  }
  void setDisplayMode(dynamic displayEnumValue) {
    if (displayEnumValue == null) return;
    int displayModeValue = displayEnumValue.index;
    _executeDeviceAction("设置显示模式为 $displayModeValue", (manager) async {
      manager.setDisplayMode(displayModeValue);
      return;
    });
  }
  void setPower(bool isOn) {
    _executeDeviceAction("设置电源为 ${isOn ? '开启' : '关闭'}", (manager) async {
      (manager as dynamic).setPower(isOn);
      return;
    });
  }
  void setFilter(int filterValue) {
    if (isD900Device) {
      Log.w("D900设备不支持滤波器设置");
      Get.snackbar('提示', '当前设备不支持此操作');
      return;
    }
    _executeDeviceAction("设置滤波器为 $filterValue", (manager) async {
      manager.setFilter(filterValue);
      return;
    });
  }
  void setChannelBalance(int balance) {
    if (balance < -95 || balance > 95) return;
    _executeDeviceAction("设置声道平衡为 $balance", (manager) async {
      (manager as dynamic).setChannelBalance(balance);
      return;
    });
  }
  void requestSettings() {
    _executeDeviceAction("请求设备设置", (manager) async {
      manager.requestSettings();
      return;
    });
  }
  void resetSettings() {
    _executeDeviceAction("重置所有设置", (manager) async {
      manager.resetSettings();
      return;
    });
  }
  void restoreFactorySettings() {
    _executeDeviceAction("恢复出厂设置", (manager) async {
      manager.restoreFactorySettings();
      return;
    });
  }
  void setUsbSettings(int usbMode) {
    if (isD900Device) {
      _executeDeviceAction(
        "设置D900 USB类型为 $usbMode",
        (manager) async {
          (manager as D900DeviceManager).setUsbType(usbMode);
          return;
        },
      );
    } else {
      _executeDeviceAction("设置DX5 USB模式为 $usbMode", (manager) async {
        manager.setUsbMode(usbMode);
        return;
      });
    }
  }
}

================
File: lib/gaia/rwcp/RWCPListener.dart
================
abstract class RWCPListener{
  bool sendRWCPSegment(List<int> bytes);
  void onTransferFailed();
  void onTransferFinished();
  void onTransferProgress(int acknowledged);
}

================
File: lib/gaia/ConfirmationType.dart
================
class ConfirmationType{
  static const int TRANSFER_COMPLETE = 1;
  static const int COMMIT = 2;
  static const int IN_PROGRESS = 3;
  static const int WARNING_FILE_IS_DIFFERENT = 4;
  static const int BATTERY_LOW_ON_DEVICE = 5;
}

================
File: lib/gaia/GAIA.dart
================
class GAIA{
  static const int COMMAND_MASK = 0x7FFF;
  static const int ACKNOWLEDGMENT_MASK = 0x8000;
  static const int VENDOR_NONE = 0x7FFE;
  static const int VENDOR_QUALCOMM = 0x000A;
  static const int COMMANDS_CONFIGURATION_MASK = 0x0100;
  static const int COMMAND_SET_RAW_CONFIGURATION = 0x0100;
  static const int COMMAND_GET_CONFIGURATION_VERSION = 0x0180;
  static const int COMMAND_SET_LED_CONFIGURATION = 0x0101;
  static const int COMMAND_GET_LED_CONFIGURATION = 0x0181;
  static const int COMMAND_SET_TONE_CONFIGURATION = 0x0102;
  static const int COMMAND_GET_TONE_CONFIGURATION = 0x0182;
  static const int COMMAND_SET_DEFAULT_VOLUME = 0x0103;
  static const int COMMAND_GET_DEFAULT_VOLUME = 0x0183;
  static const int COMMAND_FACTORY_DEFAULT_RESET = 0x0104;
  static const int COMMAND_GET_CONFIGURATION_ID = 0x0184;
  static const int COMMAND_SET_VIBRATOR_CONFIGURATION = 0x0105;
  static const int COMMAND_GET_VIBRATOR_CONFIGURATION = 0x0185;
  static const int COMMAND_SET_VOICE_PROMPT_CONFIGURATION = 0x0106;
  static const int COMMAND_GET_VOICE_PROMPT_CONFIGURATION = 0x0186;
  static const int COMMAND_SET_FEATURE_CONFIGURATION = 0x0107;
  static const int COMMAND_GET_FEATURE_CONFIGURATION = 0x0187;
  static const int COMMAND_SET_USER_EVENT_CONFIGURATION = 0x0108;
  static const int COMMAND_GET_USER_EVENT_CONFIGURATION = 0x0188;
  static const int COMMAND_SET_TIMER_CONFIGURATION = 0x0109;
  static const int COMMAND_GET_TIMER_CONFIGURATION = 0x0189;
  static const int COMMAND_SET_AUDIO_GAIN_CONFIGURATION = 0x010A;
  static const int COMMAND_GET_AUDIO_GAIN_CONFIGURATION = 0x018A;
  static const int COMMAND_SET_VOLUME_CONFIGURATION = 0x010B;
  static const int COMMAND_GET_VOLUME_CONFIGURATION = 0x018B;
  static const int COMMAND_SET_POWER_CONFIGURATION = 0x010C;
  static const int COMMAND_GET_POWER_CONFIGURATION = 0x018C;
  static const int COMMAND_SET_USER_TONE_CONFIGURATION = 0x010E;
  static const int COMMAND_GET_USER_TONE_CONFIGURATION = 0x018E;
  static const int COMMAND_SET_DEVICE_NAME = 0x010F;
  static const int COMMAND_GET_DEVICE_NAME = 0x018F;
  static const int COMMAND_SET_WLAN_CREDENTIALS = 0x0110;
  static const int COMMAND_GET_WLAN_CREDENTIALS = 0x0190;
  static const int COMMAND_SET_PEER_PERMITTED_ROUTING = 0x0111;
  static const int COMMAND_GET_PEER_PERMITTED_ROUTING = 0x0191;
  static const int COMMAND_SET_PERMITTED_NEXT_AUDIO_SOURCE = 0x0112;
  static const int COMMAND_GET_PERMITTED_NEXT_AUDIO_SOURCE = 0x0192;
  static const int COMMAND_SET_ONE_TOUCH_DIAL_STRING = 0x0116;
  static const int COMMAND_GET_ONE_TOUCH_DIAL_STRING = 0x0196;
  static const int COMMAND_GET_MOUNTED_PARTITIONS = 0x01A0;
  static const int COMMAND_SET_DFU_PARTITION = 0x0121;
  static const int COMMAND_GET_DFU_PARTITION = 0x01A1;
  static const int COMMANDS_CONTROLS_MASK = 0x0200;
  static const int COMMAND_CHANGE_VOLUME = 0x0201;
  static const int COMMAND_DEVICE_RESET = 0x0202;
  static const int COMMAND_GET_BOOT_MODE = 0x0282;
  static const int COMMAND_SET_PIO_CONTROL = 0x0203;
  static const int COMMAND_GET_PIO_CONTROL = 0x0283;
  static const int COMMAND_SET_POWER_STATE = 0x0204;
  static const int COMMAND_GET_POWER_STATE = 0x0284;
  static const int COMMAND_SET_VOLUME_ORIENTATION = 0x0205;
  static const int COMMAND_GET_VOLUME_ORIENTATION = 0x0285;
  static const int COMMAND_SET_VIBRATOR_CONTROL = 0x0206;
  static const int COMMAND_GET_VIBRATOR_CONTROL = 0x0286;
  static const int COMMAND_SET_LED_CONTROL = 0x0207;
  static const int COMMAND_GET_LED_CONTROL = 0x0287;
  static const int COMMAND_FM_CONTROL = 0x0208;
  static const int COMMAND_PLAY_TONE = 0x0209;
  static const int COMMAND_SET_VOICE_PROMPT_CONTROL = 0x020A;
  static const int COMMAND_GET_VOICE_PROMPT_CONTROL = 0x028A;
  static const int COMMAND_CHANGE_AUDIO_PROMPT_LANGUAGE = 0x020B;
  static const int COMMAND_SET_SPEECH_RECOGNITION_CONTROL = 0x020C;
  static const int COMMAND_GET_SPEECH_RECOGNITION_CONTROL = 0x028C;
  static const int COMMAND_ALERT_LEDS = 0x020D;
  static const int COMMAND_ALERT_TONE = 0x020E;
  static const int COMMAND_ALERT_EVENT = 0x0210;
  static const int COMMAND_ALERT_VOICE = 0x0211;
  static const int COMMAND_SET_AUDIO_PROMPT_LANGUAGE = 0x0212;
  static const int COMMAND_GET_AUDIO_PROMPT_LANGUAGE = 0x0292;
  static const int COMMAND_START_SPEECH_RECOGNITION = 0x0213;
  static const int COMMAND_SET_EQ_CONTROL = 0x0214;
  static const int COMMAND_GET_EQ_CONTROL = 0x0294;
  static const int COMMAND_SET_BASS_BOOST_CONTROL = 0x0215;
  static const int COMMAND_GET_BASS_BOOST_CONTROL = 0x0295;
  static const int COMMAND_SET_3D_ENHANCEMENT_CONTROL = 0x0216;
  static const int COMMAND_GET_3D_ENHANCEMENT_CONTROL = 0x0296;
  static const int COMMAND_SWITCH_EQ_CONTROL = 0x0217;
  static const int COMMAND_TOGGLE_BASS_BOOST_CONTROL = 0x0218;
  static const int COMMAND_TOGGLE_3D_ENHANCEMENT_CONTROL = 0x0219;
  static const int COMMAND_SET_EQ_PARAMETER = 0x021A;
  static const int COMMAND_GET_EQ_PARAMETER = 0x029A;
  static const int COMMAND_SET_EQ_GROUP_PARAMETER = 0x021B;
  static const int COMMAND_GET_EQ_GROUP_PARAMETER = 0x029B;
  static const int COMMAND_DISPLAY_CONTROL = 0x021C;
  static const int COMMAND_ENTER_BLUETOOTH_PAIRING_MODE = 0x021D;
  static const int COMMAND_SET_AUDIO_SOURCE = 0x021E;
  static const int COMMAND_GET_AUDIO_SOURCE = 0x029E;
  static const int COMMAND_AV_REMOTE_CONTROL = 0x021F;
  static const int COMMAND_SET_USER_EQ_CONTROL = 0x0220;
  static const int COMMAND_GET_USER_EQ_CONTROL = 0x02A0;
  static const int COMMAND_TOGGLE_USER_EQ_CONTROL = 0x0221;
  static const int COMMAND_SET_SPEAKER_EQ_CONTROL = 0x0222;
  static const int COMMAND_GET_SPEAKER_EQ_CONTROL = 0x02A2;
  static const int COMMAND_TOGGLE_SPEAKER_EQ_CONTROL = 0x0223;
  static const int COMMAND_SET_TWS_AUDIO_ROUTING = 0x0224;
  static const int COMMAND_GET_TWS_AUDIO_ROUTING = 0x02A4;
  static const int COMMAND_SET_TWS_VOLUME = 0x0225;
  static const int COMMAND_GET_TWS_VOLUME = 0x02A5;
  static const int COMMAND_TRIM_TWS_VOLUME = 0x0226;
  static const int COMMAND_SET_PEER_LINK_RESERVED = 0x0227;
  static const int COMMAND_GET_PEER_LINK_RESERVED = 0x02A7;
  static const int COMMAND_TWS_PEER_START_ADVERTISING = 0x022A;
  static const int COMMAND_FIND_MY_REMOTE = 0x022B;
  static const int COMMAND_SET_CODEC = 0x0240;
  static const int COMMAND_GET_CODEC = 0x02C0;
  static const int COMMAND_SET_SUPPORTED_FEATURES = 0x022C;
  static const int COMMAND_DISCONNECT = 0x022D;
  static const int COMMAND_SET_DATA_ENDPOINT_MODE = 0x022E;
  static const int COMMAND_GET_DATA_ENDPOINT_MODE = 0x02AE;
  static const int COMMANDS_POLLED_STATUS_MASK = 0x0300;
  static const int COMMAND_GET_API_VERSION = 0x0300;
  static const int COMMAND_GET_CURRENT_RSSI = 0x0301;
  static const int COMMAND_GET_CURRENT_BATTERY_LEVEL = 0x0302;
  static const int COMMAND_GET_MODULE_ID = 0x0303;
  static const int COMMAND_GET_APPLICATION_VERSION = 0x0304;
  static const int COMMAND_GET_PIO_STATE = 0x0306;
  static const int COMMAND_READ_ADC = 0x0307;
  static const int COMMAND_GET_PEER_ADDRESS = 0x030A;
  static const int COMMAND_GET_DFU_STATUS = 0x0310;
  static const int COMMAND_GET_HOST_FEATURE_INFORMATION = 0x0320;
  static const int COMMANDS_FEATURE_CONTROL_MASK = 0x0500;
  static const int COMMAND_GET_AUTH_BITMAPS = 0x0580;
  static const int COMMAND_AUTHENTICATE_REQUEST = 0x0501;
  static const int COMMAND_AUTHENTICATE_RESPONSE = 0x0502;
  static const int COMMAND_SET_FEATURE = 0x0503;
  static const int COMMAND_GET_FEATURE = 0x0583;
  static const int COMMAND_SET_SESSION_ENABLE = 0x0504;
  static const int COMMAND_GET_SESSION_ENABLE = 0x0584;
  static const int COMMANDS_DATA_TRANSFER_MASK = 0x0600;
  static const int COMMAND_DATA_TRANSFER_SETUP = 0x0601;
  static const int COMMAND_DATA_TRANSFER_CLOSE = 0x0602;
  static const int COMMAND_HOST_TO_DEVICE_DATA = 0x0603;
  static const int COMMAND_DEVICE_TO_HOST_DATA = 0x0604;
  static const int COMMAND_I2C_TRANSFER = 0x0608;
  static const int COMMAND_GET_STORAGE_PARTITION_STATUS = 0x0610;
  static const int COMMAND_OPEN_STORAGE_PARTITION = 0x0611;
  static const int COMMAND_OPEN_UART = 0x0612;
  static const int COMMAND_WRITE_STORAGE_PARTITION = 0x0615;
  static const int COMMAND_WRITE_STREAM = 0x0617;
  static const int COMMAND_CLOSE_STORAGE_PARTITION = 0x0618;
  static const int COMMAND_MOUNT_STORAGE_PARTITION = 0x061A;
  static const int COMMAND_GET_FILE_STATUS = 0x0620;
  static const int COMMAND_OPEN_FILE = 0x0621;
  static const int COMMAND_READ_FILE = 0x0624;
  static const int COMMAND_CLOSE_FILE = 0x0628;
  static const int COMMAND_DFU_REQUEST = 0x0630;
  static const int COMMAND_DFU_BEGIN = 0x0631;
  static const int COMMAND_DFU_WRITE = 0x0632;
  static const int COMMAND_DFU_COMMIT = 0x0633;
  static const int COMMAND_DFU_GET_RESULT = 0x0634;
  static const int COMMAND_VM_UPGRADE_CONNECT = 0x0640;
  static const int COMMAND_VM_UPGRADE_DISCONNECT = 0x0641;
  static const int COMMAND_VM_UPGRADE_CONTROL = 0x0642;
  static const int COMMAND_VM_UPGRADE_DATA = 0x0643;
  static const int COMMANDS_DEBUGGING_MASK = 0x0700;
  static const int COMMAND_NO_OPERATION = 0x0700;
  static const int COMMAND_GET_DEBUG_FLAGS = 0x0701;
  static const int COMMAND_SET_DEBUG_FLAGS = 0x0702;
  static const int COMMAND_RETRIEVE_PS_KEY = 0x0710;
  static const int COMMAND_RETRIEVE_FULL_PS_KEY = 0x0711;
  static const int COMMAND_STORE_PS_KEY = 0x0712;
  static const int COMMAND_FLOOD_PS = 0x0713;
  static const int COMMAND_STORE_FULL_PS_KEY = 0x0714;
  static const int COMMAND_SEND_DEBUG_MESSAGE = 0x0720;
  static const int COMMAND_SEND_APPLICATION_MESSAGE = 0x0721;
  static const int COMMAND_SEND_KALIMBA_MESSAGE = 0x0722;
  static const int COMMAND_GET_MEMORY_SLOTS = 0x0730;
  static const int COMMAND_GET_DEBUG_VARIABLE = 0x0740;
  static const int COMMAND_SET_DEBUG_VARIABLE = 0x0741;
  static const int COMMAND_DELETE_PDL = 0x0750;
  static const int COMMAND_SET_BLE_CONNECTION_PARAMETERS = 0x0752;
  static const int COMMANDS_IVOR_MASK = 0x1000;
  static const int COMMAND_IVOR_START = 0x1000;
  static const int COMMAND_IVOR_VOICE_DATA_REQUEST = 0x1001;
  static const int COMMAND_IVOR_VOICE_DATA = 0x1002;
  static const int COMMAND_IVOR_VOICE_END = 0x1003;
  static const int COMMAND_IVOR_CANCEL = 0x1004;
  static const int COMMAND_IVOR_CHECK_VERSION = 0x1005;
  static const int COMMAND_IVOR_ANSWER_START = 0x1006;
  static const int COMMAND_IVOR_ANSWER_END = 0x1007;
  static const int COMMAND_IVOR_PING = 0x10F0;
  static const int COMMANDS_NOTIFICATION_MASK = 0x4000;
  static const int COMMAND_REGISTER_NOTIFICATION = 0x4001;
  static const int COMMAND_GET_NOTIFICATION = 0x4081;
  static const int COMMAND_CANCEL_NOTIFICATION = 0x4002;
  static const int COMMAND_EVENT_NOTIFICATION = 0x4003;
  static int NOT_STATUS = -1;
  static int SUCCESS = 0;
  static int NOT_SUPPORTED = 1;
  static int NOT_AUTHENTICATED = 2;
  static int INSUFFICIENT_RESOURCES = 3;
  static int AUTHENTICATING = 4;
  static int INVALID_PARAMETER = 5;
  static int INCORRECT_STATE = 6;
  static int IN_PROGRESS = 7;
  static int NOT_NOTIFICATION = 0;
  static int RSSI_LOW_THRESHOLD = 0x01;
  static int RSSI_HIGH_THRESHOLD = 0x02;
  static int BATTERY_LOW_THRESHOLD = 0x03;
  static int BATTERY_HIGH_THRESHOLD = 0x04;
  static int DEVICE_STATE_CHANGED = 0x05;
  static int PIO_CHANGED = 0x06;
  static int DEBUG_MESSAGE = 0x07;
  static int BATTERY_CHARGED = 0x08;
  static int CHARGER_CONNECTION = 0x09;
  static int CAPSENSE_UPDATE = 0x0A;
  static int USER_ACTION = 0x0B;
  static int SPEECH_RECOGNITION = 0x0C;
  static int AV_COMMAND = 0x0D;
  static int REMOTE_BATTERY_LEVEL = 0x0E;
  static int KEY = 0x0F;
  static int DFU_STATE = 0x10;
  static int UART_RECEIVED_DATA = 0x11;
  static int VMU_PACKET = 0x12;
  static int HOST_NOTIFICATION =0x13;
  static int  BLE = 0;
  static int  BR_EDR = 1;
}

================
File: lib/gaia/OpCodes.dart
================
class OpCodes {
  static const UPGRADE_START_REQ = 0x01;
  static const UPGRADE_START_CFM = 0x02;
  static const UPGRADE_DATA_BYTES_REQ = 0x03;
  static const UPGRADE_DATA = 0x04;
  static const UPGRADE_SUSPEND_IND = 0x05;
  static const UPGRADE_RESUME_IND = 0x06;
  static const UPGRADE_ABORT_REQ = 0x07;
  static const UPGRADE_ABORT_CFM = 0x08;
  static const UPGRADE_PROGRESS_REQ = 0x09;
  static const UPGRADE_PROGRESS_CFM = 0x0A;
  static const UPGRADE_TRANSFER_COMPLETE_IND = 0x0B;
  static const UPGRADE_TRANSFER_COMPLETE_RES = 0x0C;
  static const UPGRADE_IN_PROGRESS_IND = 0x0D;
  static const UPGRADE_IN_PROGRESS_RES = 0x0E;
  static const UPGRADE_COMMIT_REQ = 0x0F;
  static const UPGRADE_COMMIT_CFM = 0x10;
  static const UPGRADE_ERROR_WARN_IND = 0x11;
  static const UPGRADE_COMPLETE_IND = 0x12;
  static const UPGRADE_SYNC_REQ = 0x13;
  static const UPGRADE_SYNC_CFM = 0x14;
  static const UPGRADE_START_DATA_REQ = 0x15;
  static const UPGRADE_IS_VALIDATION_DONE_REQ = 0x16;
  static const UPGRADE_IS_VALIDATION_DONE_CFM = 0x17;
  static const UPGRADE_SYNC_AFTER_REBOOT_REQ = 0x18;
  static const UPGRADE_VERSION_REQ = 0x19;
  static const UPGRADE_VERSION_CFM = 0x1A;
  static const UPGRADE_VARIANT_REQ = 0x1B;
  static const UPGRADE_VARIANT_CFM = 0x1C;
  static const UPGRADE_ERASE_SQIF_REQ = 0x1D;
  static const UPGRADE_ERASE_SQIF_CFM = 0x1E;
  static const UPGRADE_ERROR_WARN_RES = 0x1F;
   static const NB_BYTES_LENGTH = 4;
  static const NB_BYTES_OFFSET = 0;
  static const FILE_OFFSET_LENGTH = 4;
  static const FILE_OFFSET_OFFSET = NB_BYTES_OFFSET + NB_BYTES_LENGTH;
  static const DATA_LENGTH = FILE_OFFSET_LENGTH + NB_BYTES_LENGTH;
}

================
File: lib/gaia/ResumePoints.dart
================
class ResumePoints{
 static const int DATA_TRANSFER = 0x00;
 static const int VALIDATION = 0x01;
 static const int TRANSFER_COMPLETE = 0x02;
 static const int IN_PROGRESS = 0x03;
 static const int COMMIT = 0x04;
}

================
File: lib/gaia/UpgradeStartCFMStatus.dart
================
class UpgradeStartCFMStatus{
  static const int SUCCESS = 0x00;
  static const int ERROR_APP_NOT_READY = 0x09;
}

================
File: lib/model/base/topping_device_callback.dart
================
import 'package:topping_ble_control/utils/log_util.dart';
abstract class ToppingDeviceCallback {
  final Function(List<dynamic>) onScanResults;
  final Function(int) onScanFailed;
  final Function(int) onStateChange;
  final Function(int) onVerifyResult;
  final Function(bool) onPowerChange;
  final Function(String) onDeviceNameChange;
  final Function(int) onVolumeChange;
  final Function(bool) onMuteChange;
  final Function(int) onInputTypeChange;
  final Function(int) onOutputTypeChange;
  final Function(int) onDisplayModeChange;
  final Function(int) onThemeChange;
  final Function(int) onPowerTriggerChange;
  final Function(int) onBalanceChange;
  final Function(bool) onAudioBluetoothChange;
  final Function(bool) onBluetoothAptxChange;
  final Function(bool) onRemoteEnabledChange;
  final Function(int) onMultifunctionKeyChange;
  final Function(int) onUsbModeChange;
  final Function(int) onScreenBrightnessChange;
  final Function(int) onLanguageChange;
  final Function(int) onSamplingRateChange;
  ToppingDeviceCallback({
    required this.onScanResults,
    required this.onScanFailed,
    required this.onStateChange,
    required this.onVerifyResult,
    this.onPowerChange = _defaultOnPowerChange,
    this.onDeviceNameChange = _defaultOnDeviceNameChange,
    this.onVolumeChange = _defaultOnVolumeChange,
    this.onMuteChange = _defaultOnMuteChange,
    this.onInputTypeChange = _defaultOnInputTypeChange,
    this.onOutputTypeChange = _defaultOnOutputTypeChange,
    this.onDisplayModeChange = _defaultOnDisplayModeChange,
    this.onThemeChange = _defaultOnThemeChange,
    this.onPowerTriggerChange = _defaultOnPowerTriggerChange,
    this.onBalanceChange = _defaultOnBalanceChange,
    this.onAudioBluetoothChange = _defaultOnAudioBluetoothChange,
    this.onBluetoothAptxChange = _defaultOnBluetoothAptxChange,
    this.onRemoteEnabledChange = _defaultOnRemoteEnabledChange,
    this.onMultifunctionKeyChange = _defaultOnMultifunctionKeyChange,
    this.onUsbModeChange = _defaultOnUsbModeChange,
    this.onScreenBrightnessChange = _defaultOnScreenBrightnessChange,
    this.onLanguageChange = _defaultOnLanguageChange,
    this.onSamplingRateChange = _defaultOnSamplingRateChange,
  });
  static void _defaultOnPowerChange(bool isOn) => Log.e("未处理: 电源状态 $isOn");
  static void _defaultOnDeviceNameChange(String name) =>
      Log.e("未处理: 设备名称 $name");
  static void _defaultOnVolumeChange(int volume) => Log.e("未处理: 音量 $volume");
  static void _defaultOnMuteChange(bool isMute) => Log.e("未处理: 静音 $isMute");
  static void _defaultOnInputTypeChange(int inputType) =>
      Log.e("未处理: 输入类型 $inputType");
  static void _defaultOnOutputTypeChange(int outputType) =>
      Log.e("未处理: 输出类型 $outputType");
  static void _defaultOnDisplayModeChange(int mode) => Log.e("未处理: 显示模式 $mode");
  static void _defaultOnThemeChange(int theme) => Log.e("未处理: 主题 $theme");
  static void _defaultOnPowerTriggerChange(int trigger) =>
      Log.e("未处理: 电源触发 $trigger");
  static void _defaultOnBalanceChange(int balance) => Log.e("未处理: 平衡 $balance");
  static void _defaultOnAudioBluetoothChange(bool enabled) =>
      Log.e("未处理: 音频蓝牙 $enabled");
  static void _defaultOnBluetoothAptxChange(bool enabled) =>
      Log.e("未处理: AptX $enabled");
  static void _defaultOnRemoteEnabledChange(bool enabled) =>
      Log.e("未处理: 远程启用 $enabled");
  static void _defaultOnMultifunctionKeyChange(int key) =>
      Log.e("未处理: 多功能键 $key");
  static void _defaultOnUsbModeChange(int mode) => Log.e("未处理: USB模式 $mode");
  static void _defaultOnScreenBrightnessChange(int brightness) =>
      Log.e("未处理: 屏幕亮度 $brightness");
  static void _defaultOnLanguageChange(int language) =>
      Log.e("未处理: 语言 $language");
  static void _defaultOnSamplingRateChange(int rate) => Log.e("未处理: 采样率 $rate");
}

================
File: lib/model/base/topping_device_settings.dart
================
import 'dart:convert';
import 'dart:typed_data';
import 'package:topping_ble_control/utils/log_util.dart';
abstract class ToppingDeviceSettings {
  final bool isOn;
  final String deviceName;
  final int volume;
  final bool mute;
  final int inputType;
  final int outputType;
  final int displayMode;
  final int theme;
  final int powerTrigger;
  final int balance;
  final bool audioBluetooth;
  final bool bluetoothAptx;
  final bool remoteEnabled;
  final int multifunctionKey;
  final int usbMode;
  final int screenBrightness;
  final int language;
  final int sampling;
  ToppingDeviceSettings({
    required this.isOn,
    required this.deviceName,
    required this.volume,
    required this.mute,
    required this.inputType,
    required this.outputType,
    required this.displayMode,
    required this.theme,
    required this.powerTrigger,
    required this.balance,
    required this.audioBluetooth,
    required this.bluetoothAptx,
    required this.remoteEnabled,
    required this.multifunctionKey,
    required this.usbMode,
    required this.screenBrightness,
    required this.language,
    required this.sampling,
  });
  static String readDeviceNameFromFFI(Uint8List nameBytes) {
    try {
      int end = nameBytes.indexOf(0);
      if (end == -1) {
        end = nameBytes.length;
      }
      String name = utf8.decode(
        nameBytes.sublist(0, end),
        allowMalformed: true,
      );
      if (name.trim().isEmpty) {
        Log.w("从FFI读取的deviceName为空或无效");
        return "未知设备";
      }
      Log.d("读取deviceName: '$name'");
      return name;
    } catch (e) {
      Log.e("读取或解码FFI device_name时出错: $e");
      return "名称读取错误";
    }
  }
  ToppingDeviceSettings copy();
  @override
  String toString() {
    return '${runtimeType.toString()} {'
        'isOn: $isOn, '
        'deviceName: $deviceName, '
        'volume: $volume, '
        'mute: $mute, '
        'inputType: $inputType, '
        'outputType: $outputType, '
        'displayMode: $displayMode, '
        'theme: $theme, '
        'powerTrigger: $powerTrigger, '
        'balance: $balance, '
        'audioBluetooth: $audioBluetooth, '
        'bluetoothAptx: $bluetoothAptx, '
        'remoteEnabled: $remoteEnabled, '
        'multifunctionKey: $multifunctionKey, '
        'usbMode: $usbMode, '
        'screenBrightness: $screenBrightness, '
        'language: $language, '
        'sampling: $sampling'
        '}';
  }
}

================
File: lib/model/base/topping_device_state.dart
================
import 'package:topping_ble_control/model/base/topping_verify_result_type.dart';
import 'package:topping_ble_control/model/dx5ii/dx5ii_settings.dart';
class ToppingDeviceState {
  final bool? isPowerOn;
  final String? deviceName;
  final int? volume;
  final bool? isMute;
  final int? inputType;
  final int? outputType;
  final bool? headphoneEnabled;
  final int? headphoneGain;
  final int? displayMode;
  final int? theme;
  final int? powerTrigger;
  final int? balance;
  final int? filter;
  final int? decodeMode;
  final bool? audioBluetooth;
  final bool? bluetoothAptx;
  final bool? relay;
  final int? multifunctionKey;
  final int? usbMode;
  final int? screenBrightness;
  final int? language;
  final int? samplingRate;
  final Dx5iiSettings? settings;
  final ToppingVerifyResultType? verifyResult;
  final int? usbSelect;
  final bool? usbDsdEnabled;
  final int? iisPhase;
  final int? iisChannel;
  const ToppingDeviceState({
    this.isPowerOn,
    this.deviceName,
    this.volume,
    this.isMute,
    this.inputType,
    this.outputType,
    this.headphoneEnabled,
    this.headphoneGain,
    this.displayMode,
    this.theme,
    this.powerTrigger,
    this.balance,
    this.filter,
    this.decodeMode,
    this.audioBluetooth,
    this.bluetoothAptx,
    this.relay,
    this.multifunctionKey,
    this.usbMode,
    this.screenBrightness,
    this.language,
    this.samplingRate,
    this.settings,
    this.verifyResult,
    this.usbSelect,
    this.usbDsdEnabled,
    this.iisPhase,
    this.iisChannel,
  });
  factory ToppingDeviceState.empty() => const ToppingDeviceState();
  ToppingDeviceState copyWith({
    bool? isPowerOn,
    String? deviceName,
    int? volume,
    bool? isMute,
    int? inputType,
    int? outputType,
    bool? headphoneEnabled,
    int? headphoneGain,
    int? displayMode,
    int? theme,
    int? powerTrigger,
    int? balance,
    int? filter,
    int? decodeMode,
    bool? audioBluetooth,
    bool? bluetoothAptx,
    bool? relay,
    int? multifunctionKey,
    int? usbMode,
    int? screenBrightness,
    int? language,
    int? samplingRate,
    Dx5iiSettings? settings,
    ToppingVerifyResultType? verifyResult,
    int? usbSelect,
    bool? usbDsdEnabled,
    int? iisPhase,
    int? iisChannel,
  }) {
    return ToppingDeviceState(
      isPowerOn: isPowerOn ?? this.isPowerOn,
      deviceName: deviceName ?? this.deviceName,
      volume: volume ?? this.volume,
      isMute: isMute ?? this.isMute,
      inputType: inputType ?? this.inputType,
      outputType: outputType ?? this.outputType,
      headphoneEnabled: headphoneEnabled ?? this.headphoneEnabled,
      headphoneGain: headphoneGain ?? this.headphoneGain,
      displayMode: displayMode ?? this.displayMode,
      theme: theme ?? this.theme,
      powerTrigger: powerTrigger ?? this.powerTrigger,
      balance: balance ?? this.balance,
      filter: filter ?? this.filter,
      decodeMode: decodeMode ?? this.decodeMode,
      audioBluetooth: audioBluetooth ?? this.audioBluetooth,
      bluetoothAptx: bluetoothAptx ?? this.bluetoothAptx,
      relay: relay ?? this.relay,
      multifunctionKey: multifunctionKey ?? this.multifunctionKey,
      usbMode: usbMode ?? this.usbMode,
      screenBrightness: screenBrightness ?? this.screenBrightness,
      language: language ?? this.language,
      samplingRate: samplingRate ?? this.samplingRate,
      settings: settings ?? this.settings,
      verifyResult: verifyResult ?? this.verifyResult,
      usbSelect: usbSelect ?? this.usbSelect,
      usbDsdEnabled: usbDsdEnabled ?? this.usbDsdEnabled,
      iisPhase: iisPhase ?? this.iisPhase,
      iisChannel: iisChannel ?? this.iisChannel,
    );
  }
  @override
  String toString() {
    return 'ToppingDeviceState{'
        'isPowerOn: $isPowerOn, '
        'deviceName: $deviceName, '
        'volume: $volume, '
        'isMute: $isMute, '
        'samplingRate: $samplingRate'
        '}';
  }
}

================
File: lib/model/base/topping_verify_result_type.dart
================
enum ToppingVerifyResultType {
  success,
  failed,
  timeout,
  incompatible,
  unknown,
}
extension ToppingVerifyResultTypeExtension on ToppingVerifyResultType {
  static ToppingVerifyResultType fromValue(int value) {
    switch (value) {
      case 0:
        return ToppingVerifyResultType.success;
      case 1:
        return ToppingVerifyResultType.failed;
      case 2:
        return ToppingVerifyResultType.timeout;
      case 3:
        return ToppingVerifyResultType.incompatible;
      default:
        return ToppingVerifyResultType.unknown;
    }
  }
  int get value {
    switch (this) {
      case ToppingVerifyResultType.success:
        return 0;
      case ToppingVerifyResultType.failed:
        return 1;
      case ToppingVerifyResultType.timeout:
        return 2;
      case ToppingVerifyResultType.incompatible:
        return 3;
      case ToppingVerifyResultType.unknown:
        return 4;
    }
  }
}

================
File: lib/model/bluetooth/gatt_characteristic.dart
================
class GattCharacteristic {
  final String uuid;
  final int property;
  final List<int> value;
  GattCharacteristic({
    required this.uuid,
    required this.property,
    required this.value,
  });
  static const int PROPERTY_BROADCAST = 0x01;
  static const int PROPERTY_READ = 0x02;
  static const int PROPERTY_WRITE_NO_RESPONSE = 0x04;
  static const int PROPERTY_WRITE = 0x08;
  static const int PROPERTY_NOTIFY = 0x10;
  static const int PROPERTY_INDICATE = 0x20;
  static const int PROPERTY_SIGNED_WRITE = 0x40;
  static const int PROPERTY_EXTENDED_PROPS = 0x80;
}

================
File: lib/model/enums/ble_connection_state.dart
================
enum BleConnectionState {
  disconnected(0),
  connecting(1),
  connected(2),
  disconnecting(3),
  connectedUnsafe(4);
  final int value;
  const BleConnectionState(this.value);
  static BleConnectionState fromValue(int value) {
    switch (value) {
      case 0:
        return BleConnectionState.connectedUnsafe;
      case 1:
        return BleConnectionState.connected;
      case 2:
        return BleConnectionState.disconnected;
      default:
        return BleConnectionState.disconnected;
    }
  }
}

================
File: lib/model/ffi/ffi_ble_scanner_functions.dart
================
import 'dart:ffi';
base class FFIBleScannerFunctions extends Struct {
  external Pointer<NativeFunction<Int64 Function(Int64)>> init;
  external Pointer<NativeFunction<Void Function(Int64)>> uninit;
  external Pointer<NativeFunction<Void Function(Int64)>> start_scan;
  external Pointer<NativeFunction<Void Function(Int64)>> stop_scan;
}

================
File: lib/model/ffi/ffi_bluetooth_profile_state.dart
================
enum FfiBluetoothProfileState {
  stateConnected(0),
  stateDisconnected(1);
  final int value;
  const FfiBluetoothProfileState(this.value);
}

================
File: lib/model/ffi/ffi_connection_state.dart
================
enum FFIConnectionState {
  stateConnectedUnsafe(0),
  stateConnected(1),
  stateDisconnected(2);
  final int value;
  const FFIConnectionState(this.value);
}

================
File: lib/model/ffi/ffi_d900_device_callback.dart
================
import 'dart:ffi';
import 'package:ffi/ffi.dart';
import 'ffi_dx5ii_scan_result.dart';
import 'ffi_d900_settings.dart';
base class FFID900DeviceCallback extends Struct {
  external Pointer<
    NativeFunction<Void Function(Int64, Pointer<FFIDx5iiScanResult>, Size)>
  >
  on_scan_results;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_scan_failed;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_state_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_verify_result;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_power_change;
  external Pointer<NativeFunction<Void Function(Int64, Pointer<Utf8>)>>
  on_device_name_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_volume_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_mute_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_input_type_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_output_type_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_display_mode_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_theme_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_power_trigger_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_balance_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_audio_bluetooth;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_bluetooth_aptx;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_relay;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_multifunction_key_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_usb_mode_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_usb_select_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_usb_dsd;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_iis_phase_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_iis_channel_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_screen_brightness_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_language_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_sampling_rate_change;
  external Pointer<NativeFunction<Void Function(Int64)>>
  on_device_reset_settings;
  external Pointer<NativeFunction<Void Function(Int64)>>
  on_device_restore_factory_settings;
  external Pointer<
    NativeFunction<Void Function(Int64, Pointer<FFID900Settings>)>
  >
  on_device_settings_response;
}

================
File: lib/model/ffi/ffi_d900_settings.dart
================
import 'dart:ffi';
import 'package:ffi/ffi.dart';
final class FFID900Settings extends Struct {
  @Int32()
  external int is_on;
  @Int32()
  external int volume;
  @Int32()
  external int is_mute;
  @Int32()
  external int input_type;
  @Int32()
  external int output_type;
  @Int32()
  external int display_mode;
  @Int32()
  external int theme;
  @Int32()
  external int power_trigger;
  @Int32()
  external int usb_select;
  @Int32()
  external int balance;
  @Int32()
  external int audio_bt_enable;
  @Int32()
  external int aptx_enable;
  @Int32()
  external int remote_enable;
  @Int32()
  external int multifunction_key;
  @Int32()
  external int usb_dsd_enable;
  @Int32()
  external int usb_mode;
  @Int32()
  external int iis_phase;
  @Int32()
  external int iis_channel;
  @Int32()
  external int screen_brightness;
  @Int32()
  external int language;
  @Int32()
  external int sampling;
  @Array<Uint8>(32)
  external Array<Uint8> device_name;
}

================
File: lib/model/ffi/ffi_dx5ii_scan_result.dart
================
import 'dart:ffi';
base class FFIDx5iiScanResult extends Struct {
  external Pointer<Char> name;
  @Int64()
  external int device;
  @Int32()
  external int rssi;
}

================
File: lib/model/ffi/ffi_gatt_characteristic.dart
================
import 'dart:ffi';
base class FFIGattCharacteristic extends Struct {
  external Pointer<Char> uuid;
  @Int32()
  external int property;
  external Pointer<Uint8> value;
  @Int32()
  external int value_len;
}

================
File: lib/model/ffi/ffi_gatt_functions.dart
================
import 'dart:ffi';
import 'ffi_gatt_characteristic.dart';
base class FFIGattFunctions extends Struct {
  external Pointer<NativeFunction<Int64 Function(Int64)>> init;
  external Pointer<NativeFunction<Void Function(Int64)>> uninit;
  external Pointer<NativeFunction<Void Function(Int64)>> close;
  external Pointer<NativeFunction<Void Function(Int64)>> connect;
  external Pointer<NativeFunction<Void Function(Int64)>> disconnect;
  external Pointer<NativeFunction<Int32 Function(Int64, Int32)>> request_mtu;
  external Pointer<NativeFunction<Int32 Function(Int64, Pointer<FFIGattCharacteristic>)>> write_characteristic;
  external Pointer<NativeFunction<Int32 Function(Int64, Pointer<FFIGattCharacteristic>, Int32)>> set_characteristic_notification;
  external Pointer<NativeFunction<Int64 Function(Int64, Pointer<Char>)>> get_service;
}

================
File: lib/model/ffi/ffi_scan_callback_t.dart
================
import 'dart:ffi';
import 'ffi_scan_result.dart';
base class FFIScanCallback extends Struct {
  external Pointer<NativeFunction<Void Function(Int64, Pointer<FFIScanResult>, Size)>> on_batch_scan_results;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_scan_failed;
}

================
File: lib/model/ffi/ffi_scan_result.dart
================
import 'dart:ffi';
base class FFIScanResult extends Struct {
  external Pointer<Char> name;
  @Int64()
  external int device;
  @Int32()
  external int rssi;
  external Pointer<Uint8> manufacturer_data;
  @Int64()
  external int manufacturer_data_len;
}

================
File: lib/utils/ble_uuid_util.dart
================
class BleUuidUtil {
  static String normalizeUuid(String uuid) {
    String cleaned = uuid.replaceAll(RegExp(r'[^a-fA-F0-9]'), '').toLowerCase();
    if (cleaned.length <= 4) {
      cleaned = cleaned.padLeft(4, '0');
      return "0000$cleaned-0000-1000-8000-00805f9b34fb";
    }
    if (cleaned.length == 32) {
      return "${cleaned.substring(0, 8)}-${cleaned.substring(8, 12)}-${cleaned.substring(12, 16)}-${cleaned.substring(16, 20)}-${cleaned.substring(20)}";
    }
    return uuid.toLowerCase();
  }
  static bool uuidMatches(String uuid1, String uuid2) {
    return normalizeUuid(uuid1) == normalizeUuid(uuid2);
  }
}

================
File: lib/utils/constants.dart
================
class Constants {
  static const String apiBaseUrl = 'https://api.topping.com.cn';
  static const String firmware = 'firmware';
}

================
File: lib/utils/errors.dart
================
class BluetoothError extends Error {
  final String message;
  final int code;
  BluetoothError(this.message, {this.code = 0});
  @override
  String toString() => 'BluetoothError($code): $message';
}
class ConnectionError extends BluetoothError {
  ConnectionError(super.message, {super.code});
}
class ScanError extends BluetoothError {
  ScanError(super.message, {super.code});
}
class BleErrorHandler {
  static void handleError(dynamic error) {
    if (error is BluetoothError) {
      throw error;
    } else {
      throw BluetoothError(error.toString());
    }
  }
}

================
File: lib/utils/string_utils.dart
================
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
class StringUtils {
  final hexDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'];
  static String byteToString(List<int> list) {
    try {
      String string = const Utf8Decoder().convert(list);
      return string;
    } catch (e) {
      log("转换异常 $e");
    }
    return "";
  }
  static String byteToHexString(List<int> bytes) {
    const hexDigits = '0123456789ABCDEF';
    var charCodes = Uint8List(bytes.length * 2);
    for (var i = 0, j = 0; i < bytes.length; i++) {
      var byte = bytes[i];
      charCodes[j++] = hexDigits.codeUnitAt((byte >> 4) & 0xF);
      charCodes[j++] = hexDigits.codeUnitAt(byte & 0xF);
    }
    return String.fromCharCodes(charCodes);
  }
  static List<int> hexStringToBytes(String hexString) {
    if (hexString.length % 2 != 0) {
      hexString = "0" + hexString;
    }
    List<int> ret = [];
    for (int i = 0; i < hexString.length; i += 2) {
      var hex = hexString.substring(i, i + 2);
      ret.add(int.parse(hex, radix: 16));
    }
    return ret;
  }
  static String file2md5(List<int> input) {
    return md5.convert(input).toString();
  }
  static List<int> encode(String s) {
    return utf8.encode(s);
  }
  static int minToSecond(String s) {
    if (s.isEmpty || !s.contains(":")) return 0;
    return int.parse(s.split(":")[0]) * 60 + int.parse(s.split(":")[1]);
  }
  static int extractIntFromByteArray(List<int> source, int offset, int length, bool reverse) {
    if (length < 0 || length > 8) {
      return 0;
    }
    int result = 0;
    int shift = (length - 1) * 8;
    if (reverse) {
      for (int i = offset + length - 1; i >= offset; i--) {
        result |= ((source[i] & 0xFF) << shift);
        shift -= 8;
      }
    } else {
      for (int i = offset; i < offset + length; i++) {
        result |= ((source[i] & 0xFF) << shift);
        shift -= 8;
      }
    }
    return result;
  }
  static String intTo2HexString(int mVendorId) {
    var high = mVendorId >> 8 & 0xff;
    var low = mVendorId & 0xff;
    return byteToHexString([high, low]);
  }
  static List<int> intTo2List(int mVendorId) {
    var high = mVendorId >> 8 & 0xff;
    var low = mVendorId & 0xff;
    return [high, low];
  }
  static int byteListToInt(List<int> hex) {
    return hex[1] & 0xff | hex[0] << 8 & 0xff;
  }
}

================
File: lib/gaia/rwcp/RWCP.dart
================
class RWCP {
  static const int WINDOW_MAX = 32;
  static const int WINDOW_DEFAULT = 15;
  static const int SYN_TIMEOUT_MS = 1000;
  static const int RST_TIMEOUT_MS = 1000;
  static const int DATA_TIMEOUT_MS_DEFAULT = 100;
  static const int DATA_TIMEOUT_MS_MAX = 2000;
  static const int SEQUENCE_NUMBER_MAX = 63;
  static String getStateLabel(int state) {
    switch (state) {
      case RWCPState.CLOSING:
        return "CLOSING";
      case RWCPState.ESTABLISHED:
        return "ESTABLISHED";
      case RWCPState.LISTEN:
        return "LISTEN";
      case RWCPState.SYN_SENT:
        return "SYN_SENT";
      default:
        return "Unknown state ($state)";
    }
  }
}
class RWCPState {
  static const int LISTEN = 0;
  static const int SYN_SENT = 1;
  static const int ESTABLISHED = 2;
  static const int CLOSING = 3;
}
class RWCPOpCodeClient {
  static const int DATA = 0;
  static const int SYN = 1;
  static const int RST = 2;
  static const int RESERVED = 3;
}
class RWCPOpCodeServer {
  static const int DATA_ACK = 0;
  static const int SYN_ACK = 1;
  static const int RST = 2;
  static const int RST_ACK = 2;
  static const int GAP = 3;
}
class RWCPSegment {
  static const int HEADER_OFFSET = 0;
  static const int HEADER_LENGTH = 1;
  static const int PAYLOAD_OFFSET = HEADER_OFFSET + HEADER_LENGTH;
  static const int REQUIRED_INFORMATION_LENGTH = HEADER_LENGTH;
}
class SegmentHeader {
  static const int SEQUENCE_NUMBER_BIT_OFFSET = 0;
  static const int SEQUENCE_NUMBER_BITS_LENGTH = 6;
  static const int OPERATION_CODE_BIT_OFFSET = SEQUENCE_NUMBER_BIT_OFFSET + SEQUENCE_NUMBER_BITS_LENGTH;
  static const int OPERATION_CODE_BITS_LENGTH = 2;
}

================
File: lib/gaia/rwcp/RWCPClient.dart
================
import 'dart:async';
import 'dart:collection';
import '../../utils/log_util.dart';
import '../../utils/string_utils.dart';
import 'RWCP.dart';
import 'RWCPListener.dart';
import 'Segment.dart';
class RWCPClient {
  final String TAG = "RWCPClient";
  final RWCPListener mListener;
  int mLastAckSequence = 0;
  int mNextSequence = 0;
  int mInitialWindow = RWCP.WINDOW_DEFAULT;
  int mMaximumWindow = RWCP.WINDOW_MAX;
  int mWindow = RWCP.WINDOW_DEFAULT;
  int mCredits = RWCP.WINDOW_DEFAULT;
  bool mIsResendingSegments = false;
  int mState = RWCPState.LISTEN;
  var mPendingData = ListQueue<List<int>>();
  var mUnacknowledgedSegments = ListQueue<Segment>();
  bool isTimeOutRunning = false;
  int mDataTimeOutMs = RWCP.DATA_TIMEOUT_MS_DEFAULT;
  bool mShowDebugLogs = true;
  int mAcknowledgedSegments = 0;
  Timer? _timer;
  RWCPClient(this.mListener);
  bool isRunningASession() {
    return mState != RWCPState.LISTEN;
  }
  void showDebugLogs(bool show) {
    mShowDebugLogs = show;
    Log.i(TAG, "Debug logs are now " + (show ? "activated" : "deactivated") + ".");
  }
  bool sendData(List<int> bytes) {
    mPendingData.add(bytes);
    if (mState == RWCPState.LISTEN) {
      return startSession();
    } else if (mState == RWCPState.ESTABLISHED && !isTimeOutRunning) {
      sendDataSegment();
      return true;
    }
    return true;
  }
  void cancelTransfer() {
    logState("cancelTransfer");
    if (mState == RWCPState.LISTEN) {
      Log.i(TAG, "cancelTransfer: no ongoing transfer to cancel.");
      return;
    }
    reset(true);
    if (!sendRSTSegment()) {
      Log.w(TAG, "Sending of RST segment has failed, terminating session.");
      terminateSession();
    }
  }
  bool onReceiveRWCPSegment(List<int>? bytes) {
    if (bytes == null) {
      Log.w(TAG, "onReceiveRWCPSegment called with a null bytes array.");
      return false;
    }
    if (bytes.length < RWCPSegment.REQUIRED_INFORMATION_LENGTH) {
      String message =
          "Analyse of RWCP Segment failed: the byte array does not contain the minimum " + "required information.";
      if (mShowDebugLogs) {
        message += "\n\tbytes=${StringUtils.byteToHexString(bytes)}";
      }
      Log.w(TAG, message);
      return false;
    }
    Segment segment = Segment.parse(bytes);
    int code = segment.getOperationCode();
    if (code == -1) {
      Log.w(
          TAG,
          "onReceivedRWCPSegment failed to get a RWCP segment from given bytes: $code data->" +
              StringUtils.byteToHexString(bytes));
      return false;
    }
    Log.d(TAG, "onReceiveRWCPSegment code$code");
    switch (code) {
      case RWCPOpCodeServer.SYN_ACK:
        return receiveSynAck(segment);
      case RWCPOpCodeServer.DATA_ACK:
        return receiveDataAck(segment);
      case RWCPOpCodeServer.RST:
        return receiveRST(segment);
      case RWCPOpCodeServer.GAP:
        return receiveGAP(segment);
      default:
        Log.w(TAG, "Received unknown operation code: $code");
        return false;
    }
  }
  int getInitialWindowSize() {
    return mInitialWindow;
  }
  bool setInitialWindowSize(int size) {
    logState("set initial window size to $size");
    if (mState != RWCPState.LISTEN) {
      Log.w(TAG, "FAIL to set initial window size to $size: not possible when there is an ongoing " + "session.");
      return false;
    }
    if (size <= 0 || size > mMaximumWindow) {
      Log.w(TAG, "FAIL to set initial window to $size: size is out of range.");
      return false;
    }
    mInitialWindow = size;
    mWindow = mInitialWindow;
    return true;
  }
  int getMaximumWindowSize() {
    return mMaximumWindow;
  }
  bool setMaximumWindowSize(int size) {
    logState("set maximum window size to $size");
    if (mState != RWCPState.LISTEN) {
      Log.w(TAG, "FAIL to set maximum window size to $size: not possible when there is an ongoing " + "session.");
      return false;
    }
    if (size <= 0 || size > RWCP.WINDOW_MAX) {
      Log.w(TAG, "FAIL to set maximum window to $size: size is out of range.");
      return false;
    }
    if (mInitialWindow > mMaximumWindow) {
      Log.w(TAG, "FAIL to set maximum window to $size: initial window is $mInitialWindow.");
      return false;
    }
    mMaximumWindow = size;
    if (mWindow > mMaximumWindow) {
      Log.i(TAG, "window is updated to be less than the maximum window size ( $mInitialWindow).");
      mWindow = mMaximumWindow;
    }
    return true;
  }
  bool receiveRST(Segment segment) {
    if (mShowDebugLogs) {
      Log.d(TAG, "Receive RST or RST_ACK for sequence ${segment.getSequenceNumber()}");
    }
    switch (mState) {
      case RWCPState.SYN_SENT:
        Log.i(TAG, "Received RST (sequence ${segment.getSequenceNumber()}) in SYN_SENT state, ignoring " + "segment.");
        return true;
      case RWCPState.ESTABLISHED:
        Log.w(
            TAG,
            "Received RST (sequence ${segment.getSequenceNumber()}) in ESTABLISHED state, " +
                "terminating session, transfer failed.");
        terminateSession();
        mListener.onTransferFailed();
        return true;
      case RWCPState.CLOSING:
        cancelTimeOut();
        validateAckSequence(RWCPOpCodeClient.RST, segment.getSequenceNumber());
        reset(false);
        if (mPendingData.isNotEmpty) {
          if (!sendSYNSegment()) {
            Log.w(TAG, "Start session of RWCP data transfer failed: sending of SYN failed.");
            terminateSession();
            mListener.onTransferFailed();
          }
        } else {
          mListener.onTransferFinished();
        }
        return true;
      case RWCPState.LISTEN:
      default:
        Log.w(
            TAG,
            "Received unexpected RST segment with sequence=${segment.getSequenceNumber()}" +
                " while in state " +
                RWCP.getStateLabel(mState));
        return false;
    }
  }
  bool sendSYNSegment() {
    bool done = false;
    mState = RWCPState.SYN_SENT;
    Segment segment = Segment.get(RWCPOpCodeClient.SYN, mNextSequence);
    done = sendSegment(segment, RWCP.SYN_TIMEOUT_MS);
    if (done) {
      mUnacknowledgedSegments.add(segment);
      mNextSequence = increaseSequenceNumber(mNextSequence);
      mCredits--;
      logState("send SYN segment");
    }
    return done;
  }
  void logState(String label) {
    if (mShowDebugLogs) {
      String message = label +
          "\t\t\tstate=" +
          RWCP.getStateLabel(mState) +
          "\n\tWindow: \tcurrent = " +
          "$mWindow" +
          " \t\tdefault = " +
          "$mInitialWindow" +
          " \t\tcredits = " +
          "$mCredits" +
          "\n\tSequence: \tlast = " +
          "$mLastAckSequence" +
          " \t\tnext = " +
          "$mNextSequence" +
          "\n\tPending: \tPSegments = " +
          "${mUnacknowledgedSegments.length}" +
          " \t\tPData = " +
          "${mPendingData.length}";
      Log.d(TAG, message);
    }
  }
  bool startSession() {
    logState("startSession");
    if (mState != RWCPState.LISTEN) {
      Log.w(TAG, "Start RWCP session failed: already an ongoing session.");
      return false;
    }
    if (sendRSTSegment()) {
      return true;
    } else {
      Log.w(TAG, "Start RWCP session failed: sending of RST segment failed.");
      terminateSession();
      return false;
    }
  }
  void terminateSession() {
    logState("terminateSession");
    reset(true);
  }
  bool sendRSTSegment() {
    if (mState == RWCPState.CLOSING) {
      return true;
    }
    bool done = false;
    reset(false);
    mState = RWCPState.CLOSING;
    Segment segment = Segment.get(RWCPOpCodeClient.RST, mNextSequence);
    done = sendSegment(segment, RWCP.RST_TIMEOUT_MS);
    if (done) {
      mUnacknowledgedSegments.add(segment);
      mNextSequence = increaseSequenceNumber(mNextSequence);
      mCredits--;
      logState("send RST segment");
    }
    return done;
  }
  bool sendSegment(Segment segment, int timeout) {
    List<int> bytes = segment.getBytes();
    if (mListener.sendRWCPSegment(bytes)) {
      startTimeOut(timeout);
      return true;
    }
    return false;
  }
  void startTimeOut(int delay) {
    if (isTimeOutRunning) {
      _timer?.cancel();
    }
    isTimeOutRunning = true;
    _timer = Timer(Duration(milliseconds: delay), () {
      onTimeOut();
    });
  }
  void onTimeOut() {
    if (isTimeOutRunning) {
      isTimeOutRunning = false;
      mIsResendingSegments = true;
      mAcknowledgedSegments = 0;
      if (mShowDebugLogs) {
        Log.i(TAG, "TIME OUT > re sending segments");
      }
      if (mState == RWCPState.ESTABLISHED) {
        mDataTimeOutMs *= 2;
        if (mDataTimeOutMs > RWCP.DATA_TIMEOUT_MS_MAX) {
          mDataTimeOutMs = RWCP.DATA_TIMEOUT_MS_MAX;
        }
        resendDataSegment();
      } else {
        resendSegment();
      }
    }
  }
  void resendSegment() {
    if (mState == RWCPState.ESTABLISHED) {
      Log.w(TAG, "Trying to resend non data segment while in ESTABLISHED state.");
      return;
    }
    mIsResendingSegments = true;
    mCredits = mWindow;
    for (Segment segment in mUnacknowledgedSegments) {
      int delay = (segment.getOperationCode() == RWCPOpCodeClient.SYN)
          ? RWCP.SYN_TIMEOUT_MS
          : (segment.getOperationCode() == RWCPOpCodeClient.RST)
              ? RWCP.RST_TIMEOUT_MS
              : mDataTimeOutMs;
      sendSegment(segment, delay);
      mCredits--;
    }
    logState("resend segments");
    mIsResendingSegments = false;
  }
  void resendDataSegment() {
    if (mState != RWCPState.ESTABLISHED) {
      Log.w(TAG, "Trying to resend data segment while not in ESTABLISHED state.");
      return;
    }
    mIsResendingSegments = true;
    mCredits = mWindow;
    logState("reset credits");
    int moved = 0;
    while (mUnacknowledgedSegments.length > mCredits) {
      Segment segment = mUnacknowledgedSegments.last;
      if (segment.getOperationCode() == RWCPOpCodeClient.DATA) {
        mUnacknowledgedSegments.removeLast();
        mPendingData.addFirst(segment.getPayload());
        moved++;
      } else {
        Log.w(TAG, "Segment " + segment.toString() + " in pending segments but not a DATA segment.");
        break;
      }
    }
    mNextSequence = decreaseSequenceNumber(mNextSequence, moved);
    for (var segment in mUnacknowledgedSegments) {
      sendSegment(segment, mDataTimeOutMs);
      mCredits--;
    }
    logState("Resend DATA segments");
    mIsResendingSegments = false;
    if (mCredits > 0) {
      sendDataSegment();
    }
  }
  void sendDataSegment() {
    while (mCredits > 0 && mPendingData.isNotEmpty && !mIsResendingSegments && mState == RWCPState.ESTABLISHED) {
      List<int> data = mPendingData.removeFirst();
      Segment segment = Segment.get(RWCPOpCodeClient.DATA, mNextSequence, payload: data);
      sendSegment(segment, mDataTimeOutMs);
      mUnacknowledgedSegments.add(segment);
      mNextSequence = increaseSequenceNumber(mNextSequence);
      mCredits--;
    }
    logState("send DATA segments");
  }
  int increaseSequenceNumber(int sequence) {
    return (sequence + 1) % (RWCP.SEQUENCE_NUMBER_MAX + 1);
  }
  int decreaseSequenceNumber(int sequence, int decrease) {
    return (sequence - decrease + RWCP.SEQUENCE_NUMBER_MAX + 1) % (RWCP.SEQUENCE_NUMBER_MAX + 1);
  }
  void reset(bool complete) {
    mLastAckSequence = -1;
    mNextSequence = 0;
    mState = RWCPState.LISTEN;
    mUnacknowledgedSegments.clear();
    mWindow = mInitialWindow;
    mAcknowledgedSegments = 0;
    mCredits = mWindow;
    cancelTimeOut();
    if (complete) {
      mPendingData.clear();
    }
    logState("reset");
  }
  void cancelTimeOut() {
    if (isTimeOutRunning) {
      _timer?.cancel();
      isTimeOutRunning = false;
    }
  }
  bool receiveSynAck(Segment segment) {
    if (mShowDebugLogs) {
      Log.d(TAG, "Receive SYN_ACK for sequence ${segment.getSequenceNumber()}");
    }
    switch (mState) {
      case RWCPState.SYN_SENT:
        cancelTimeOut();
        int validated = validateAckSequence(RWCPOpCodeClient.SYN, segment.getSequenceNumber());
        if (validated >= 0) {
          mState = RWCPState.ESTABLISHED;
          if (mPendingData.isNotEmpty) {
            sendDataSegment();
          }
        } else {
          Log.w(TAG, "Receive SYN_ACK with unexpected sequence number: ${segment.getSequenceNumber()}");
          terminateSession();
          mListener.onTransferFailed();
          sendRSTSegment();
        }
        return true;
      case RWCPState.ESTABLISHED:
        cancelTimeOut();
        if (mUnacknowledgedSegments.isNotEmpty) {
          resendDataSegment();
        }
        return true;
      case RWCPState.CLOSING:
      case RWCPState.LISTEN:
      default:
        Log.w(
            TAG,
            "Received unexpected SYN_ACK segment with header " +
                "${segment.getHeader()}" +
                " while in state " +
                RWCP.getStateLabel(mState));
        return false;
    }
  }
  int validateAckSequence(final int code, final int sequence) {
    final int NOT_VALIDATED = -1;
    if (sequence < 0) {
      Log.w(TAG, "Received ACK sequence ($sequence) is less than 0.");
      return NOT_VALIDATED;
    }
    if (sequence > RWCP.SEQUENCE_NUMBER_MAX) {
      Log.w(
          TAG,
          "Received ACK sequence ($sequence) is bigger than its maximum value (" +
              "${RWCP.SEQUENCE_NUMBER_MAX}" +
              ").");
      return NOT_VALIDATED;
    }
    if (mLastAckSequence < mNextSequence && (sequence < mLastAckSequence || sequence > mNextSequence)) {
      Log.w(
          TAG,
          "Received ACK sequence ($sequence) is out of interval: last received is " +
              "$mLastAckSequence" +
              " and next will be " +
              "$mNextSequence");
      return NOT_VALIDATED;
    }
    if (mLastAckSequence > mNextSequence && sequence < mLastAckSequence && sequence > mNextSequence) {
      Log.w(
          TAG,
          "Received ACK sequence ($sequence) is out of interval: last received is " +
              "$mLastAckSequence" +
              " and next will be " +
              "$mNextSequence");
      return NOT_VALIDATED;
    }
    int acknowledged = 0;
    int nextAckSequence = mLastAckSequence;
    while (nextAckSequence != sequence) {
      nextAckSequence = increaseSequenceNumber(nextAckSequence);
      if (removeSegmentFromQueue(code, nextAckSequence)) {
        mLastAckSequence = nextAckSequence;
        if (mCredits < mWindow) {
          mCredits++;
        }
        acknowledged++;
      } else {
        Log.w(TAG,
            "Error validating sequence " + "$nextAckSequence" + ": no corresponding segment in " + "pending segments.");
      }
    }
    logState("$acknowledged" + " segment(s) validated with ACK sequence(code=$code seq=$sequence");
    increaseWindow(acknowledged);
    return acknowledged;
  }
  bool removeSegmentFromQueue(int code, int sequence) {
    for (Segment segment in mUnacknowledgedSegments) {
      if (segment.getOperationCode() == code && segment.getSequenceNumber() == sequence) {
        mUnacknowledgedSegments.remove(segment);
        return true;
      }
    }
    Log.w(TAG, "Pending segments does not contain acknowledged segment: code=$code \tsequence=$sequence");
    return false;
  }
  void increaseWindow(int acknowledged) {
    mAcknowledgedSegments += acknowledged;
    if (mAcknowledgedSegments > mWindow && mWindow < mMaximumWindow) {
      mAcknowledgedSegments = 0;
      mWindow++;
      mCredits++;
      logState("increase window to $mWindow");
    }
  }
  bool receiveDataAck(Segment segment) {
    if (mShowDebugLogs) {
      Log.d(TAG, "Receive DATA_ACK for sequence ${segment.getSequenceNumber()}");
    }
    switch (mState) {
      case RWCPState.ESTABLISHED:
        cancelTimeOut();
        int sequence = segment.getSequenceNumber();
        int validated = validateAckSequence(RWCPOpCodeClient.DATA, sequence);
        if (validated >= 0) {
          if (mCredits > 0 && !mPendingData.isEmpty) {
            sendDataSegment();
          } else if (mPendingData.isEmpty && mUnacknowledgedSegments.isEmpty) {
            sendRSTSegment();
          } else if (mPendingData.isEmpty
              ||
              mCredits == 0 ) {
            startTimeOut(mDataTimeOutMs);
          }
          mListener.onTransferProgress(validated);
        }
        return true;
      case RWCPState.CLOSING:
        if (mShowDebugLogs) {
          Log.i(TAG,
              "Received DATA_ACK(${segment.getSequenceNumber()}) segment while in state CLOSING: segment discarded.");
        }
        return true;
      case RWCPState.SYN_SENT:
      case RWCPState.LISTEN:
      default:
        Log.w(
            TAG,
            "Received unexpected DATA_ACK segment with sequence ${segment.getSequenceNumber()}" +
                " while in state " +
                RWCP.getStateLabel(mState));
        return false;
    }
  }
  bool receiveGAP(Segment segment) {
    if (mShowDebugLogs) {
      Log.d(TAG, "Receive GAP for sequence ${segment.getSequenceNumber()}");
    }
    switch (mState) {
      case RWCPState.ESTABLISHED:
        if (mLastAckSequence > segment.getSequenceNumber()) {
          Log.i(TAG, "Ignoring GAP (${segment.getSequenceNumber()}) as last ack sequence is $mLastAckSequence.");
          return true;
        }
        if (mLastAckSequence <= segment.getSequenceNumber()) {
          decreaseWindow();
          validateAckSequence(RWCPOpCodeClient.DATA, segment.getSequenceNumber());
        }
        cancelTimeOut();
        resendDataSegment();
        return true;
      case RWCPState.CLOSING:
        if (mShowDebugLogs) {
          Log.i(TAG, "Received GAP(${segment.getSequenceNumber()}) segment while in state CLOSING: segment discarded.");
        }
        return true;
      case RWCPState.SYN_SENT:
      case RWCPState.LISTEN:
      default:
        Log.w(
            TAG,
            "Received unexpected GAP segment with header ${segment.getHeader()} while in state " +
                RWCP.getStateLabel(mState));
        return false;
    }
  }
  void decreaseWindow() {
    mWindow = ((mWindow - 1) ~/ 2) + 1;
    if (mWindow > mMaximumWindow || mWindow < 1) {
      mWindow = 1;
    }
    mAcknowledgedSegments = 0;
    mCredits = mWindow;
    logState("decrease window to $mWindow");
  }
}

================
File: lib/gaia/rwcp/Segment.dart
================
import '../../utils/string_utils.dart';
import 'RWCP.dart';
class Segment {
  final String TAG = "Segment";
  int mOperationCode = -1;
  int mSequenceNumber = -1;
  int mHeader = 0;
  List<int>? mPayload;
  List<int>? mBytes;
  static Segment get(int operationCode, int sequenceNumber, {List<int>? payload}) {
    final seg = Segment();
    seg.mOperationCode = operationCode;
    seg.mSequenceNumber = sequenceNumber;
    seg.mPayload = payload ?? [];
    seg.mHeader = (operationCode << SegmentHeader.SEQUENCE_NUMBER_BITS_LENGTH) | sequenceNumber;
    return seg;
  }
  static Segment parse(List<int>? bytes) {
    int mOperationCode = -1;
    int mSequenceNumber = -1;
    int mHeader = -1;
    List<int> mPayload = [];
    if (bytes == null || bytes.length < RWCPSegment.REQUIRED_INFORMATION_LENGTH) {
      mOperationCode = -1;
      mSequenceNumber = -1;
      mHeader = -1;
      mPayload = bytes ?? [];
    } else {
      mHeader = bytes[RWCPSegment.HEADER_OFFSET];
      mOperationCode =
          getBits(mHeader, SegmentHeader.OPERATION_CODE_BIT_OFFSET, SegmentHeader.OPERATION_CODE_BITS_LENGTH);
      mSequenceNumber =
          getBits(mHeader, SegmentHeader.SEQUENCE_NUMBER_BIT_OFFSET, SegmentHeader.SEQUENCE_NUMBER_BITS_LENGTH);
      mPayload = bytes.sublist(1);
    }
    final seg = Segment();
    seg.mBytes = bytes;
    seg.mOperationCode = mOperationCode;
    seg.mSequenceNumber = mSequenceNumber;
    seg.mPayload = mPayload;
    seg.mHeader = mHeader;
    return seg;
  }
  List<int> getBytes() {
    if (mBytes == null) {
      mBytes = [];
      int payloadLength = (mPayload == null) ? 0 : mPayload?.length ?? 0;
      mBytes?.add(mHeader);
      if (payloadLength > 0) {
        mBytes?.addAll(mPayload ?? []);
      }
    }
    return mBytes ?? [];
  }
  static int getBits(int value, int offset, int length) {
    int mask = ((1 << length) - 1) << offset;
    return (value & mask) >>> offset;
  }
  @override
  String toString() {
    var res = "";
    res += "mOperationCode $mOperationCode";
    res += "mSequenceNumber $mSequenceNumber";
    res += "mPayload ${StringUtils.byteToHexString(mPayload ?? [])}";
    return res;
  }
  int getOperationCode() {
    return mOperationCode;
  }
  List<int> getPayload() {
    return mPayload ?? [];
  }
  int getSequenceNumber() {
    return mSequenceNumber;
  }
  int getHeader() {
    return mHeader;
  }
}

================
File: lib/gaia/GaiaPacketBLE.dart
================
import 'package:flutter/material.dart';
import '../utils/string_utils.dart';
import 'GAIA.dart';
class GaiaPacketBLE {
  int mVendorId = GAIA.VENDOR_QUALCOMM;
  int mCommandId = 0;
  int getCommand() {
    return mCommandId & GAIA.COMMAND_MASK;
  }
  List<int>? mPayload;
  List<int>? mBytes;
  GaiaPacketBLE(this.mCommandId, {this.mPayload});
  int getStatus() {
    final int STATUS_OFFSET = 0;
    final int STATUS_LENGTH = 1;
    if (!isAcknowledgement() || mPayload == null || (mPayload ?? []).length < STATUS_LENGTH) {
      return GAIA.NOT_STATUS;
    } else {
      return (mPayload ?? [0])[STATUS_OFFSET];
    }
  }
  bool isAcknowledgement() {
    return (mCommandId & GAIA.ACKNOWLEDGMENT_MASK) > 0;
  }
  int getEvent() {
    final int EVENT_OFFSET = 0;
    final int EVENT_LENGTH = 1;
    if ((mCommandId & GAIA.COMMANDS_NOTIFICATION_MASK) < 1 ||
        mPayload == null ||
        (mPayload?.length ?? 0) < EVENT_LENGTH) {
      return GAIA.NOT_NOTIFICATION;
    } else {
      return (mPayload ?? [0])[EVENT_OFFSET];
    }
  }
  List<int> getBytes() {
    if (mBytes != null) {
      return mBytes ?? [];
    } else {
      mBytes = buildBytes(mCommandId, mPayload);
      return mBytes ?? [];
    }
  }
  static GaiaPacketBLE buildGaiaNotificationPacket(int commandID, int event, List<int>? data, int type) {
    List<int> payload = [];
    payload.add(event);
    if (data != null && data.isNotEmpty) {
      payload.addAll(data);
    }
    return GaiaPacketBLE(commandID, mPayload: payload);
  }
  static final int MAX_PAYLOAD = 16;
  static final int OFFSET_VENDOR_ID = 0;
  static final int LENGTH_VENDOR_ID = 2;
  static final int OFFSET_COMMAND_ID = 2;
  static final int LENGTH_COMMAND_ID = 2;
  static final int OFFSET_PAYLOAD = 4;
  static final int PACKET_INFORMATION_LENGTH = LENGTH_COMMAND_ID + LENGTH_VENDOR_ID;
  static final int MIN_PACKET_LENGTH = PACKET_INFORMATION_LENGTH;
  static GaiaPacketBLE? fromByte(List<int> source) {
    int payloadLength = source.length - PACKET_INFORMATION_LENGTH;
    if (payloadLength < 0) {
      debugPrint("GaiaPacketBLE fromByte error");
      return null;
    }
    int mVendorId = StringUtils.extractIntFromByteArray(source, OFFSET_VENDOR_ID, LENGTH_VENDOR_ID, false);
    int mCommandId = StringUtils.extractIntFromByteArray(source, OFFSET_COMMAND_ID, LENGTH_COMMAND_ID, false);
    var mCommandIdStr = StringUtils.intTo2HexString(mCommandId);
    debugPrint(
        "GaiaPacketBLE ${StringUtils.byteToHexString(source)} vendorId $mVendorId payloadLength$payloadLength mCommandId$mCommandId mCommandIdStr $mCommandIdStr");
    List<int> mPayload = [];
    if (payloadLength > 0) {
      mPayload.addAll(source.sublist(PACKET_INFORMATION_LENGTH));
    }
    GaiaPacketBLE gaiaPacketBLE = GaiaPacketBLE(mCommandId, mPayload: mPayload);
    gaiaPacketBLE.mBytes = source;
    return gaiaPacketBLE;
  }
  List<int> buildBytes(int commandId, List<int>? payload) {
    List<int> bytes = [];
    bytes.addAll(StringUtils.intTo2List(mVendorId));
    bytes.addAll(StringUtils.intTo2List(mCommandId));
    if (payload != null) {
      bytes.addAll(payload);
    }
    return bytes;
  }
  int getCommandId() {
    return mCommandId;
  }
}

================
File: lib/gaia/VMUPacket.dart
================
import 'package:flutter/cupertino.dart';
import '../utils/string_utils.dart';
class VMUPacket {
  final String TAG = "VMUPacket";
  static final int LENGTH_LENGTH = 2;
  static final int OPCODE_LENGTH = 1;
  static final int OPCODE_OFFSET = 0;
  static final int LENGTH_OFFSET = OPCODE_OFFSET + OPCODE_LENGTH;
  static final int DATA_OFFSET = LENGTH_OFFSET + LENGTH_LENGTH;
  int mOpCode = -1;
  List<int>? mData;
  static final int REQUIRED_INFORMATION_LENGTH = LENGTH_LENGTH + OPCODE_LENGTH;
  static VMUPacket get(int opCode, {List<int>? data}) {
    VMUPacket vmuPacket = VMUPacket();
    vmuPacket.mOpCode = opCode;
    if (data != null) {
      vmuPacket.mData = data;
    }
    return vmuPacket;
  }
  static VMUPacket? getPackageFromByte(List<int> bytes) {
    int opCode = -1;
    if (bytes.length >= REQUIRED_INFORMATION_LENGTH) {
      opCode = bytes[0];
      int length = StringUtils.byteListToInt([bytes[1], bytes[2]]);
      int dataLength = bytes.length - REQUIRED_INFORMATION_LENGTH;
      debugPrint("$length getPackageFromByte $dataLength");
      if (length > dataLength) {
        debugPrint("getPackageFromByte length > dataLength");
      } else if (length < dataLength) {
        debugPrint("getPackageFromByte length < dataLength");
      }
      List<int> data = bytes.sublist(3);
      return VMUPacket.get(opCode, data: data);
    }
    return null;
  }
  List<int> getBytes() {
    List<int> packet = [];
    packet.add(mOpCode);
    packet.addAll(StringUtils.intTo2List((mData ?? []).length));
    packet.addAll(mData ?? []);
    return packet;
  }
}

================
File: lib/model/dx5ii/dx5ii_verify_result_type.dart
================
import '../base/topping_verify_result_type.dart';
typedef Dx5iiVerifyResultType = ToppingVerifyResultType;
class Dx5iiVerifyResult {
  static ToppingVerifyResultType fromValue(int value) {
    switch (value) {
      case 0:
        return ToppingVerifyResultType.success;
      case 1:
        return ToppingVerifyResultType.failed;
      case 2:
        return ToppingVerifyResultType.timeout;
      default:
        return ToppingVerifyResultType.unknown;
    }
  }
}

================
File: lib/model/ffi/ffi_dx5ii_device_callback.dart
================
import 'dart:ffi';
import 'package:ffi/ffi.dart';
import 'ffi_dx5ii_scan_result.dart';
import 'ffi_dx5ii_settings.dart';
base class FFIDx5iiDeviceCallback extends Struct {
  external Pointer<
    NativeFunction<Void Function(Int64, Pointer<FFIDx5iiScanResult>, Size)>
  >
  on_scan_results;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_scan_failed;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_state_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_verify_result;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_power_change;
  external Pointer<NativeFunction<Void Function(Int64, Pointer<Utf8>)>>
  on_device_name_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_volume_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_mute_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_input_type_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_output_type_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_headphone;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_headphone_gain_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_display_mode_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_theme_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_power_trigger_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_balance_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_filter_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_decode_mode_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_audio_bluetooth;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_bluetooth_aptx;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_relay;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_multifunction_key_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_usb_mode_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_screen_brightness_change;
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_language_change;
  external Pointer<NativeFunction<Void Function(Int64)>>
  on_device_reset_settings;
  external Pointer<NativeFunction<Void Function(Int64)>>
  on_device_restore_factory_settings;
  external Pointer<
    NativeFunction<Void Function(Int64, Pointer<FFIDx5iiSettings>)>
  >
  on_device_settings_response;
}

================
File: lib/model/ffi/ffi_dx5ii_settings.dart
================
import 'dart:ffi';
base class FFIDx5iiSettings extends Struct {
  @Int32()
  external int is_on;
  @Array(32)
  external Array<Uint8> device_name;
  @Int32()
  external int volume;
  @Int32()
  external int is_mute;
  @Int32()
  external int input_type;
  @Int32()
  external int output_type;
  @Int32()
  external int headphone_enable;
  @Int32()
  external int headphone_gain;
  @Int32()
  external int display_mode;
  @Int32()
  external int theme;
  @Int32()
  external int power_trigger;
  @Int32()
  external int balance;
  @Int32()
  external int pcm_filter;
  @Int32()
  external int decode_mode;
  @Int32()
  external int audio_bt_enable;
  @Int32()
  external int aptx_enable;
  @Int32()
  external int remote_enable;
  @Int32()
  external int multifunction_key;
  @Int32()
  external int usb_mode;
  @Int32()
  external int screen_brightness;
  @Int32()
  external int language;
  @Int32()
  external int sampling;
}

================
File: lib/registry/current_connecting_device.dart
================
class CurrentConnectingDevice {
  static final CurrentConnectingDevice _instance =
      CurrentConnectingDevice._internal();
  factory CurrentConnectingDevice() => _instance;
  CurrentConnectingDevice._internal();
  int? handle;
}

================
File: lib/registry/device_data_manager.dart
================
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../model/bluetooth/ble_device.dart';
import '../model/enums/device_mode_type.dart';
import '../utils/log_util.dart';
class DeviceDataManager {
  static final DeviceDataManager _instance = DeviceDataManager._internal();
  factory DeviceDataManager() => _instance;
  final Map<int, BleDevice> _devicesById = {};
  final Map<int, BleDevice> _devicesByHandle = {};
  final Map<String, BleDevice> _devicesByName = {};
  final Map<String, BleDevice> _devicesByMac = {};
  DeviceDataManager._internal();
  void registerDevice(BleDevice device) {
    BleDevice? existingDevice = getDeviceByHandle(device.nativeHandle);
    if (existingDevice != null) {
      if (existingDevice.flutterDevice == null && device.flutterDevice != null) {
        Log.i("更新设备 ${device.name} 的 Flutter 设备对象");
        existingDevice.flutterDevice = device.flutterDevice;
        final macAddress = device.flutterDevice!.remoteId.str;
        _devicesByMac[macAddress] = existingDevice;
      }
      existingDevice.rssi = device.rssi;
      return;
    }
    _devicesById[device.id] = device;
    _devicesByHandle[device.nativeHandle] = device;
    _devicesByName[device.name] = device;
    if (device.flutterDevice != null) {
      final macAddress = device.flutterDevice!.remoteId.str;
      _devicesByMac[macAddress] = device;
      Log.i("注册设备并添加MAC索引: ${device.name}, MAC: $macAddress");
    } else {
      Log.i("注册设备(无MAC索引): ${device.name}, 句柄: ${device.nativeHandle}");
    }
  }
  BleDevice? getDeviceById(int id) => _devicesById[id];
  BleDevice? getDeviceByHandle(int handle) => _devicesByHandle[handle];
  BleDevice? getDeviceByName(String name) => _devicesByName[name];
  BleDevice? getDeviceByMac(String macAddress) => _devicesByMac[macAddress];
  BleDevice? findDevice(
      {int? id, int? handle, String? name, String? macAddress}) {
    if (handle != null && _devicesByHandle.containsKey(handle)) {
      return _devicesByHandle[handle];
    }
    if (macAddress != null && _devicesByMac.containsKey(macAddress)) {
      return _devicesByMac[macAddress];
    }
    if (id != null && _devicesById.containsKey(id)) {
      return _devicesById[id];
    }
    if (name != null && _devicesByName.containsKey(name)) {
      return _devicesByName[name];
    }
    if (name != null && name.isNotEmpty) {
      for (var device in _devicesByName.values) {
        if (device.name.contains(name) || name.contains(device.name)) {
          Log.i("找到模糊匹配设备: ${device.name} (查找: $name)");
          return device;
        }
      }
    }
    return null;
  }
  BleDevice restoreDeviceFromPersistent({
    required String id,
    required String name,
    required String macAddress,
    int? rssi,
    DeviceModeType deviceType = DeviceModeType.unknown,
  }) {
    Log.i("尝试从持久化数据恢复设备: $name, MAC: $macAddress deviceType: $deviceType");
    if (_devicesByMac.containsKey(macAddress)) {
      Log.i("找到已存在的设备，使用现有实例");
      final device = _devicesByMac[macAddress]!;
      device.isPersistent = true;
      return device;
    }
    final remoteId = DeviceIdentifier(macAddress);
    final flutterDevice = BluetoothDevice(remoteId: remoteId);
    final handleBase = macAddress.hashCode;
    final idHash = id.hashCode;
    final combinedHandle = ((handleBase & 0xFFFFFFFF) <<
        16) | (idHash & 0xFFFF);
    final device = BleDevice(
        id: int.parse(id),
        name: name,
        rssi: rssi ?? 50,
        nativeHandle: combinedHandle,
        flutterDevice: flutterDevice,
        deviceType: deviceType
    );
    device.isPersistent = true;
    registerDevice(device);
    Log.i("已恢复设备: $name (id: ${device.id}, handle: ${device
        .nativeHandle}, MAC: $macAddress, 类型: ${deviceType.toString().split('.').last})");
    return device;
  }
  void mergeDevice(BleDevice device) {
    BleDevice? existingDevice = findDevice(
        id: device.id,
        handle: device.nativeHandle,
        macAddress: device.flutterDevice?.remoteId.str
    );
    if (existingDevice != null) {
      existingDevice.rssi = device.rssi;
      Log.i("DeviceManager: 更新设备 ${existingDevice
          .name} (RSSI: ${existingDevice.rssi})");
    } else {
      registerDevice(device);
    }
  }
  void unregisterDevice(BleDevice device) {
    _devicesById.remove(device.id);
    _devicesByHandle.remove(device.nativeHandle);
    _devicesByName.remove(device.name);
    if (device.flutterDevice != null) {
      _devicesByMac.remove(device.flutterDevice!.remoteId.str);
    }
    Log.i("DeviceManager: 移除设备 ${device.name} (id: ${device
        .id}, handle: ${device.nativeHandle})");
  }
  List<BleDevice> getAllDevices() {
    return _devicesById.values.toList();
  }
  void clearAllDevices() {
    _devicesById.clear();
    _devicesByHandle.clear();
    _devicesByName.clear();
    _devicesByMac.clear();
    Log.i("DeviceManager: 已清除所有设备");
  }
  void clearScanResults() {
    final devicesToRemove = _devicesById.values.where((device) =>
      device.isPersistent == false).toList();
    for (var device in devicesToRemove) {
      _devicesById.remove(device.id);
      _devicesByHandle.remove(device.nativeHandle);
      _devicesByName.remove(device.name);
      if (device.flutterDevice != null) {
        _devicesByMac.remove(device.flutterDevice!.remoteId.str);
      }
    }
  }
  void clearScanResultsExceptConnected(BleDevice connectedDevice) {
    _devicesById.clear();
    _devicesByHandle.clear();
    _devicesByName.clear();
    _devicesByMac.clear();
    registerDevice(connectedDevice);
    Log.i("DeviceManager: 已清除所有扫描设备，保留${connectedDevice.name}");
  }
}

================
File: lib/ui/ota_view.dart
================
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../service/ota_server.dart';
import '../utils/string_utils.dart';
class TestOtaView extends StatefulWidget {
  const TestOtaView({Key? key}) : super(key: key);
  @override
  State<TestOtaView> createState() => _TestOtaState();
}
class _TestOtaState extends State<TestOtaView> {
  var isDownloading = false;
  var progress = 0;
  var savePath = "";
  var isLocalFile = false;
  @override
  void initState() {
    super.initState();
    // 在组件初始化时请求权限
    _requestPermissions();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("GAIA Control Demo"),
      ),
      body: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: MaterialButton(
                  color: Colors.green,
                  onPressed: () {
                    _pickLocalFile();
                  },
                  child: Text("选择本地bin文件\n${isLocalFile ? "路径：$savePath" : '未选择文件'}"),
                ),
              ),
            ],
          ),
          Row(
            children: [
              // 使用 Wrap 包装 RWCP 文本和复选框，以防止溢出
              Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text('RWCP'),
                  Obx(() {
                    bool rwcp = OtaServer.to.mIsRWCPEnabled.value;
                    return Checkbox(
                        value: rwcp,
                        onChanged: (on) async {
                          OtaServer.to.mIsRWCPEnabled.value = on ?? false;
                          await OtaServer.to.restPayloadSize();
                          await Future.delayed(const Duration(seconds: 1));
                          if (OtaServer.to.mIsRWCPEnabled.value) {
                            OtaServer.to.writeMsg(StringUtils.hexStringToBytes("000A022E01"));
                          } else {
                            OtaServer.to.writeMsg(StringUtils.hexStringToBytes("000A022E00"));
                          }
                        });
                  }),
                ],
              ),
              Expanded(
                child: MaterialButton(
                    color: Colors.blue,
                    onPressed: () {
                      OtaServer.to.logText.value = "";
                    },
                    child: Text('清空LOG')),
              ),
            ],
          ),
          Obx(() {
            final per = OtaServer.to.updatePer.value;
            final isUpgrading = OtaServer.to.isUpgrading.value;
            final upgradeSuccess = OtaServer.to.upgradeSuccess.value;
            return Column(
              children: [
                // 进度条
                Row(
                  children: [
                    Expanded(child: Slider(value: per, onChanged: (data) {}, max: 100, min: 0)),
                    SizedBox(width: 60, child: Text('${per.toStringAsFixed(2)}%'))
                  ],
                ),
                // 升级状态
                Container(
                  padding: EdgeInsets.all(8),
                  color: upgradeSuccess ? Colors.green.shade100 :
                         isUpgrading ? Colors.blue.shade100 : Colors.grey.shade200,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        upgradeSuccess ? '升级成功' :
                        isUpgrading ? '正在升级中...' : '未开始升级',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: upgradeSuccess ? Colors.green.shade800 :
                                 isUpgrading ? Colors.blue.shade800 : Colors.grey.shade700,
                        ),
                      ),
                      if (isUpgrading) ...[
                        SizedBox(width: 10),
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      ]
                    ],
                  ),
                ),
              ],
            );
          }),
          Obx(() {
            final time = OtaServer.to.timeCount.value;
            return MaterialButton(
              color: Colors.blue,
              onPressed: () async {
                // 检查是否已选择文件
                if (!isLocalFile || savePath.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('请先选择升级文件'))
                  );
                  return;
                }
                // 检查文件是否存在
                final file = File(savePath);
                if (!await file.exists()) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('升级文件不存在，请重新选择'))
                  );
                  isLocalFile = false;
                  savePath = "";
                  setState(() {});
                  return;
                }
                // 先启动升级过程
                OtaServer.to.startUpdate();
                // 如果启用了 RWCP，还需要设置 RWCP 模式
                if (OtaServer.to.mIsRWCPEnabled.value) {
                  await OtaServer.to.restPayloadSize();
                  await Future.delayed(const Duration(seconds: 1));
                  OtaServer.to.writeMsg(StringUtils.hexStringToBytes("000A022E01"));
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('已启用 RWCP 模式，开始升级'))
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('开始升级，未启用 RWCP 模式'))
                  );
                }
              },
              child: Text('开始升级 $time'),
            );
          }),
          MaterialButton(
            color: Colors.blue,
            onPressed: () {
              OtaServer.to.stopUpgrade();
            },
            child: const Text('取消升级'),
          ),
          Expanded(child: Obx(() {
            final log = OtaServer.to.logText.value;
            return SingleChildScrollView(
                child: Text(
                  log,
                  style: const TextStyle(fontSize: 10),
                ));
          }))
        ],
      ),
    );
  }
  @override
  void dispose() {
    super.dispose();
    OtaServer.to.disconnect();
  }
  Future<bool> _requestPermissions() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
      Permission.manageExternalStorage,
    ].request();
    bool hasStoragePermission = statuses[Permission.storage]!.isGranted;
    bool hasManagePermission = statuses[Permission.manageExternalStorage]!.isGranted;
    print('存储权限状态: $hasStoragePermission');
    print('管理外部存储权限状态: $hasManagePermission');
    if (hasStoragePermission || hasManagePermission) {
      return true;
    }
    if (statuses[Permission.storage]!.isPermanentlyDenied ||
        statuses[Permission.manageExternalStorage]!.isPermanentlyDenied) {
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('需要存储权限才能选择文件，请在设置中开启'))
      );
      await openAppSettings();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('需要存储权限才能选择文件，请允许权限'))
      );
    }
    return false;
  }
  void _pickLocalFile() async {
    try {
      bool hasPermission = await _requestPermissions();
      if (!hasPermission) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('没有存储权限，无法选择文件'))
        );
        return;
      }
      print('开始选择文件...');
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
        dialogTitle: '选择bin文件',
      );
      if (result != null) {
        String? filePath = result.files.single.path;
        String? fileName = result.files.single.name;
        if (filePath != null) {
          print('选择的文件路径: $filePath');
          print('选择的文件名称: $fileName');
          if (!fileName.toLowerCase().endsWith('.bin')) {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('请选择.bin格式的文件'))
            );
            return;
          }
          try {
            final appDir = await getApplicationDocumentsDirectory();
            final targetPath = '${appDir.path}/1.bin';
            print('目标路径: $targetPath');
            File sourceFile = File(filePath);
            if (!await sourceFile.exists()) {
              print('源文件不存在: $filePath');
              ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('源文件不存在'))
              );
              return;
            }
            Directory(appDir.path).createSync(recursive: true);
            File targetFile = File(targetPath);
            if (await targetFile.exists()) {
              await targetFile.delete();
              print('删除现有目标文件');
            }
            List<int> bytes = await sourceFile.readAsBytes();
            print('读取源文件内容，大小: ${bytes.length} 字节');
            await targetFile.writeAsBytes(bytes);
            print('写入目标文件完成');
            setState(() {
              savePath = targetPath;
              isLocalFile = true;
            });
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('文件已选择: $fileName'))
            );
          } catch (e) {
            print('复制文件错误: $e');
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('复制文件错误: $e'))
            );
          }
        }
      } else {
        print('用户取消了文件选择');
      }
    } catch (e) {
      print('选择文件错误: $e');
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('选择文件错误: $e'))
      );
    }
  }
}

================
File: lib/bluetooth/ffi/ble_ffi_registry.dart
================
class BleFfiRegistry {
  static final BleFfiRegistry _instance = BleFfiRegistry._internal();
  factory BleFfiRegistry() => _instance;
  BleFfiRegistry._internal();
  final Map<int, int> _nativeToFlutterScannerMap = {};
  final Map<int, int> _nativeToFlutterGattMap = {};
  Map<int, int> get nativeToFlutterScannerMap => _nativeToFlutterScannerMap;
  Map<int, int> get nativeToFlutterGattMap => _nativeToFlutterGattMap;
  void registerScannerMapping(int nativeObject, int flutterObject) {
    _nativeToFlutterScannerMap[nativeObject] = flutterObject;
  }
  void removeScannerMapping(int flutterObject) {
    _nativeToFlutterScannerMap.removeWhere((key, value) => value == flutterObject);
  }
  void registerGattMapping(int nativeObject, int flutterObject) {
    _nativeToFlutterGattMap[nativeObject] = flutterObject;
  }
  void removeGattMapping(int flutterObject) {
    _nativeToFlutterGattMap.removeWhere((key, value) => value == flutterObject);
  }
  void clearAllMappings() {
    _nativeToFlutterScannerMap.clear();
    _nativeToFlutterGattMap.clear();
  }
}

================
File: lib/device/d900/d900_device_bindings.dart
================
import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';
import '../../model/d900/d900_callback.dart';
import '../../model/ffi/ffi_d900_device_callback.dart';
import '../../model/ffi/ffi_dx5ii_scan_result.dart';
import '../../model/ffi/ffi_d900_settings.dart';
import '../../utils/log_util.dart';
class D900DeviceBindings {
  static D900DeviceBindings? _instance;
  static D900DeviceBindings get instance {
    _instance ??= D900DeviceBindings._();
    return _instance!;
  }
  late final DynamicLibrary _dylib;
  late final int Function(int) _d900DeviceCreate;
  late final void Function(int) _d900DeviceDestroy;
  late final void Function(int, Pointer<FFID900DeviceCallback>)
  _d900DeviceRegisterCallback;
  late final void Function(int, int) _d900DeviceConnect;
  late final void Function(int) _d900DeviceDisconnect;
  late final void Function(int) _d900DeviceVerify;
  late final void Function(int, int) _d900DevicePowerOn;
  late final void Function(int, Pointer<Utf8>) _d900DeviceSetDeviceName;
  late final void Function(int, int) _d900DeviceSetVolume;
  late final void Function(int, int) _d900DeviceSetMute;
  late final void Function(int, int) _d900DeviceSetInputType;
  late final void Function(int, int) _d900DeviceSetOutputType;
  late final void Function(int, int) _d900DeviceSetDisplayMode;
  late final void Function(int, int) _d900DeviceSetTheme;
  late final void Function(int, int) _d900DeviceSetPowerTrigger;
  late final void Function(int, int) _d900DeviceSetBalance;
  late final void Function(int, int) _d900DeviceEnableAudioBluetooth;
  late final void Function(int, int) _d900DeviceEnableBluetoothAptx;
  late final void Function(int, int) _d900DeviceEnableRelay;
  late final void Function(int, int) _d900DeviceSetMultifunctionKey;
  late final void Function(int, int) _d900DeviceSetUsbMode;
  late final void Function(int, int) _d900DeviceSetScreenBrightness;
  late final void Function(int, int) _d900DeviceSetLanguage;
  late final void Function(int) _d900DeviceResetSettings;
  late final void Function(int) _d900DeviceRestoreFactorySettings;
  late final void Function(int) _d900DeviceRequestSettings;
  late final void Function(int, int) _d900DeviceSetUsbSelect;
  late final void Function(int, int) _d900DeviceEnableUsbDsd;
  late final void Function(int, int) _d900DeviceSetIisPhase;
  late final void Function(int, int) _d900DeviceSetIisChannel;
  D900Callback? _callback;
  int? _nativeObject;
  int? _flutterObjectId;
  D900DeviceBindings._() {
    _dylib = _nativeLib;
    _loadFunctions();
  }
  final DynamicLibrary _nativeLib =
      Platform.isAndroid
          ? DynamicLibrary.open("libtopping_controller.so")
          : DynamicLibrary.process();
  void _loadFunctions() {
    _d900DeviceCreate =
        _dylib
            .lookup<NativeFunction<Int64 Function(Int64)>>('d900_device_create')
            .asFunction();
    _d900DeviceDestroy =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>('d900_device_destory')
            .asFunction();
    _d900DeviceRegisterCallback =
        _dylib
            .lookup<
              NativeFunction<
                Void Function(Int64, Pointer<FFID900DeviceCallback>)
              >
            >('d900_device_register_callback')
            .asFunction();
    _d900DeviceConnect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int64)>>(
              'd900_device_connect',
            )
            .asFunction();
    _d900DeviceDisconnect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'd900_device_disconnect',
            )
            .asFunction();
    _d900DeviceVerify =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>('d900_device_verify')
            .asFunction();
    _d900DevicePowerOn =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_power_on',
            )
            .asFunction();
    _d900DeviceSetDeviceName =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Pointer<Utf8>)>>(
              'd900_device_set_device_name',
            )
            .asFunction();
    _d900DeviceSetVolume =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_volume',
            )
            .asFunction();
    _d900DeviceSetMute =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_mute',
            )
            .asFunction();
    _d900DeviceSetInputType =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_input_type',
            )
            .asFunction();
    _d900DeviceSetOutputType =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_output_type',
            )
            .asFunction();
    _d900DeviceSetDisplayMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_display_mode',
            )
            .asFunction();
    _d900DeviceSetTheme =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_theme',
            )
            .asFunction();
    _d900DeviceSetPowerTrigger =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_power_trigger',
            )
            .asFunction();
    _d900DeviceSetBalance =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_balance',
            )
            .asFunction();
    _d900DeviceEnableAudioBluetooth =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_enable_audio_bluetooth',
            )
            .asFunction();
    _d900DeviceEnableBluetoothAptx =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_enable_bluetooth_aptx',
            )
            .asFunction();
    _d900DeviceEnableRelay =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_enable_remote',
            )
            .asFunction();
    _d900DeviceSetMultifunctionKey =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_multifunction_key',
            )
            .asFunction();
    _d900DeviceSetUsbMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_usb_mode',
            )
            .asFunction();
    _d900DeviceSetScreenBrightness =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_screen_brightness',
            )
            .asFunction();
    _d900DeviceSetLanguage =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_language',
            )
            .asFunction();
    _d900DeviceResetSettings =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'd900_device_reset_settings',
            )
            .asFunction();
    _d900DeviceRestoreFactorySettings =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'd900_device_restore_factory_settings',
            )
            .asFunction();
    _d900DeviceRequestSettings =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'd900_device_request_settings',
            )
            .asFunction();
    _d900DeviceSetUsbSelect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_usb_select',
            )
            .asFunction();
    _d900DeviceEnableUsbDsd =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_enable_usb_dsd',
            )
            .asFunction();
    _d900DeviceSetIisPhase =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_iis_phase',
            )
            .asFunction();
    _d900DeviceSetIisChannel =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'd900_device_set_iis_channel',
            )
            .asFunction();
  }
  static void _onScanResults(
    int flutterObject,
    Pointer<FFIDx5iiScanResult> results,
    int count,
  ) {
    Log.i(
      "Flutter回调: onScanResults, flutterObject: $flutterObject, count: $count",
    );
    final instance = D900DeviceBindings.instance;
    if (instance._callback == null) return;
    final scanResults = <dynamic>[];
    for (var i = 0; i < count; i++) {
      final result = results[i];
      final name = result.name.cast<Utf8>().toDartString();
      final deviceHandle = result.device;
      final rssi = result.rssi;
      scanResults.add(
        _ScanResultWrapper(name: name, device: deviceHandle, rssi: rssi),
      );
      Log.i("添加扫描结果: name=$name, device=$deviceHandle, rssi=$rssi");
    }
    instance._callback!.onScanResults(scanResults);
  }
  static void _onScanFailed(int flutterObject, int errorCode) {
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onScanFailed(errorCode);
    }
  }
  static void _onStateChange(int flutterObject, int state) {
    Log.i(
      "Flutter回调: onStateChange, flutterObject: $flutterObject, state: $state",
    );
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onStateChange(state);
    }
  }
  static void _onVerifyResult(int flutterObject, int type) {
    Log.i("Flutter回调: onVerifyResult, type: $type");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onVerifyResult(type);
    }
  }
  static void _onPowerChange(int flutterObject, int isOn) {
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onPowerChange(isOn != 0);
    }
  }
  static void _onDeviceNameChange(int flutterObject, Pointer<Utf8> name) {
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      final nameStr = name.toDartString();
      instance._callback!.onDeviceNameChange(nameStr);
    }
  }
  static void _onDeviceVolumeChange(int flutterObject, int volume) {
    Log.i("Flutter回调: onDeviceVolumeChange, volume: $volume");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onVolumeChange(volume);
    }
  }
  static void _onDeviceMuteChange(int flutterObject, int isMute) {
    Log.i("Flutter回调: onDeviceMuteChange, isMute: $isMute");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onMuteChange(isMute != 0);
    }
  }
  static void _onDeviceInputTypeChange(int flutterObject, int inputType) {
    Log.i("onDeviceInputTypeChange: $inputType");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onInputTypeChange(inputType);
    }
  }
  static void _onDeviceOutputTypeChange(int flutterObject, int outputType) {
    Log.i("onDeviceOutputTypeChange: $outputType");
    final instance = D900DeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onOutputTypeChange(outputType);
    }
  }
  static void _onDeviceEnableAudioBluetooth(int flutterObject, int enable) {
    Log.i("Flutter回调: onDeviceEnableAudioBluetooth, enable: $enable");
    final instance = D900DeviceBindings.instance;
    if (instance._flutterObjectId == flutterObject &&
        instance._callback != null) {
      instance._callback!.onAudioBluetoothChange(enable != 0);
    }
  }
  static void _onDeviceEnableBluetoothAptx(int flutterObject, int enable) {
    Log.i("Flutter回调: onDeviceEnableBluetoothAptx, enable: $enable");
    final instance = D900DeviceBindings.instance;
    if (instance._flutterObjectId == flutterObject &&
        instance._callback != null) {
      instance._callback!.onBluetoothAptxChange(enable != 0);
    }
  }
  static void _onDeviceEnableRelay(int flutterObject, int enable) {
    Log.i("Flutter回调: onDeviceEnableRelay, enable: $enable");
    final instance = D900DeviceBindings.instance;
    if (instance._flutterObjectId == flutterObject &&
        instance._callback != null) {
      instance._callback!.onRemoteEnabledChange(enable != 0);
    }
  }
  static void _onDeviceSamplingRateChange(int flutterObject, int rate) {
    Log.i("Flutter回调: onDeviceSamplingRateChange, rate: $rate");
    final instance = D900DeviceBindings.instance;
    if (instance._flutterObjectId == flutterObject &&
        instance._callback != null) {
      instance._callback!.onSamplingRateChange(rate);
    }
  }
  static void _onDeviceSettingsResponse(
    int flutterObject,
    Pointer<FFID900Settings> settings,
  ) {
    Log.i("Flutter回调: onDeviceSettingsResponse, pointer: ${settings.address}");
    final instance = D900DeviceBindings.instance;
    if (instance._flutterObjectId == flutterObject &&
        instance._callback != null) {
      instance._callback!.onDeviceSettingsResponse(settings);
    }
  }
  void initialize(D900Callback callback) {
    _callback = callback;
    _flutterObjectId = DateTime.now().millisecondsSinceEpoch;
    _nativeObject = _d900DeviceCreate(_flutterObjectId!);
    final nativeCallbacks = calloc<FFID900DeviceCallback>();
    nativeCallbacks.ref.on_scan_results = Pointer.fromFunction<
      Void Function(Int64, Pointer<FFIDx5iiScanResult>, Size)
    >(_onScanResults);
    nativeCallbacks.ref.on_scan_failed =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onScanFailed);
    nativeCallbacks.ref.on_state_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onStateChange);
    nativeCallbacks.ref.on_verify_result =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onVerifyResult);
    nativeCallbacks.ref.on_power_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onPowerChange);
    nativeCallbacks.ref.on_device_name_change =
        Pointer.fromFunction<Void Function(Int64, Pointer<Utf8>)>(
          _onDeviceNameChange,
        );
    nativeCallbacks.ref.on_device_volume_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceVolumeChange,
        );
    nativeCallbacks.ref.on_device_mute_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onDeviceMuteChange);
    nativeCallbacks.ref.on_device_input_type_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceInputTypeChange,
        );
    nativeCallbacks.ref.on_device_output_type_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceOutputTypeChange,
        );
    nativeCallbacks.ref.on_device_display_mode_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceDisplayModeChange,
        );
    nativeCallbacks.ref.on_device_theme_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onDeviceThemeChange);
    nativeCallbacks.ref.on_device_power_trigger_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDevicePowerTriggerChange,
        );
    nativeCallbacks.ref.on_device_balance_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceBalanceChange,
        );
    nativeCallbacks.ref.on_device_enable_audio_bluetooth =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceEnableAudioBluetooth,
        );
    nativeCallbacks.ref.on_device_enable_bluetooth_aptx =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceEnableBluetoothAptx,
        );
    nativeCallbacks.ref.on_device_enable_relay =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onDeviceEnableRelay);
    nativeCallbacks.ref.on_device_multifunction_key_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceMultifunctionKeyChange,
        );
    nativeCallbacks.ref.on_device_usb_mode_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceUsbModeChange,
        );
    nativeCallbacks.ref.on_device_screen_brightness_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceScreenBrightnessChange,
        );
    nativeCallbacks.ref.on_device_language_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceLanguageChange,
        );
    nativeCallbacks.ref.on_device_sampling_rate_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceSamplingRateChange,
        );
    nativeCallbacks.ref.on_device_reset_settings =
        Pointer.fromFunction<Void Function(Int64)>(_onDeviceResetSettings);
    nativeCallbacks.ref.on_device_restore_factory_settings =
        Pointer.fromFunction<Void Function(Int64)>(
          _onDeviceRestoreFactorySettings,
        );
    nativeCallbacks.ref.on_device_settings_response =
        Pointer.fromFunction<Void Function(Int64, Pointer<FFID900Settings>)>(
          _onDeviceSettingsResponse,
        );
    nativeCallbacks.ref.on_device_usb_select_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceUsbSelectChange,
        );
    nativeCallbacks.ref.on_device_enable_usb_dsd =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceUsbDsdEnabledChange,
        );
    nativeCallbacks.ref.on_device_iis_phase_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceIisPhaseChange,
        );
    nativeCallbacks.ref.on_device_iis_channel_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceIisChannelChange,
        );
    _d900DeviceRegisterCallback(_nativeObject!, nativeCallbacks);
    calloc.free(nativeCallbacks);
  }
  void connect(int device) {
    if (_nativeObject != null) {
      _d900DeviceConnect(_nativeObject!, device);
    }
  }
  void disconnect() {
    if (_nativeObject != null) _d900DeviceDisconnect(_nativeObject!);
  }
  void verify() {
    if (_nativeObject != null) _d900DeviceVerify(_nativeObject!);
  }
  void powerOn(bool isOn) {
    if (_nativeObject != null) {
      _d900DevicePowerOn(_nativeObject!, isOn ? 1 : 0);
    }
  }
  void setDeviceName(String name) {
    if (_nativeObject != null) {
      final namePointer = name.toNativeUtf8();
      _d900DeviceSetDeviceName(_nativeObject!, namePointer);
      calloc.free(namePointer);
    }
  }
  void setVolume(int volume) {
    if (_nativeObject != null) _d900DeviceSetVolume(_nativeObject!, volume);
  }
  void setMute(bool isMute) {
    if (_nativeObject != null) {
      _d900DeviceSetMute(_nativeObject!, isMute ? 1 : 0);
    }
  }
  void setInputType(int inputType) {
    if (_nativeObject != null) {
      _d900DeviceSetInputType(_nativeObject!, inputType);
    }
  }
  void setOutputType(int outputType) {
    if (_nativeObject != null) {
      _d900DeviceSetOutputType(_nativeObject!, outputType);
    }
  }
  void setDisplayMode(int displayMode) {
    if (_nativeObject != null) {
      _d900DeviceSetDisplayMode(_nativeObject!, displayMode);
    }
  }
  void setTheme(int theme) {
    if (_nativeObject != null) _d900DeviceSetTheme(_nativeObject!, theme);
  }
  void setPowerTrigger(int triggerType) {
    if (_nativeObject != null) {
      _d900DeviceSetPowerTrigger(_nativeObject!, triggerType);
    }
  }
  void setRightBalance(int balance) {
    if (_nativeObject != null) {
      _d900DeviceSetBalance(_nativeObject!, balance);
    }
  }
  void enableAudioBluetooth(bool enable) {
    if (_nativeObject != null) {
      _d900DeviceEnableAudioBluetooth(_nativeObject!, enable ? 1 : 0);
    }
  }
  void enableBluetoothAptx(bool enable) {
    if (_nativeObject != null) {
      _d900DeviceEnableBluetoothAptx(_nativeObject!, enable ? 1 : 0);
    }
  }
  void enableRelay(bool enable) {
    if (_nativeObject != null) {
      _d900DeviceEnableRelay(_nativeObject!, enable ? 1 : 0);
    }
  }
  void setMultifunctionKey(int keyType) {
    if (_nativeObject != null) {
      _d900DeviceSetMultifunctionKey(_nativeObject!, keyType);
    }
  }
  void setUsbMode(int usbMode) {
    if (_nativeObject != null) _d900DeviceSetUsbMode(_nativeObject!, usbMode);
  }
  void setScreenBrightness(int brightnessType) {
    if (_nativeObject != null) {
      _d900DeviceSetScreenBrightness(_nativeObject!, brightnessType);
    }
  }
  void setLanguage(int language) {
    if (_nativeObject != null) {
      _d900DeviceSetLanguage(_nativeObject!, language);
    }
  }
  void resetSettings() {
    if (_nativeObject != null) _d900DeviceResetSettings(_nativeObject!);
  }
  void restoreFactorySettings() {
    if (_nativeObject != null) {
      _d900DeviceRestoreFactorySettings(_nativeObject!);
    }
  }
  void requestSettings() {
    if (_nativeObject != null) _d900DeviceRequestSettings(_nativeObject!);
  }
  void setUsbSelect(int type) {
    if (_nativeObject == null) return;
    _d900DeviceSetUsbSelect(_nativeObject!, type);
  }
  void enableUsbDsd(bool enable) {
    if (_nativeObject == null) return;
    _d900DeviceEnableUsbDsd(_nativeObject!, enable ? 1 : 0);
  }
  void setIisPhase(int phase) {
    if (_nativeObject == null) return;
    _d900DeviceSetIisPhase(_nativeObject!, phase);
  }
  void setIisChannel(int channel) {
    if (_nativeObject == null) return;
    _d900DeviceSetIisChannel(_nativeObject!, channel);
  }
  void dispose() {
    if (_nativeObject != null) {
      _d900DeviceDestroy(_nativeObject!);
      _nativeObject = null;
    }
    _callback = null;
  }
}
class _ScanResultWrapper {
  final String name;
  final int device;
  final int rssi;
  _ScanResultWrapper({
    required this.name,
    required this.device,
    required this.rssi,
  });
  @override
  String toString() => '{name: $name, device: $device, rssi: $rssi}';
}
void _onDeviceDisplayModeChange(int flutterObject, int mode) {
  Log.i("onDeviceDisplayModeChange: $mode");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onDisplayModeChange(mode);
  }
}
void _onDeviceThemeChange(int flutterObject, int theme) {
  Log.i("onDeviceThemeChange: $theme");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onThemeChange(theme);
  }
}
void _onDevicePowerTriggerChange(int flutterObject, int trigger) {
  Log.i("onDevicePowerTriggerChange: $trigger");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onPowerTriggerChange(trigger);
  }
}
void _onDeviceBalanceChange(int flutterObject, int balance) {
  Log.i("onDeviceRightBalanceChange: $balance");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onBalanceChange(balance);
  }
}
void _onDeviceMultifunctionKeyChange(int flutterObject, int key) {
  Log.i("onDeviceMultifunctionKeyChange: $key");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onMultifunctionKeyChange(key);
  }
}
void _onDeviceUsbModeChange(int flutterObject, int mode) {
  Log.i("onDeviceUsbModeChange: $mode");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onUsbModeChange(mode);
  }
}
void _onDeviceScreenBrightnessChange(int flutterObject, int brightness) {
  Log.i("onDeviceScreenBrightnessChange: $brightness");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onScreenBrightnessChange(brightness);
  }
}
void _onDeviceLanguageChange(int flutterObject, int language) {
  Log.i("onDeviceLanguageChange: $language");
  final instance = D900DeviceBindings.instance;
  if (instance._callback != null) {
    instance._callback!.onLanguageChange(language);
  }
}
void _onDeviceResetSettings(int flutterObject) =>
    Log.i("onDeviceResetSettings: $flutterObject");
void _onDeviceRestoreFactorySettings(int flutterObject) =>
    Log.i("onDeviceRestoreFactorySettings");
void _onDeviceUsbSelectChange(int flutterObject, int usbSelect) {
  Log.i("Flutter回调: onDeviceUsbSelectChange, usbSelect: $usbSelect");
  final instance = D900DeviceBindings.instance;
  if (instance._flutterObjectId == flutterObject &&
      instance._callback != null) {
    instance._callback!.onUsbSelectChange(usbSelect);
  }
}
void _onDeviceUsbDsdEnabledChange(int flutterObject, int isEnabled) {
  Log.i("Flutter回调: onDeviceUsbDsdEnabledChange, isEnabled: $isEnabled");
  final instance = D900DeviceBindings.instance;
  if (instance._flutterObjectId == flutterObject &&
      instance._callback != null) {
    instance._callback!.onUsbDsdEnabledChange(isEnabled == 1);
  }
}
void _onDeviceIisPhaseChange(int flutterObject, int phase) {
  Log.i("Flutter回调: onDeviceIisPhaseChange, phase: $phase");
  final instance = D900DeviceBindings.instance;
  if (instance._flutterObjectId == flutterObject &&
      instance._callback != null) {
    instance._callback!.onIisPhaseChange(phase);
  }
}
void _onDeviceIisChannelChange(int flutterObject, int channel) {
  Log.i("Flutter回调: onDeviceIisChannelChange, channel: $channel");
  final instance = D900DeviceBindings.instance;
  if (instance._flutterObjectId == flutterObject &&
      instance._callback != null) {
    instance._callback!.onIisChannelChange(channel);
  }
}

================
File: lib/device/dx5/dx5ii_device_bindings.dart
================
import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';
import '../../model/dx5ii/dx5ii_callback.dart';
import '../../model/ffi/ffi_dx5ii_device_callback.dart';
import '../../model/ffi/ffi_dx5ii_scan_result.dart';
import '../../model/ffi/ffi_dx5ii_settings.dart';
import '../../utils/log_util.dart';
class Dx5iiDeviceBindings {
  static Dx5iiDeviceBindings? _instance;
  static Dx5iiDeviceBindings get instance {
    _instance ??= Dx5iiDeviceBindings._();
    return _instance!;
  }
  late final DynamicLibrary _dylib;
  late final int Function(int) _dx5iiDeviceCreate;
  late final void Function(int) _dx5iiDeviceDestroy;
  late final void Function(int, Pointer<FFIDx5iiDeviceCallback>)
  _dx5iiDeviceRegisterCallback;
  late final void Function(int, int) _dx5iiDeviceConnect;
  late final void Function(int) _dx5iiDeviceDisconnect;
  late final void Function(int) _dx5iiDeviceVerify;
  late final void Function(int, int) _dx5iiDevicePowerOn;
  late final void Function(int, Pointer<Utf8>) _dx5iiDeviceSetDeviceName;
  late final void Function(int, int) _dx5iiDeviceSetVolume;
  late final void Function(int, int) _dx5iiDeviceSetMute;
  late final void Function(int, int) _dx5iiDeviceSetInputType;
  late final void Function(int, int) _dx5iiDeviceSetOutputType;
  late final void Function(int, int) _dx5iiDeviceEnableHeadphone;
  late final void Function(int, int) _dx5iiDeviceSetHeadphoneGain;
  late final void Function(int, int) _dx5iiDeviceSetDisplayMode;
  late final void Function(int, int) _dx5iiDeviceSetTheme;
  late final void Function(int, int) _dx5iiDeviceSetPowerTrigger;
  late final void Function(int, int) _dx5iiDeviceSetBalance;
  late final void Function(int, int) _dx5iiDeviceSetFilter;
  late final void Function(int, int) _dx5iiDeviceSetDecodeMode;
  late final void Function(int, int) _dx5iiDeviceEnableAudioBluetooth;
  late final void Function(int, int) _dx5iiDeviceEnableBluetoothAptx;
  late final void Function(int, int) _dx5iiDeviceEnableRelay;
  late final void Function(int, int) _dx5iiDeviceSetMultifunctionKey;
  late final void Function(int, int) _dx5iiDeviceSetUsbMode;
  late final void Function(int, int) _dx5iiDeviceSetScreenBrightness;
  late final void Function(int, int) _dx5iiDeviceSetLanguage;
  late final void Function(int) _dx5iiDeviceResetSettings;
  late final void Function(int) _dx5iiDeviceRestoreFactorySettings;
  late final void Function(int) _dx5iiDeviceRequestSettings;
  Dx5iiCallback? _callback;
  int? _nativeObject;
  int? _flutterObjectId;
  Dx5iiDeviceBindings._() {
    _dylib = _nativeLib;
    _loadFunctions();
  }
  final DynamicLibrary _nativeLib =
      Platform.isAndroid
          ? DynamicLibrary.open("libtopping_controller.so")
          : DynamicLibrary.process();
  void _loadFunctions() {
    _dx5iiDeviceCreate =
        _dylib
            .lookup<NativeFunction<Int64 Function(Int64)>>(
              'dx5ii_device_create',
            )
            .asFunction();
    _dx5iiDeviceDestroy =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'dx5ii_device_destory',
            )
            .asFunction();
    _dx5iiDeviceRegisterCallback =
        _dylib
            .lookup<
              NativeFunction<
                Void Function(Int64, Pointer<FFIDx5iiDeviceCallback>)
              >
            >('dx5ii_device_register_callback')
            .asFunction();
    _dx5iiDeviceConnect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int64)>>(
              'dx5ii_device_connect',
            )
            .asFunction();
    _dx5iiDeviceDisconnect =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'dx5ii_device_disconnect',
            )
            .asFunction();
    _dx5iiDeviceVerify =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>('dx5ii_device_verify')
            .asFunction();
    _dx5iiDevicePowerOn =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_power_on',
            )
            .asFunction();
    _dx5iiDeviceSetDeviceName =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Pointer<Utf8>)>>(
              'dx5ii_device_set_device_name',
            )
            .asFunction();
    _dx5iiDeviceSetVolume =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_volume',
            )
            .asFunction();
    _dx5iiDeviceSetMute =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_mute',
            )
            .asFunction();
    _dx5iiDeviceSetInputType =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_input_type',
            )
            .asFunction();
    _dx5iiDeviceSetOutputType =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_output_type',
            )
            .asFunction();
    _dx5iiDeviceEnableHeadphone =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_enable_headphone',
            )
            .asFunction();
    _dx5iiDeviceSetHeadphoneGain =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_headphone_gain',
            )
            .asFunction();
    _dx5iiDeviceSetDisplayMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_diaplay_mode',
            )
            .asFunction();
    _dx5iiDeviceSetTheme =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_theme',
            )
            .asFunction();
    _dx5iiDeviceSetPowerTrigger =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_power_trigger',
            )
            .asFunction();
    _dx5iiDeviceSetBalance =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_balance',
            )
            .asFunction();
    _dx5iiDeviceSetFilter =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_pcm_filter',
            )
            .asFunction();
    _dx5iiDeviceSetDecodeMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_decode_type',
            )
            .asFunction();
    _dx5iiDeviceEnableAudioBluetooth =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_enable_audio_bluetooth',
            )
            .asFunction();
    _dx5iiDeviceEnableBluetoothAptx =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_enable_bluetooth_aptx',
            )
            .asFunction();
    _dx5iiDeviceEnableRelay =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_enable_remote',
            )
            .asFunction();
    _dx5iiDeviceSetMultifunctionKey =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_multifunction_key',
            )
            .asFunction();
    _dx5iiDeviceSetUsbMode =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_usb_mode',
            )
            .asFunction();
    _dx5iiDeviceSetScreenBrightness =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_screen_brightness',
            )
            .asFunction();
    _dx5iiDeviceSetLanguage =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'dx5ii_device_set_language',
            )
            .asFunction();
    _dx5iiDeviceResetSettings =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'dx5ii_device_reset_settings',
            )
            .asFunction();
    _dx5iiDeviceRestoreFactorySettings =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'dx5ii_device_restore_factory_settings',
            )
            .asFunction();
    _dx5iiDeviceRequestSettings =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'dx5ii_device_request_settings',
            )
            .asFunction();
  }
  static void _onScanResults(
    int flutterObject,
    Pointer<FFIDx5iiScanResult> results,
    int count,
  ) {
    Log.i(
      "Flutter回调: onScanResults, flutterObject: $flutterObject, count: $count",
    );
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback == null) return;
    final scanResults = <dynamic>[];
    for (var i = 0; i < count; i++) {
      final result = results[i];
      final name = result.name.cast<Utf8>().toDartString();
      final deviceHandle = result.device;
      final rssi = result.rssi;
      scanResults.add(
        _ScanResultWrapper(name: name, device: deviceHandle, rssi: rssi),
      );
      Log.i("添加扫描结果: name=$name, device=$deviceHandle, rssi=$rssi");
    }
    instance._callback!.onScanResults(scanResults);
  }
  static void _onScanFailed(int flutterObject, int errorCode) {
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onScanFailed(errorCode);
    }
  }
  static void _onStateChange(int flutterObject, int state) {
    Log.i(
      "Flutter回调: onStateChange, flutterObject: $flutterObject, state: $state",
    );
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onStateChange(state);
    }
  }
  static void _onVerifyResult(int flutterObject, int type) {
    Log.i("Flutter回调: onVerifyResult, type: $type");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onVerifyResult(type);
    }
  }
  static void _onPowerChange(int flutterObject, int isOn) {
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onPowerChange(isOn != 0);
    }
  }
  static void _onDeviceNameChange(int flutterObject, Pointer<Utf8> name) {
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      final nameStr = name.toDartString();
      instance._callback!.onDeviceNameChange(nameStr);
    }
  }
  static void _onDeviceVolumeChange(int flutterObject, int volume) {
    Log.i("Flutter回调: onDeviceVolumeChange, volume: $volume");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onVolumeChange(volume);
    }
  }
  static void _onDeviceMuteChange(int flutterObject, int isMute) {
    Log.i("Flutter回调: onDeviceMuteChange, isMute: $isMute");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onMuteChange(isMute != 0);
    }
  }
  static void _onDeviceInputTypeChange(int flutterObject, int inputType) {
    Log.i("onDeviceInputTypeChange: $inputType");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onInputTypeChange(inputType);
    }
  }
  static void _onDeviceOutputTypeChange(int flutterObject, int outputType) {
    Log.i("onDeviceOutputTypeChange: $outputType");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onOutputTypeChange(outputType);
    }
  }
  static void _onDeviceEnableHeadphone(int flutterObject, int enable) {
    Log.i("onDeviceEnableHeadphone: $enable");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onHeadphoneEnabledChange(enable != 0);
    }
  }
  static void _onDeviceHeadphoneGainChange(int flutterObject, int gain) {
    Log.i("onDeviceHeadphoneGainChange: $gain");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onHeadphoneGainChange(gain);
    }
  }
  static void _onDeviceDisplayModeChange(int flutterObject, int mode) {
    Log.i("onDeviceDisplayModeChange: $mode");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onDisplayModeChange(mode);
    }
  }
  static void _onDeviceThemeChange(int flutterObject, int theme) {
    Log.i("onDeviceThemeChange: $theme");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onThemeChange(theme);
    }
  }
  static void _onDevicePowerTriggerChange(int flutterObject, int trigger) {
    Log.i("onDevicePowerTriggerChange: $trigger");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onPowerTriggerChange(trigger);
    }
  }
  static void _onDeviceBalanceChange(int flutterObject, int balance) {
    Log.i("onDeviceRightBalanceChange: $balance");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onBalanceChange(balance);
    }
  }
  static void _onDeviceFilterChange(int flutterObject, int filter) {
    Log.i("onDeviceFilterChange: $filter");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onPcmFilterChange(filter);
    }
  }
  static void _onDeviceDecodeModeChange(int flutterObject, int mode) {
    Log.i("onDeviceDecodeModeChange: $mode");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onDecodeModeChange(mode);
    }
  }
  static void _onDeviceEnableAudioBluetooth(int flutterObject, int enable) {
    Log.i("onDeviceEnableAudioBluetooth: $enable");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onAudioBluetoothChange(enable != 0);
    }
  }
  static void _onDeviceEnableBluetoothAptx(int flutterObject, int enable) {
    Log.i("onDeviceEnableBluetoothAptx: $enable");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onBluetoothAptxChange(enable != 0);
    }
  }
  static void _onDeviceEnableRelay(int flutterObject, int enable) {
    Log.i("onDeviceEnableRelay: $enable");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onRemoteEnabledChange(enable != 0);
    }
  }
  static void _onDeviceMultifunctionKeyChange(int flutterObject, int key) {
    Log.i("onDeviceMultifunctionKeyChange: $key");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onMultifunctionKeyChange(key);
    }
  }
  static void _onDeviceUsbModeChange(int flutterObject, int mode) {
    Log.i("onDeviceUsbModeChange: $mode");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onUsbModeChange(mode);
    }
  }
  static void _onDeviceScreenBrightnessChange(
    int flutterObject,
    int brightness,
  ) {
    Log.i("onDeviceScreenBrightnessChange: $brightness");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onScreenBrightnessChange(brightness);
    }
  }
  static void _onDeviceLanguageChange(int flutterObject, int language) {
    Log.i("onDeviceLanguageChange: $language");
    final instance = Dx5iiDeviceBindings.instance;
    if (instance._callback != null) {
      instance._callback!.onLanguageChange(language);
    }
  }
  static void _onDeviceResetSettings(int flutterObject) =>
      Log.i("onDeviceResetSettings: $flutterObject");
  static void _onDeviceRestoreFactorySettings(int flutterObject) =>
      Log.i("onDeviceRestoreFactorySettings");
  static void _onDeviceSettingsResponse(
    int flutterObject,
    Pointer<FFIDx5iiSettings> settings,
  ) {
    Log.i("Flutter回调: onDeviceSettingsResponse, pointer: ${settings.address}");
    final instance = Dx5iiDeviceBindings.instance;
    try {
      instance._callback?.onDeviceSettingsResponse(settings);
    } catch (e) {
      Log.e("调用 onDeviceSettingsResponse 回调时出错: $e");
    }
  }
  void initialize(Dx5iiCallback callback) {
    _callback = callback;
    _flutterObjectId = DateTime.now().millisecondsSinceEpoch;
    _nativeObject = _dx5iiDeviceCreate(_flutterObjectId!);
    final nativeCallbacks = calloc<FFIDx5iiDeviceCallback>();
    nativeCallbacks.ref.on_scan_results = Pointer.fromFunction<
      Void Function(Int64, Pointer<FFIDx5iiScanResult>, Size)
    >(_onScanResults);
    nativeCallbacks.ref.on_scan_failed =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onScanFailed);
    nativeCallbacks.ref.on_state_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onStateChange);
    nativeCallbacks.ref.on_verify_result =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onVerifyResult);
    nativeCallbacks.ref.on_power_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onPowerChange);
    nativeCallbacks.ref.on_device_name_change =
        Pointer.fromFunction<Void Function(Int64, Pointer<Utf8>)>(
          _onDeviceNameChange,
        );
    nativeCallbacks.ref.on_device_volume_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceVolumeChange,
        );
    nativeCallbacks.ref.on_device_mute_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onDeviceMuteChange);
    nativeCallbacks.ref.on_device_input_type_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceInputTypeChange,
        );
    nativeCallbacks.ref.on_device_output_type_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceOutputTypeChange,
        );
    nativeCallbacks.ref.on_device_enable_headphone =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceEnableHeadphone,
        );
    nativeCallbacks.ref.on_device_headphone_gain_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceHeadphoneGainChange,
        );
    nativeCallbacks.ref.on_device_display_mode_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceDisplayModeChange,
        );
    nativeCallbacks.ref.on_device_theme_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onDeviceThemeChange);
    nativeCallbacks.ref.on_device_power_trigger_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDevicePowerTriggerChange,
        );
    nativeCallbacks.ref.on_device_balance_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceBalanceChange,
        );
    nativeCallbacks.ref.on_device_filter_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceFilterChange,
        );
    nativeCallbacks.ref.on_device_decode_mode_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceDecodeModeChange,
        );
    nativeCallbacks.ref.on_device_enable_audio_bluetooth =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceEnableAudioBluetooth,
        );
    nativeCallbacks.ref.on_device_enable_bluetooth_aptx =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceEnableBluetoothAptx,
        );
    nativeCallbacks.ref.on_device_enable_relay =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onDeviceEnableRelay);
    nativeCallbacks.ref.on_device_multifunction_key_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceMultifunctionKeyChange,
        );
    nativeCallbacks.ref.on_device_usb_mode_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceUsbModeChange,
        );
    nativeCallbacks.ref.on_device_screen_brightness_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceScreenBrightnessChange,
        );
    nativeCallbacks.ref.on_device_language_change =
        Pointer.fromFunction<Void Function(Int64, Int32)>(
          _onDeviceLanguageChange,
        );
    nativeCallbacks.ref.on_device_reset_settings =
        Pointer.fromFunction<Void Function(Int64)>(_onDeviceResetSettings);
    nativeCallbacks.ref.on_device_restore_factory_settings =
        Pointer.fromFunction<Void Function(Int64)>(
          _onDeviceRestoreFactorySettings,
        );
    nativeCallbacks.ref.on_device_settings_response =
        Pointer.fromFunction<Void Function(Int64, Pointer<FFIDx5iiSettings>)>(
          _onDeviceSettingsResponse,
        );
    _dx5iiDeviceRegisterCallback(_nativeObject!, nativeCallbacks);
    calloc.free(nativeCallbacks);
  }
  void connect(int device) {
    if (_nativeObject != null) {
      _dx5iiDeviceConnect(_nativeObject!, device);
    }
  }
  void disconnect() {
    if (_nativeObject != null) _dx5iiDeviceDisconnect(_nativeObject!);
  }
  void verify() {
    if (_nativeObject != null) _dx5iiDeviceVerify(_nativeObject!);
  }
  void powerOn(bool isOn) {
    if (_nativeObject != null) {
      _dx5iiDevicePowerOn(_nativeObject!, isOn ? 1 : 0);
    }
  }
  void setDeviceName(String name) {
    if (_nativeObject != null) {
      final namePointer = name.toNativeUtf8();
      _dx5iiDeviceSetDeviceName(_nativeObject!, namePointer);
      calloc.free(namePointer);
    }
  }
  void setVolume(int volume) {
    Log.i("Dx5iiDeviceBindings: 设置音量: $volume");
    if (_nativeObject != null) _dx5iiDeviceSetVolume(_nativeObject!, volume);
  }
  void setMute(bool isMute) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetMute(_nativeObject!, isMute ? 1 : 0);
    }
  }
  void setInputType(int inputType) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetInputType(_nativeObject!, inputType);
    }
  }
  void setOutputType(int outputType) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetOutputType(_nativeObject!, outputType);
    }
  }
  void enableHeadphone(bool enable) {
    if (_nativeObject != null) {
      _dx5iiDeviceEnableHeadphone(_nativeObject!, enable ? 1 : 0);
    }
  }
  void setHeadphoneGain(int gainType) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetHeadphoneGain(_nativeObject!, gainType);
    }
  }
  void setDisplayMode(int displayMode) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetDisplayMode(_nativeObject!, displayMode);
    }
  }
  void setTheme(int theme) {
    if (_nativeObject != null) _dx5iiDeviceSetTheme(_nativeObject!, theme);
  }
  void setPowerTrigger(int triggerType) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetPowerTrigger(_nativeObject!, triggerType);
    }
  }
  void setBalance(int balance) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetBalance(_nativeObject!, balance);
    }
  }
  void setPcmFilter(int filterType) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetFilter(_nativeObject!, filterType);
    }
  }
  void setDecodeMode(int decodeMode) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetDecodeMode(_nativeObject!, decodeMode);
    }
  }
  void enableAudioBluetooth(bool enable) {
    if (_nativeObject != null) {
      _dx5iiDeviceEnableAudioBluetooth(_nativeObject!, enable ? 1 : 0);
    }
  }
  void enableBluetoothAptx(bool enable) {
    if (_nativeObject != null) {
      _dx5iiDeviceEnableBluetoothAptx(_nativeObject!, enable ? 1 : 0);
    }
  }
  void enableRelay(bool enable) {
    if (_nativeObject != null) {
      _dx5iiDeviceEnableRelay(_nativeObject!, enable ? 1 : 0);
    }
  }
  void setMultifunctionKey(int keyType) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetMultifunctionKey(_nativeObject!, keyType);
    }
  }
  void setUsbMode(int usbMode) {
    if (_nativeObject != null) _dx5iiDeviceSetUsbMode(_nativeObject!, usbMode);
  }
  void setScreenBrightness(int brightnessType) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetScreenBrightness(_nativeObject!, brightnessType);
    }
  }
  void setLanguage(int language) {
    if (_nativeObject != null) {
      _dx5iiDeviceSetLanguage(_nativeObject!, language);
    }
  }
  void resetSettings() {
    if (_nativeObject != null) _dx5iiDeviceResetSettings(_nativeObject!);
  }
  void restoreFactorySettings() {
    if (_nativeObject != null) {
      _dx5iiDeviceRestoreFactorySettings(_nativeObject!);
    }
  }
  void requestSettings() {
    if (_nativeObject != null) _dx5iiDeviceRequestSettings(_nativeObject!);
  }
  void dispose() {
    if (_nativeObject != null) {
      _dx5iiDeviceDestroy(_nativeObject!);
      _nativeObject = null;
    }
    _callback = null;
  }
}
class _ScanResultWrapper {
  final String name;
  final int device;
  final int rssi;
  _ScanResultWrapper({
    required this.name,
    required this.device,
    required this.rssi,
  });
  @override
  String toString() => '{name: $name, device: $device, rssi: $rssi}';
}

================
File: lib/interfaces/device_events.dart
================
import 'dart:async';
import '../event/connection_state_event.dart';
import '../model/bluetooth/ble_device.dart';
import '../model/dx5ii/dx5ii_settings.dart';
import '../model/dx5ii/dx5ii_verify_result_type.dart';
abstract class DeviceEvents {
  Stream<List<BleDevice>> get scanResults;
  Stream<ConnectionStateEvent> get deviceState;
  Stream<Dx5iiVerifyResultType> get verifyResult;
  Stream<bool> get powerState;
  Stream<String> get deviceName;
  Stream<Dx5iiSettings>? get settings => null;
  Stream<int> get volume;
  Stream<bool> get mute;
  Stream<int> get inputType;
  Stream<int> get outputType;
  Stream<bool> get headphoneEnabled;
  Stream<int> get headphoneGain;
  Stream<int> get displayMode;
  Stream<int> get theme;
  Stream<int> get powerTrigger;
  Stream<int> get balance;
  Stream<int> get filter;
  Stream<int> get decodeMode;
  Stream<bool> get audioBluetooth;
  Stream<bool> get bluetoothAptx;
  Stream<bool> get relay;
  Stream<int> get multifunctionKey;
  Stream<int> get usbMode;
  Stream<int> get screenBrightness;
  Stream<int> get language;
  Stream<void> get onResetSettings;
  Stream<void> get onRestoreFactorySettings;
  Stream<int> get samplingRate;
  Stream<bool> get isScanning;
}

================
File: lib/mappers/ffi_mapper.dart
================
import 'dart:ffi';
import 'package:ffi/ffi.dart';
import '../model/bluetooth/ble_device.dart';
import '../model/bluetooth/gatt_characteristic.dart';
import '../model/enums/ble_connection_state.dart';
import '../model/ffi/ffi_connection_state.dart';
import '../model/ffi/ffi_dx5ii_scan_result.dart';
import '../model/ffi/ffi_gatt_characteristic.dart';
import '../model/ffi/ffi_scan_result.dart';
import '../utils/log_util.dart';
class FFIMapper {
  static BleConnectionState fromFFIConnectionState(FFIConnectionState state) {
    switch (state) {
      case FFIConnectionState.stateConnectedUnsafe:
        return BleConnectionState.connectedUnsafe;
      case FFIConnectionState.stateConnected:
        return BleConnectionState.connected;
      case FFIConnectionState.stateDisconnected:
        return BleConnectionState.disconnected;
    }
  }
  static BleConnectionState connectionStateFromValue(int value) {
    FFIConnectionState? ffiState;
    if (value == FFIConnectionState.stateConnectedUnsafe.value) {
      ffiState = FFIConnectionState.stateConnectedUnsafe;
    } else if (value == FFIConnectionState.stateConnected.value) {
      ffiState = FFIConnectionState.stateConnected;
    } else if (value == FFIConnectionState.stateDisconnected.value) {
      ffiState = FFIConnectionState.stateDisconnected;
    }
    if (ffiState != null) {
      return fromFFIConnectionState(ffiState);
    }
    return BleConnectionState.fromValue(value);
  }
  static GattCharacteristic fromFFIGattCharacteristic(
    FFIGattCharacteristic characteristic,
  ) {
    final uuid = _safeReadUtf8String(characteristic.uuid);
    final property = characteristic.property;
    List<int> value = [];
    if (characteristic.value != nullptr && characteristic.value_len > 0) {
      for (int i = 0; i < characteristic.value_len; i++) {
        value.add(characteristic.value[i]);
      }
    }
    return GattCharacteristic(uuid: uuid, property: property, value: value);
  }
  static Pointer<FFIGattCharacteristic> toFFIGattCharacteristic(
    GattCharacteristic characteristic,
    Allocator allocator,
  ) {
    final nativeChar = allocator<FFIGattCharacteristic>();
    try {
      final uuidPtr = characteristic.uuid.toNativeUtf8(allocator: allocator);
      nativeChar.ref.uuid = uuidPtr.cast();
      nativeChar.ref.property = characteristic.property;
      if (characteristic.value.isNotEmpty) {
        final valuePtr = allocator<Uint8>(characteristic.value.length);
        for (var i = 0; i < characteristic.value.length; i++) {
          valuePtr[i] = characteristic.value[i];
        }
        nativeChar.ref.value = valuePtr;
        nativeChar.ref.value_len = characteristic.value.length;
      } else {
        nativeChar.ref.value = nullptr;
        nativeChar.ref.value_len = 0;
      }
      return nativeChar;
    } catch (e) {
      Log.e('转换特征值到FFI结构时出错: $e');
      if (nativeChar.ref.uuid != nullptr) {
        allocator.free(nativeChar.ref.uuid.cast<NativeType>());
      }
      if (nativeChar.ref.value != nullptr) {
        allocator.free(nativeChar.ref.value);
      }
      allocator.free(nativeChar);
      rethrow;
    }
  }
  static String _safeReadUtf8String(Pointer<Char> charPtr) {
    if (charPtr == nullptr) {
      return '';
    }
    try {
      return charPtr.cast<Utf8>().toDartString();
    } catch (e) {
      Log.e('读取UTF8字符串失败: $e');
      return '';
    }
  }
}

================
File: lib/model/d900/d900_callback.dart
================
import 'dart:ffi';
import 'package:topping_ble_control/model/base/topping_device_callback.dart';
import 'package:topping_ble_control/model/ffi/ffi_d900_settings.dart';
import 'package:topping_ble_control/utils/log_util.dart';
class D900Callback extends ToppingDeviceCallback {
  final Function(Pointer<FFID900Settings>) onDeviceSettingsResponse;
  final Function(int) onUsbSelectChange;
  final Function(bool) onUsbDsdEnabledChange;
  final Function(int) onIisPhaseChange;
  final Function(int) onIisChannelChange;
  D900Callback({
    required super.onScanResults,
    required super.onScanFailed,
    required super.onStateChange,
    required super.onVerifyResult,
    super.onPowerChange,
    super.onDeviceNameChange,
    required this.onDeviceSettingsResponse,
    super.onVolumeChange,
    super.onMuteChange,
    super.onInputTypeChange,
    super.onOutputTypeChange,
    super.onDisplayModeChange,
    super.onThemeChange,
    super.onPowerTriggerChange,
    super.onBalanceChange,
    super.onAudioBluetoothChange,
    super.onBluetoothAptxChange,
    super.onRemoteEnabledChange,
    super.onMultifunctionKeyChange,
    super.onUsbModeChange,
    this.onUsbSelectChange = _defaultOnUsbSelectChange,
    this.onUsbDsdEnabledChange = _defaultOnUsbDsdEnabledChange,
    this.onIisPhaseChange = _defaultOnIisPhaseChange,
    this.onIisChannelChange = _defaultOnIisChannelChange,
    super.onScreenBrightnessChange,
    super.onLanguageChange,
    super.onSamplingRateChange,
  });
  static void _defaultOnUsbSelectChange(int type) =>
      Log.e("未处理: USB选择类型 $type");
  static void _defaultOnUsbDsdEnabledChange(bool enabled) =>
      Log.e("未处理: USB DSD状态 $enabled");
  static void _defaultOnIisPhaseChange(int phase) => Log.e("未处理: IIS相位 $phase");
  static void _defaultOnIisChannelChange(int channel) =>
      Log.e("未处理: IIS通道 $channel");
}

================
File: lib/model/d900/d900_settings.dart
================
import 'dart:ffi';
import 'dart:convert';
import 'dart:typed_data';
import 'package:ffi/ffi.dart';
import 'package:topping_ble_control/model/base/topping_device_settings.dart';
import 'package:topping_ble_control/model/ffi/ffi_d900_settings.dart';
import 'package:topping_ble_control/utils/log_util.dart';
class D900Settings extends ToppingDeviceSettings {
  final int usbSelect;
  final bool usbDsdEnabled;
  final int iisPhase;
  final int iisChannel;
  D900Settings({
    required super.isOn,
    required super.deviceName,
    required super.volume,
    required super.mute,
    required super.inputType,
    required super.outputType,
    required super.displayMode,
    required super.theme,
    required super.powerTrigger,
    required this.usbSelect,
    required super.balance,
    required super.audioBluetooth,
    required super.bluetoothAptx,
    required super.remoteEnabled,
    required super.multifunctionKey,
    required this.usbDsdEnabled,
    required super.usbMode,
    required this.iisPhase,
    required this.iisChannel,
    required super.screenBrightness,
    required super.language,
    required super.sampling,
  });
  factory D900Settings.fromFFI(Pointer<FFID900Settings> ffiSettingsPtr) {
    if (ffiSettingsPtr == nullptr) {
      Log.e("从FFI构造D900Settings失败：传入的指针为空");
      return _defaultSettings("空指针错误");
    }
    final ffiSettings = ffiSettingsPtr.ref;
    try {
      Log.i("开始从FFI设置构造: ${ffiSettingsPtr.address}");
      final nameBytes = Uint8List(32);
      for (int i = 0; i < 32; i++) {
        nameBytes[i] = ffiSettings.device_name[i];
      }
      String name = ToppingDeviceSettings.readDeviceNameFromFFI(nameBytes);
      int readVolume = 0;
      try {
        readVolume = ffiSettings.volume;
      } catch (e) {
        Log.e("读取volume失败: $e");
      }
      bool readMute = false;
      try {
        readMute = ffiSettings.is_mute != 0;
      } catch (e) {
        Log.e("读取is_mute失败: $e");
      }
      int readInputType = 0;
      try {
        readInputType = ffiSettings.input_type;
      } catch (e) {
        Log.e("读取input_type失败: $e");
      }
      int readOutputType = 0;
      try {
        readOutputType = ffiSettings.output_type;
      } catch (e) {
        Log.e("读取output_type失败: $e");
      }
      int readDisplayMode = 0;
      try {
        readDisplayMode = ffiSettings.display_mode;
      } catch (e) {
        Log.e("读取display_mode失败: $e");
      }
      int readTheme = 0;
      try {
        readTheme = ffiSettings.theme;
      } catch (e) {
        Log.e("读取theme失败: $e");
      }
      int readPowerTrigger = 0;
      try {
        readPowerTrigger = ffiSettings.power_trigger;
      } catch (e) {
        Log.e("读取power_trigger失败: $e");
      }
      int readUsbSelect = 0;
      try {
        readUsbSelect = ffiSettings.usb_select;
      } catch (e) {
        Log.e("读取usb_select失败: $e");
      }
      int readBalance = 0;
      try {
        readBalance = ffiSettings.balance;
      } catch (e) {
        Log.e("读取balance失败: $e");
      }
      bool readAudioBluetooth = false;
      try {
        readAudioBluetooth = ffiSettings.audio_bt_enable != 0;
      } catch (e) {
        Log.e("读取audio_bt_enable失败: $e");
      }
      bool readBluetoothAptx = false;
      try {
        readBluetoothAptx = ffiSettings.aptx_enable != 0;
      } catch (e) {
        Log.e("读取aptx_enable失败: $e");
      }
      bool readRemoteEnabled = false;
      try {
        readRemoteEnabled = ffiSettings.remote_enable != 0;
      } catch (e) {
        Log.e("读取remote_enable失败: $e");
      }
      int readMultifunctionKey = 0;
      try {
        readMultifunctionKey = ffiSettings.multifunction_key;
      } catch (e) {
        Log.e("读取multifunction_key失败: $e");
      }
      bool readUsbDsdEnabled = false;
      try {
        readUsbDsdEnabled = ffiSettings.usb_dsd_enable != 0;
      } catch (e) {
        Log.e("读取usb_dsd_enable失败: $e");
      }
      int readUsbMode = 0;
      try {
        readUsbMode = ffiSettings.usb_mode;
      } catch (e) {
        Log.e("读取usb_mode失败: $e");
      }
      int readIisPhase = 0;
      try {
        readIisPhase = ffiSettings.iis_phase;
      } catch (e) {
        Log.e("读取iis_phase失败: $e");
      }
      int readIisChannel = 0;
      try {
        readIisChannel = ffiSettings.iis_channel;
      } catch (e) {
        Log.e("读取iis_channel失败: $e");
      }
      int readScreenBrightness = 0;
      try {
        readScreenBrightness = ffiSettings.screen_brightness;
      } catch (e) {
        Log.e("读取screen_brightness失败: $e");
      }
      int readLanguage = 0;
      try {
        readLanguage = ffiSettings.language;
      } catch (e) {
        Log.e("读取language失败: $e");
      }
      int readSampling = 0;
      try {
        readSampling = ffiSettings.sampling;
      } catch (e) {
        Log.e("读取sampling失败: $e");
      }
      bool readIsOn = false;
      try {
        readIsOn = ffiSettings.is_on != 0;
      } catch (e) {
        Log.e("读取is_on失败: $e");
      }
      return D900Settings(
        isOn: readIsOn,
        deviceName: name,
        volume: readVolume,
        mute: readMute,
        inputType: readInputType,
        outputType: readOutputType,
        displayMode: readDisplayMode,
        theme: readTheme,
        powerTrigger: readPowerTrigger,
        usbSelect: readUsbSelect,
        balance: readBalance,
        audioBluetooth: readAudioBluetooth,
        bluetoothAptx: readBluetoothAptx,
        remoteEnabled: readRemoteEnabled,
        multifunctionKey: readMultifunctionKey,
        usbDsdEnabled: readUsbDsdEnabled,
        usbMode: readUsbMode,
        iisPhase: readIisPhase,
        iisChannel: readIisChannel,
        screenBrightness: readScreenBrightness,
        language: readLanguage,
        sampling: readSampling,
      );
    } catch (e, s) {
      Log.e("从FFI创建D900Settings时发生严重错误: $e\nStack trace:\n$s");
      return _defaultSettings("构造函数错误");
    }
  }
  static D900Settings _defaultSettings(String errorContext) {
    Log.w("返回默认D900Settings，错误上下文: $errorContext");
    return D900Settings(
      isOn: false,
      deviceName: "默认名称 ($errorContext)",
      volume: -1,
      mute: false,
      inputType: 0,
      outputType: 0,
      displayMode: 0,
      theme: 0,
      powerTrigger: 0,
      usbSelect: 0,
      balance: 0,
      audioBluetooth: false,
      bluetoothAptx: false,
      remoteEnabled: false,
      multifunctionKey: 0,
      usbDsdEnabled: false,
      usbMode: 0,
      iisPhase: 0,
      iisChannel: 0,
      screenBrightness: 0,
      language: 0,
      sampling: 0,
    );
  }
  Pointer<FFID900Settings> toFFI() {
    final ffiSettingsPtr = calloc<FFID900Settings>();
    try {
      final ffiSettings = ffiSettingsPtr.ref;
      final nameBytes = utf8.encode(deviceName);
      final List<int> nameBytesPadded = List.filled(32, 0);
      final lengthToCopy =
          nameBytes.length < 32 ? nameBytes.length : 31;
      for (int i = 0; i < lengthToCopy; i++) {
        nameBytesPadded[i] = nameBytes[i];
      }
      for (int i = 0; i < 32; i++) {
        ffiSettings.device_name[i] = nameBytesPadded[i];
      }
      ffiSettings.is_on = isOn ? 1 : 0;
      ffiSettings.volume = volume;
      ffiSettings.is_mute = mute ? 1 : 0;
      ffiSettings.input_type = inputType;
      ffiSettings.output_type = outputType;
      ffiSettings.display_mode = displayMode;
      ffiSettings.theme = theme;
      ffiSettings.power_trigger = powerTrigger;
      ffiSettings.usb_select = usbSelect;
      ffiSettings.balance = balance;
      ffiSettings.audio_bt_enable = audioBluetooth ? 1 : 0;
      ffiSettings.aptx_enable = bluetoothAptx ? 1 : 0;
      ffiSettings.remote_enable = remoteEnabled ? 1 : 0;
      ffiSettings.multifunction_key = multifunctionKey;
      ffiSettings.usb_dsd_enable = usbDsdEnabled ? 1 : 0;
      ffiSettings.usb_mode = usbMode;
      ffiSettings.iis_phase = iisPhase;
      ffiSettings.iis_channel = iisChannel;
      ffiSettings.screen_brightness = screenBrightness;
      ffiSettings.language = language;
      ffiSettings.sampling = sampling;
      return ffiSettingsPtr;
    } catch (e) {
      Log.e("转换为FFI设置时出错: $e");
      calloc.free(ffiSettingsPtr);
      rethrow;
    }
  }
  static void freeCreatedFFIPointer(Pointer<FFID900Settings> ffiSettingsPtr) {
    if (ffiSettingsPtr != nullptr) {
      calloc.free(ffiSettingsPtr);
    }
  }
  @override
  D900Settings copy() {
    return D900Settings(
      isOn: isOn,
      deviceName: deviceName,
      volume: volume,
      mute: mute,
      inputType: inputType,
      outputType: outputType,
      displayMode: displayMode,
      theme: theme,
      powerTrigger: powerTrigger,
      usbSelect: usbSelect,
      balance: balance,
      audioBluetooth: audioBluetooth,
      bluetoothAptx: bluetoothAptx,
      remoteEnabled: remoteEnabled,
      multifunctionKey: multifunctionKey,
      usbDsdEnabled: usbDsdEnabled,
      usbMode: usbMode,
      iisPhase: iisPhase,
      iisChannel: iisChannel,
      screenBrightness: screenBrightness,
      language: language,
      sampling: sampling,
    );
  }
  @override
  String toString() {
    return super.toString().replaceFirst(
      '}',
      ', '
          'usbSelect: $usbSelect, '
          'usbDsdEnabled: $usbDsdEnabled, '
          'iisPhase: $iisPhase, '
          'iisChannel: $iisChannel}',
    );
  }
}

================
File: lib/model/d900/d900_verify_result_type.dart
================
import '../base/topping_verify_result_type.dart';
typedef D900VerifyResultType = ToppingVerifyResultType;
class D900VerifyResult {
  static ToppingVerifyResultType fromValue(int value) {
    switch (value) {
      case 0:
        return ToppingVerifyResultType.success;
      case 1:
        return ToppingVerifyResultType.failed;
      case 2:
        return ToppingVerifyResultType.timeout;
      default:
        return ToppingVerifyResultType.unknown;
    }
  }
}

================
File: lib/model/enums/device_mode_type.dart
================
import 'package:topping_ble_control/utils/log_util.dart';
enum DeviceModeType {
  dx5,
  dx9,
  unknown ;
  factory DeviceModeType.fromString(String name) {
    final lowerName = name.toLowerCase();
     var firstWhere = values.firstWhere(
          (e) => e.name == lowerName,
      orElse: () => DeviceModeType.unknown,
    );
     Log.i("尝试匹配枚举值的名称: name: $name, firstWhere: $firstWhere");
    return firstWhere;
  }
}

================
File: lib/registry/service_registry.dart
================
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
class ServiceRegistry {
  static final ServiceRegistry _instance = ServiceRegistry._internal();
  factory ServiceRegistry() => _instance;
  final Map<int, BluetoothService> _services = {};
  ServiceRegistry._internal();
  void registerService(int handle, BluetoothService service) {
    _services[handle] = service;
  }
  BluetoothService? getServiceByHandle(int handle) {
    return _services[handle];
  }
  Map<int, BluetoothService> getServices() {
    return Map.from(_services);
  }
  void clear() {
    _services.clear();
  }
}

================
File: lib/service/ota_server.dart
================
import 'dart:async';
import 'dart:collection';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import '../gaia/ConfirmationType.dart';
import '../gaia/GAIA.dart';
import '../gaia/GaiaPacketBLE.dart';
import '../gaia/OpCodes.dart';
import '../gaia/ResumePoints.dart';
import '../gaia/UpgradeStartCFMStatus.dart';
import '../gaia/VMUPacket.dart';
import '../gaia/rwcp/RWCPClient.dart';
import '../gaia/rwcp/RWCPListener.dart';
import '../utils/string_utils.dart';
class OtaServer extends GetxService implements RWCPListener {
  var logText = "".obs;
  final String TAG = "OtaServer";
  var deviceVersion = "".obs;
  // 升级前的版本信息
  var previousVersion = "".obs;
  // 升级是否成功
  var upgradeSuccess = false.obs;
  // 蓝牙设备
  BluetoothDevice? _device;
  // 服务和特征UUID
  final String otaServiceUuid = "00001100-d102-11e1-9b23-00025b00a5a5";
  final String notifyCharUuid = "00001102-d102-11e1-9b23-00025b00a5a5";
  final String writeCharUuid = "00001101-d102-11e1-9b23-00025b00a5a5";
  final String writeNoResCharUuid = "00001103-d102-11e1-9b23-00025b00a5a5";
  BluetoothService? _otaService;
  BluetoothCharacteristic? _notifyChar;
  BluetoothCharacteristic? _writeChar;
  BluetoothCharacteristic? _writeNoResChar;
  RxBool isUpgrading = false.obs;
  bool transFerComplete = false;
  var mStartAttempts = 0;
  var mStartOffset = 0;
  List<int>? mBytesFile;
  List<int> writeBytes = [];
  var mMaxLengthForDataTransfer = 16;
  var mPayloadSizeMax = 16;
  bool wasLastPacket = false;
  int mBytesToSend = 0;
  int mResumePoint = -1;
  var mIsRWCPEnabled = false.obs;
  int sendPkgCount = 0;
  RxDouble updatePer = RxDouble(0);
  bool hasToAbort = false;
  final writeQueue = Queue<List<int>>();
  StreamSubscription<List<int>>? _notifySubscription;
  StreamSubscription<List<int>>? _rwcpSubscription;
  String fileMd5 = "";
  var percentage = 0.0.obs;
  Timer? _timer;
  var timeCount = 0.obs;
  //RWCP
  ListQueue<double> mProgressQueue = ListQueue();
  late RWCPClient mRWCPClient;
  int mTransferStartTime = 0;
  int writeRTCPCount = 0;
  File? file;
  static OtaServer get to => Get.find();
  @override
  void onInit() {
    super.onInit();
    mRWCPClient = RWCPClient(this);
    // 监听蓝牙状态变化
    FlutterBluePlus.adapterState.listen((state) {
      switch (state) {
        case BluetoothAdapterState.on:
          addLog("蓝牙打开");
          break;
        case BluetoothAdapterState.off:
          addLog("蓝牙关闭");
          break;
        default:
          break;
      }
    });
  }
  /// 设置要使用的蓝牙设备
  Future<bool> setDevice(BluetoothDevice device) async {
    try {
      _device = device;
      addLog('设置设备: ${device.platformName}');
      // 检查设备连接状态
      if (device.connectionState != BluetoothConnectionState.connected) {
        addLog('设备未连接，尝试连接...');
        try {
          await device.connect();
          addLog('设备连接成功');
        } catch (e) {
          addLog('设备连接失败: $e');
          return false;
        }
      }
      // 发现服务
      List<BluetoothService> services = await device.discoverServices();
      // 查找 OTA 服务
      _otaService = services.firstWhere(
        (service) =>
            service.uuid.toString().toLowerCase() ==
            otaServiceUuid.toLowerCase(),
        orElse: () => throw Exception('未找到 OTA 服务'),
      );
      // 查找并设置特征
      _findAndSetCharacteristics();
      // 注册通知
      await registerNotice();
      return true;
    } catch (e) {
      addLog('设置设备失败: $e');
      return false;
    }
  }
  /// 查找并设置特征
  void _findAndSetCharacteristics() {
    if (_otaService == null) {
      addLog('未找到 OTA 服务，无法设置特征');
      return;
    }
    try {
      // 查找通知特征
      _notifyChar = _otaService!.characteristics.firstWhere(
        (c) => c.uuid.toString().toLowerCase() == notifyCharUuid.toLowerCase(),
        orElse: () => throw Exception('未找到通知特征'),
      );
      // 查找写入特征
      _writeChar = _otaService!.characteristics.firstWhere(
        (c) => c.uuid.toString().toLowerCase() == writeCharUuid.toLowerCase(),
        orElse: () => throw Exception('未找到写入特征'),
      );
      // 查找无响应写入特征
      _writeNoResChar = _otaService!.characteristics.firstWhere(
        (c) =>
            c.uuid.toString().toLowerCase() == writeNoResCharUuid.toLowerCase(),
        orElse: () => throw Exception('未找到无响应写入特征'),
      );
      addLog('特征设置成功');
    } catch (e) {
      addLog('设置特征失败: $e');
    }
  }
  void writeMsg(List<int> data) {
    scheduleMicrotask(() async {
      await writeData(data);
    });
  }
  Future<void> registerRWCP() async {
    if (_device == null || _writeNoResChar == null) {
      addLog('设备或特征未设置，无法注册 RWCP');
      return;
    }
    // 取消之前的订阅
    await _rwcpSubscription?.cancel();
    // 订阅无响应写入特征的通知
    _rwcpSubscription = _writeNoResChar!.onValueReceived.listen(
      (data) {
        mRWCPClient.onReceiveRWCPSegment(data);
      },
      onError: (error) {
        addLog('接收 RWCP 数据错误: $error');
      },
    );
    // 启用通知
    await _writeNoResChar!.setNotifyValue(true);
    addLog("RWCP 注册成功");
    // 如果传输已完成，重新连接
    if (transFerComplete) {
      await Future.delayed(const Duration(seconds: 1));
      transFerComplete = false;
      addLog("传输已完成，重新连接");
      sendUpgradeConnect();
    }
    // 移除自动启动升级的逻辑，让用户手动点击按钮开始升级
  }
  /// 注册通知
  Future<void> registerNotice() async {
    if (_device == null || _notifyChar == null) {
      addLog('设备或通知特征未设置，无法注册通知');
      return;
    }
    // 取消之前的订阅
    await _notifySubscription?.cancel();
    // 订阅通知特征
    _notifySubscription = _notifyChar!.onValueReceived.listen(
      (data) {
        addLog("收到通知>${StringUtils.byteToHexString(data)}");
        handleRecMsg(data);
      },
      onError: (error) {
        addLog('接收通知错误: $error');
      },
    );
    // 启用通知
    await _notifyChar!.setNotifyValue(true);
    await Future.delayed(const Duration(seconds: 1));
    // 发送注册通知命令
    GaiaPacketBLE packet = GaiaPacketBLE.buildGaiaNotificationPacket(
      GAIA.COMMAND_REGISTER_NOTIFICATION,
      GAIA.VMU_PACKET,
      null,
      GAIA.BLE,
    );
    writeMsg(packet.getBytes());
    // 如果开启RWCP那么需要在重连之后启用RWCP
    if (isUpgrading.value && transFerComplete && mIsRWCPEnabled.value) {
      // 开启RWCP
      await Future.delayed(const Duration(seconds: 1));
      writeMsg(StringUtils.hexStringToBytes("000A022E01"));
    }
  }
  void startUpdate() async {
    previousVersion.value = deviceVersion.value;
    addLog('升级前版本: ${previousVersion.value}');
    upgradeSuccess.value = false;
    logText.value = "";
    writeBytes.clear();
    writeRTCPCount = 0;
    mProgressQueue.clear();
    mTransferStartTime = 0;
    timeCount.value = 0;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      timeCount.value += 1;
    });
    sendPkgCount = 0;
    updatePer.value = 0;
    writeQueue.clear();
    resetUpload();
    // 直接开始升级过程
    addLog('开始升级连接');
    isUpgrading.value = true; // 设置为 true，表示正在升级
    sendUpgradeConnect();
  }
  void handleRecMsg(List<int> data) async {
    GaiaPacketBLE packet = GaiaPacketBLE.fromByte(data) ?? GaiaPacketBLE(0);
    if (packet.isAcknowledgement()) {
      int status = packet.getStatus();
      if (status == GAIA.SUCCESS) {
        receiveSuccessfulAcknowledgement(packet);
        // 处理版本信息响应
        if (packet.getCommand() ==
            GAIA.COMMAND_GET_APPLICATION_VERSION + 0x8000) {
          final payload = packet.mPayload ?? [];
          if (payload.isNotEmpty) {
            // 直接显示原始数据，便于调试
            String rawData = StringUtils.byteToHexString(payload);
            addLog("原始版本数据: $rawData");
            try {
              List<String> parts = [];
              if (payload.length >= 2) {
                int majorVersion = payload[0];
                parts.add("主版本: $majorVersion");
              }
              if (payload.length >= 3) {
                int minorVersion = payload[1];
                parts.add("次版本: $minorVersion");
              }
              if (payload.length > 3) {
                String additionalInfo = StringUtils.byteToHexString(
                  payload.sublist(3),
                );
                parts.add("其他信息: $additionalInfo");
              }
              String versionInfo = parts.join("\n");
              if (versionInfo.isEmpty) {
                versionInfo = "原始数据: $rawData";
              }
              deviceVersion.value = versionInfo;
              addLog("获取到设备版本信息: $versionInfo");
            } catch (e) {
              addLog("解析版本信息出错: $e");
              deviceVersion.value = "原始数据: $rawData";
            }
          }
        }
      } else {
        receiveUnsuccessfulAcknowledgement(packet);
      }
    } else if (packet.getCommand() == GAIA.COMMAND_EVENT_NOTIFICATION) {
      final payload = packet.mPayload ?? [];
      if (payload.isNotEmpty) {
        int event = packet.getEvent();
        if (event == GAIA.VMU_PACKET) {
          createAcknowledgmentRequest();
          await Future.delayed(const Duration(milliseconds: 1000));
          receiveVMUPacket(payload.sublist(1));
          return;
        } else {
          return;
        }
      } else {
        createAcknowledgmentRequest();
        await Future.delayed(const Duration(milliseconds: 1000));
        return;
      }
    }
  }
  void receiveSuccessfulAcknowledgement(GaiaPacketBLE packet) {
    addLog(
      "receiveSuccessfulAcknowledgement ${StringUtils.intTo2HexString(packet.getCommand())}",
    );
    if (packet.getCommand() == GAIA.COMMAND_GET_APPLICATION_VERSION + 0x8000) {
      final payload = packet.mPayload ?? [];
      if (payload.isNotEmpty) {
        String rawData = StringUtils.byteToHexString(payload);
        addLog("原始版本数据: $rawData");
        deviceVersion.value =
            "设备ID: ${rawData.substring(0, 16)}\n" +
            "版本信息: ${rawData.substring(16)}";
      }
    }
    else if (packet.getCommand() ==
        GAIA.COMMAND_GET_HOST_FEATURE_INFORMATION + 0x8000) {
      final payload = packet.mPayload ?? [];
      if (payload.isNotEmpty) {
        String rawData = StringUtils.byteToHexString(payload);
        addLog("原始设备特性数据: $rawData");
        try {
          if (payload[0] == 0x00 && payload.length > 1) {
            List<String> features = [];
            if (payload.length >= 2) {
              String featureHex = StringUtils.byteToHexString(
                payload.sublist(1),
              );
              features.add("特性数据: $featureHex");
            }
            deviceVersion.value = features.join("\n");
            addLog("获取到设备特性信息: ${features.join(', ')}");
          } else {
            deviceVersion.value = "原始数据: $rawData";
          }
        } catch (e) {
          addLog("解析设备特性信息出错: $e");
          deviceVersion.value = "原始数据: $rawData";
        }
      }
    }
    switch (packet.getCommand()) {
      case GAIA.COMMAND_VM_UPGRADE_CONNECT:
        {
          addLog('收到升级连接响应');
          int size = mPayloadSizeMax;
          if (mIsRWCPEnabled.value) {
            size = mPayloadSizeMax - 1;
            size = (size % 2 == 0) ? size : size - 1;
          }
          mMaxLengthForDataTransfer =
              size - VMUPacket.REQUIRED_INFORMATION_LENGTH;
          addLog(
            "mMaxLengthForDataTransfer $mMaxLengthForDataTransfer mPayloadSizeMax $mPayloadSizeMax",
          );
          resetUpload();
          sendSyncReq();
        }
        break;
      case GAIA.COMMAND_VM_UPGRADE_DISCONNECT:
        stopUpgrade();
        break;
      case GAIA.COMMAND_VM_UPGRADE_CONTROL:
        onSuccessfulTransmission();
        break;
      case GAIA.COMMAND_SET_DATA_ENDPOINT_MODE:
        if (mIsRWCPEnabled.value) {
          registerRWCP();
        } else {
          _rwcpSubscription?.cancel();
        }
        break;
    }
  }
  void receiveUnsuccessfulAcknowledgement(GaiaPacketBLE packet) {
    addLog("命令发送失败${StringUtils.intTo2HexString(packet.getCommand())}");
    if (packet.getCommand() == GAIA.COMMAND_VM_UPGRADE_CONNECT ||
        packet.getCommand() == GAIA.COMMAND_VM_UPGRADE_CONTROL) {
      sendUpgradeDisconnect();
    } else if (packet.getCommand() == GAIA.COMMAND_VM_UPGRADE_DISCONNECT) {
    } else if (packet.getCommand() == GAIA.COMMAND_SET_DATA_ENDPOINT_MODE ||
        packet.getCommand() == GAIA.COMMAND_GET_DATA_ENDPOINT_MODE) {
      mIsRWCPEnabled.value = false;
      onRWCPNotSupported();
    } else if (packet.getCommand() ==
        GAIA.COMMAND_GET_HOST_FEATURE_INFORMATION) {
      addLog("设备不支持特性信息命令，尝试获取应用版本");
      GaiaPacketBLE versionPacket = GaiaPacketBLE(
        GAIA.COMMAND_GET_APPLICATION_VERSION,
      );
      writeMsg(versionPacket.getBytes());
    }
  }
  void startUpgradeProcess() {
    resetUpload();
    sendSyncReq();
  }
  void resetUpload() {
    transFerComplete = false;
    mStartAttempts = 0;
    mBytesToSend = 0;
    mStartOffset = 0;
  }
  void stopUpgrade() async {
    _timer?.cancel();
    timeCount.value = 0;
    abortUpgrade();
    resetUpload();
    writeRTCPCount = 0;
    updatePer.value = 0;
    isUpgrading.value = false;
    await Future.delayed(const Duration(milliseconds: 500));
    sendUpgradeDisconnect();
  }
  void sendSyncReq() async {
    final filePath = await getApplicationDocumentsDirectory();
    final saveBinPath = filePath.path + "/1.bin";
    File file = File(saveBinPath);
    mBytesFile = await file.readAsBytes();
    int fileSize = mBytesFile?.length ?? 0;
    fileMd5 = StringUtils.file2md5(mBytesFile ?? []).toUpperCase();
    addLog("读取到文件MD5$fileMd5");
    addLog("文件大小: ${fileSize} 字节");
    final endMd5 = StringUtils.hexStringToBytes(fileMd5.substring(24));
    VMUPacket packet = VMUPacket.get(OpCodes.UPGRADE_SYNC_REQ, data: endMd5);
    sendVMUPacket(packet, false);
  }
  void sendVMUPacket(VMUPacket packet, bool isTransferringData) {
    List<int> bytes = packet.getBytes();
    if (isTransferringData && mIsRWCPEnabled.value) {
      final packet = GaiaPacketBLE(
        GAIA.COMMAND_VM_UPGRADE_CONTROL,
        mPayload: bytes,
      );
      try {
        List<int> bytes = packet.getBytes();
        if (mTransferStartTime <= 0) {
          mTransferStartTime = DateTime.now().millisecond;
        }
        bool success = mRWCPClient.sendData(bytes);
        if (!success) {
          addLog(
            "Fail to send GAIA packet for GAIA command: ${packet.getCommandId()}",
          );
        }
      } catch (e) {
        addLog(
          "Exception when attempting to create GAIA packet: " + e.toString(),
        );
      }
    } else {
      final pkg = GaiaPacketBLE(
        GAIA.COMMAND_VM_UPGRADE_CONTROL,
        mPayload: bytes,
      );
      writeMsg(pkg.getBytes());
    }
  }
  void receiveVMUPacket(List<int> data) {
    try {
      final packet = VMUPacket.getPackageFromByte(data);
      if (packet != null) {
        handleVMUPacket(packet);
      } else {
        addLog("无法解析 VMU 包");
      }
    } catch (e) {
      addLog("receiveVMUPacket $e");
    }
  }
  void createAcknowledgmentRequest() {
    writeMsg(StringUtils.hexStringToBytes("000AC00300"));
  }
  void handleVMUPacket(VMUPacket? packet) {
    switch (packet?.mOpCode) {
      case OpCodes.UPGRADE_SYNC_CFM:
        receiveSyncCFM(packet);
        break;
      case OpCodes.UPGRADE_START_CFM:
        receiveStartCFM(packet);
        break;
      case OpCodes.UPGRADE_DATA_BYTES_REQ:
        receiveDataBytesREQ(packet);
        break;
      case OpCodes.UPGRADE_ABORT_CFM:
        receiveAbortCFM();
        break;
      case OpCodes.UPGRADE_ERROR_WARN_IND:
        receiveErrorWarnIND(packet);
        break;
      case OpCodes.UPGRADE_IS_VALIDATION_DONE_CFM:
        receiveValidationDoneCFM(packet);
        break;
      case OpCodes.UPGRADE_TRANSFER_COMPLETE_IND:
        receiveTransferCompleteIND();
        break;
      case OpCodes.UPGRADE_COMMIT_REQ:
        receiveCommitREQ();
        break;
      case OpCodes.UPGRADE_COMPLETE_IND:
        receiveCompleteIND();
        break;
    }
  }
  void sendUpgradeConnect() async {
    GaiaPacketBLE packet = GaiaPacketBLE(GAIA.COMMAND_VM_UPGRADE_CONNECT);
    writeMsg(packet.getBytes());
  }
  void cancelNotification() async {
    GaiaPacketBLE packet = GaiaPacketBLE.buildGaiaNotificationPacket(
      GAIA.COMMAND_CANCEL_NOTIFICATION,
      GAIA.VMU_PACKET,
      null,
      GAIA.BLE,
    );
    writeMsg(packet.getBytes());
  }
  void sendUpgradeDisconnect() {
    GaiaPacketBLE packet = GaiaPacketBLE(GAIA.COMMAND_VM_UPGRADE_DISCONNECT);
    writeMsg(packet.getBytes());
  }
  void receiveSyncCFM(VMUPacket? packet) {
    List<int> data = packet?.mData ?? [];
    if (data.length >= 6) {
      int step = data[0];
      addLog("上次传输步骤 step $step");
      if (step == ResumePoints.IN_PROGRESS) {
        setResumePoint(step);
      } else {
        mResumePoint = step;
      }
    } else {
      mResumePoint = ResumePoints.DATA_TRANSFER;
    }
    sendStartReq();
  }
  void sendStartReq() {
    VMUPacket packet = VMUPacket.get(OpCodes.UPGRADE_START_REQ);
    sendVMUPacket(packet, false);
  }
  void receiveStartCFM(VMUPacket? packet) {
    List<int> data = packet?.mData ?? [];
    if (data.length >= 3) {
      if (data[0] == UpgradeStartCFMStatus.SUCCESS) {
        mStartAttempts = 0;
        switch (mResumePoint) {
          case ResumePoints.COMMIT:
            askForConfirmation(ConfirmationType.COMMIT);
            break;
          case ResumePoints.TRANSFER_COMPLETE:
            askForConfirmation(ConfirmationType.TRANSFER_COMPLETE);
            break;
          case ResumePoints.IN_PROGRESS:
            askForConfirmation(ConfirmationType.IN_PROGRESS);
            break;
          case ResumePoints.VALIDATION:
            sendValidationDoneReq();
            break;
          case ResumePoints.DATA_TRANSFER:
          default:
            sendStartDataReq();
            break;
        }
      }
    }
  }
  void receiveAbortCFM() {
    addLog("receiveAbortCFM");
    stopUpgrade();
  }
  void receiveErrorWarnIND(VMUPacket? packet) async {
    List<int> data = packet?.mData ?? [];
    sendErrorConfirmation(data);
    int returnCode = StringUtils.extractIntFromByteArray(data, 0, 2, false);
    addLog(
      "receiveErrorWarnIND 升级失败 错误码0x${returnCode.toRadixString(16)} fileMd5$fileMd5",
    );
    if (returnCode == 0x81) {
      addLog("包不通过");
      askForConfirmation(ConfirmationType.WARNING_FILE_IS_DIFFERENT);
    } else if (returnCode == 0x21) {
      addLog("电量过低");
      askForConfirmation(ConfirmationType.BATTERY_LOW_ON_DEVICE);
    } else {
      stopUpgrade();
    }
  }
  void receiveValidationDoneCFM(VMUPacket? packet) {
    addLog("receiveValidationDoneCFM");
    List<int> data = packet?.getBytes() ?? [];
    if (data.length == 2) {
      final time = StringUtils.extractIntFromByteArray(data, 0, 2, false);
      Future.delayed(
        Duration(milliseconds: time),
      ).then((value) => sendValidationDoneReq());
    } else {
      sendValidationDoneReq();
    }
  }
  void receiveTransferCompleteIND() {
    addLog("receiveTransferCompleteIND");
    transFerComplete = true;
    setResumePoint(ResumePoints.TRANSFER_COMPLETE);
    addLog("固件传输完成，等待设备确认");
    askForConfirmation(ConfirmationType.TRANSFER_COMPLETE);
  }
  void receiveCommitREQ() {
    addLog("receiveCommitREQ");
    setResumePoint(ResumePoints.COMMIT);
    askForConfirmation(ConfirmationType.COMMIT);
  }
  void receiveCompleteIND() {
    isUpgrading.value = false;
    upgradeSuccess.value = true;
    addLog("receiveCompleteIND 升级完成");
    addLog("升级前版本: ${previousVersion.value}");
    addLog("升级完成，设备将重启并应用新固件");
    Future.delayed(const Duration(seconds: 1), () {
      disconnectUpgrade();
    });
  }
  void sendValidationDoneReq() {
    VMUPacket packet = VMUPacket.get(OpCodes.UPGRADE_IS_VALIDATION_DONE_REQ);
    sendVMUPacket(packet, false);
  }
  void sendStartDataReq() {
    setResumePoint(ResumePoints.DATA_TRANSFER);
    VMUPacket packet = VMUPacket.get(OpCodes.UPGRADE_START_DATA_REQ);
    sendVMUPacket(packet, false);
  }
  void setResumePoint(int point) {
    mResumePoint = point;
  }
  void receiveDataBytesREQ(VMUPacket? packet) {
    List<int> data = packet?.mData ?? [];
    if (data.length == OpCodes.DATA_LENGTH) {
      var lengthByte = [data[0], data[1], data[2], data[3]];
      var fileByte = [data[4], data[5], data[6], data[7]];
      mBytesToSend = int.parse(
        StringUtils.byteToHexString(lengthByte),
        radix: 16,
      );
      int fileOffset = int.parse(
        StringUtils.byteToHexString(fileByte),
        radix: 16,
      );
      addLog(
        StringUtils.byteToHexString(data) + "本次发包: $fileOffset $mBytesToSend",
      );
      mStartOffset +=
          (fileOffset > 0 &&
                  fileOffset + mStartOffset < (mBytesFile?.length ?? 0))
              ? fileOffset
              : 0;
      mBytesToSend = (mBytesToSend > 0) ? mBytesToSend : 0;
      int remainingLength = mBytesFile?.length ?? 0 - mStartOffset;
      mBytesToSend =
          (mBytesToSend < remainingLength) ? mBytesToSend : remainingLength;
      if (mIsRWCPEnabled.value) {
        while (mBytesToSend > 0) {
          sendNextDataPacket();
        }
      } else {
        addLog("receiveDataBytesREQ: sendNextDataPacket");
        sendNextDataPacket();
      }
    } else {
      addLog("UpgradeError 数据传输失败");
      abortUpgrade();
    }
  }
  void abortUpgrade() {
    if (mRWCPClient.isRunningASession()) {
      mRWCPClient.cancelTransfer();
    }
    mProgressQueue.clear();
    sendAbortReq();
    isUpgrading.value = false;
  }
  void sendAbortReq() {
    VMUPacket packet = VMUPacket.get(OpCodes.UPGRADE_ABORT_REQ);
    sendVMUPacket(packet, false);
  }
  void sendNextDataPacket() {
    if (!isUpgrading.value) {
      stopUpgrade();
      return;
    }
    onFileUploadProgress();
    int bytesToSend =
        mBytesToSend < mMaxLengthForDataTransfer - 1
            ? mBytesToSend
            : mMaxLengthForDataTransfer - 1;
    bool lastPacket = (mBytesFile ?? []).length - mStartOffset <= bytesToSend;
    if (lastPacket) {
      addLog(
        "mMaxLengthForDataTransfer$mMaxLengthForDataTransfer bytesToSend$bytesToSend lastPacket$lastPacket",
      );
    }
    List<int> dataToSend = [];
    for (int i = 0; i < bytesToSend; i++) {
      dataToSend.add((mBytesFile ?? [])[mStartOffset + i]);
    }
    if (lastPacket) {
      wasLastPacket = true;
      mBytesToSend = 0;
    } else {
      mStartOffset += bytesToSend;
      mBytesToSend -= bytesToSend;
    }
    sendData(lastPacket, dataToSend);
  }
  void onFileUploadProgress() {
    double percentage = (mStartOffset * 100.0 / (mBytesFile ?? []).length);
    percentage =
        (percentage < 0)
            ? 0
            : (percentage > 100)
            ? 100
            : percentage;
    if (mIsRWCPEnabled.value) {
      mProgressQueue.add(percentage);
    } else {
      updatePer.value = percentage;
      addLog(
        "当前升级进度: ${percentage.toStringAsFixed(2)}% (${mStartOffset}/${(mBytesFile ?? []).length})",
      );
    }
  }
  void sendData(bool lastPacket, List<int> data) {
    List<int> dataToSend = [];
    dataToSend.add(lastPacket ? 0x01 : 0x00);
    dataToSend.addAll(data);
    sendPkgCount++;
    VMUPacket packet = VMUPacket.get(OpCodes.UPGRADE_DATA, data: dataToSend);
    sendVMUPacket(packet, true);
  }
  void onSuccessfulTransmission() {
    if (wasLastPacket) {
      if (mResumePoint == ResumePoints.DATA_TRANSFER) {
        wasLastPacket = false;
        setResumePoint(ResumePoints.VALIDATION);
        sendValidationDoneReq();
      }
    } else if (hasToAbort) {
      hasToAbort = false;
      abortUpgrade();
    } else {
      if (mBytesToSend > 0 &&
          mResumePoint == ResumePoints.DATA_TRANSFER &&
          !mIsRWCPEnabled.value) {
        sendNextDataPacket();
      }
    }
  }
  void onRWCPNotSupported() {
    addLog("RWCP onRWCPNotSupported");
  }
  void askForConfirmation(int type) {
    int code = -1;
    switch (type) {
      case ConfirmationType.COMMIT:
        {
          code = OpCodes.UPGRADE_COMMIT_CFM;
        }
        break;
      case ConfirmationType.IN_PROGRESS:
        {
          code = OpCodes.UPGRADE_IN_PROGRESS_RES;
        }
        break;
      case ConfirmationType.TRANSFER_COMPLETE:
        {
          code = OpCodes.UPGRADE_TRANSFER_COMPLETE_RES;
        }
        break;
      case ConfirmationType.BATTERY_LOW_ON_DEVICE:
        {
          sendSyncReq();
        }
        return;
      case ConfirmationType.WARNING_FILE_IS_DIFFERENT:
        {
          stopUpgrade();
        }
        return;
    }
    addLog("askForConfirmation ConfirmationType type $type $code");
    VMUPacket packet = VMUPacket.get(code, data: [0]);
    sendVMUPacket(packet, false);
  }
  void sendErrorConfirmation(List<int> data) {
    VMUPacket packet = VMUPacket.get(
      OpCodes.UPGRADE_ERROR_WARN_RES,
      data: data,
    );
    sendVMUPacket(packet, false);
  }
  void disconnectUpgrade() {
    cancelNotification();
    sendUpgradeDisconnect();
  }
  @override
  void onTransferFailed() {
    abortUpgrade();
  }
  @override
  void onTransferFinished() {
    onSuccessfulTransmission();
    mProgressQueue.clear();
  }
  @override
  void onTransferProgress(int acknowledged) {
    if (acknowledged > 0) {
      double percentage = 0;
      while (acknowledged > 0 && mProgressQueue.isNotEmpty) {
        percentage = mProgressQueue.removeFirst();
        acknowledged--;
      }
      if (mIsRWCPEnabled.value) {
        updatePer.value = percentage;
      }
    }
  }
  @override
  bool sendRWCPSegment(List<int> bytes) {
    writeMsgRWCP(bytes);
    return true;
  }
  Future<void> writeData(List<int> data) async {
    if (_device == null || _writeChar == null) {
      addLog('设备或写入特征未设置，无法写入数据');
      return;
    }
    addLog(
      "${DateTime.now()} wenDataWrite start>${StringUtils.byteToHexString(data)}",
    );
    await Future.delayed(const Duration(milliseconds: 100));
    try {
      await _writeChar!.write(data, withoutResponse: false);
      addLog(
        "${DateTime.now()} wenDataWrite end>${StringUtils.byteToHexString(data)}",
      );
    } catch (e) {
      addLog('写入数据失败: $e');
    }
  }
  Future<void> writeMsgRWCP(List<int> data) async {
    if (_device == null || _writeNoResChar == null) {
      addLog('设备或无响应写入特征未设置，无法写入RWCP数据');
      return;
    }
    await Future.delayed(const Duration(milliseconds: 100));
    try {
      await _writeNoResChar!.write(data, withoutResponse: true);
    } catch (e) {
      addLog('写入RWCP数据失败: $e');
    }
  }
  void disconnect() {
    _notifySubscription?.cancel();
    _rwcpSubscription?.cancel();
    _device = null;
    _otaService = null;
    _notifyChar = null;
    _writeChar = null;
    _writeNoResChar = null;
    deviceVersion.value = "";
  }
  /// 协商MTU并设置负载大小
  Future<void> restPayloadSize() async {
    if (_device == null) {
      addLog('设备未设置，无法协商MTU');
      return;
    }
    try {
      // 请求MTU
      int mtu = await _device!.requestMtu(256);
      if (!mIsRWCPEnabled.value) {
        mtu = 23; // 如果不使用RWCP，使用默认值
      }
      int dataSize = mtu - 3;
      mPayloadSizeMax = dataSize - 4;
      addLog("协商mtu $mtu mPayloadSizeMax $mPayloadSizeMax");
    } catch (e) {
      addLog('协商MTU失败: $e');
      mPayloadSizeMax = 16;
    }
  }
  void addLog(String s) {
    debugPrint("wenTest " + s);
    logText.value += s + "\n";
  }
}

================
File: lib/device/d900/d900_device_manager.dart
================
import 'dart:async';
import 'dart:ffi';
import 'package:topping_ble_control/device/topping_device_manager.dart';
import 'package:topping_ble_control/registry/current_connecting_device.dart';
import 'package:topping_ble_control/registry/device_data_manager.dart';
import '../../event/connection_state_event.dart';
import '../../model/bluetooth/ble_device.dart';
import '../../model/d900/d900_callback.dart';
import '../../model/enums/ble_connection_state.dart';
import '../../model/ffi/ffi_d900_settings.dart';
import '../../utils/log_util.dart';
import 'd900_device_bindings.dart';
class D900DeviceManager extends ToppingDeviceManager {
  late final StreamController<int> usbSelectController;
  late final StreamController<bool> usbDsdEnabledController;
  late final StreamController<int> iisPhaseController;
  late final StreamController<int> iisChannelController;
  Stream<int> get usbSelect => usbSelectController.stream;
  Stream<bool> get usbDsdEnabled => usbDsdEnabledController.stream;
  Stream<int> get iisPhase => iisPhaseController.stream;
  Stream<int> get iisChannel => iisChannelController.stream;
  D900DeviceManager() {
    initializeControllers();
    usbSelectController = StreamController<int>.broadcast();
    usbDsdEnabledController = StreamController<bool>.broadcast();
    iisPhaseController = StreamController<int>.broadcast();
    iisChannelController = StreamController<int>.broadcast();
    initializeBleListeners();
    _initializeBindings();
  }
  @override
  D900DeviceBindings get deviceBindings => D900DeviceBindings.instance;
  @override
  String get deviceTypeName => "D900";
  @override
  void connectNative(int deviceHandle) {
    D900DeviceBindings.instance.connect(deviceHandle);
    Log.i("D900 底层连接函数调用完成, 时间戳: ${DateTime.now().millisecondsSinceEpoch}");
  }
  void _initializeBindings() {
    D900DeviceBindings.instance.initialize(
      D900Callback(
        onScanResults: _handleScanResults,
        onScanFailed: _handleScanFailed,
        onStateChange: _handleStateChange,
        onVerifyResult: _handleVerifyResult,
        onPowerChange: _handlePowerChange,
        onDeviceNameChange: _handleDeviceNameChange,
        onDeviceSettingsResponse: _handleSettingsResponse,
        onVolumeChange: _handleVolumeChange,
        onMuteChange: _handleMuteChange,
        onInputTypeChange: _handleInputTypeChange,
        onOutputTypeChange: _handleOutputTypeChange,
        onDisplayModeChange: _handleDisplayModeChange,
        onThemeChange: _handleThemeChange,
        onPowerTriggerChange: _handlePowerTriggerChange,
        onBalanceChange: _handleBalanceChange,
        onAudioBluetoothChange: _handleAudioBluetoothChange,
        onBluetoothAptxChange: _handleBluetoothAptxChange,
        onRemoteEnabledChange: _handleRemoteEnabledChange,
        onMultifunctionKeyChange: _handleMultifunctionKeyChange,
        onUsbModeChange: _handleUsbModeChange,
        onScreenBrightnessChange: _handleScreenBrightnessChange,
        onLanguageChange: _handleLanguageChange,
        onSamplingRateChange: _handleSamplingRateChange,
        onUsbSelectChange: _handleUsbSelectChange,
        onUsbDsdEnabledChange: _handleUsbDsdEnabledChange,
        onIisPhaseChange: _handleIisPhaseChange,
        onIisChannelChange: _handleIisChannelChange,
      ),
    );
  }
  void _handleScanResults(List<dynamic> nativeResults) {
    Log.i("D900: 处理扫描结果: count=${nativeResults.length}");
  }
  void _handleScanFailed(int errorCode) {
    Log.i("D900: 处理扫描失败: $errorCode");
  }
  void _handleStateChange(int state) {
    BleConnectionState connectionState = BleConnectionState.fromValue(state);
    int? handle = CurrentConnectingDevice().handle;
    if (handle == null) return;
    String? deviceName;
    BleDevice? device = DeviceDataManager().getDeviceByHandle(handle);
    deviceName = device?.name;
    Log.i(
      "D900: 收到状态变更: ${connectionState.toString()}, 设备句柄: $handle, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    if (connectionState == BleConnectionState.connectedUnsafe) {
      verify();
      return;
    }
    final event = ConnectionStateEvent(
      connectionState,
      handle,
      deviceName: deviceName,
    );
    if (!deviceStateController.isClosed) {
      deviceStateController.add(event);
    }
  }
  void _handleVerifyResult(int type) {
    Log.i("D900: 收到验证结果: $type, 时间戳: ${DateTime.now().millisecondsSinceEpoch}");
    safeAddEvent(verifyResultController, mapVerifyTypeToEnum(type));
  }
  void _handlePowerChange(bool isOn) {
    Log.i("D900: 处理电源状态变化: $isOn");
    safeAddEvent(powerStateController, isOn);
  }
  void _handleDeviceNameChange(String name) {
    Log.i("D900: 处理设备名称变化: $name");
    safeAddEvent(deviceNameController, name);
  }
  void _handleSettingsResponse(Pointer<FFID900Settings> ffiSettingsPtr) {
    Log.i("D900: 处理设备设置响应");
    if (ffiSettingsPtr == nullptr) {
      Log.e("D900: 处理设备设置响应失败：接收到的指针为空");
      return;
    }
    try {
      List<int> nameBytes = [];
      for (int i = 0; i < 32; i++) {
        int byte = ffiSettingsPtr.ref.device_name[i];
        if (byte == 0) break;
        nameBytes.add(byte);
      }
      final deviceName = String.fromCharCodes(nameBytes);
      final volume = ffiSettingsPtr.ref.volume;
      final mute = ffiSettingsPtr.ref.is_mute != 0;
      final inputType = ffiSettingsPtr.ref.input_type;
      final outputType = ffiSettingsPtr.ref.output_type;
      final displayMode = ffiSettingsPtr.ref.display_mode;
      final theme = ffiSettingsPtr.ref.theme;
      final powerTrigger = ffiSettingsPtr.ref.power_trigger;
      final balance = ffiSettingsPtr.ref.balance;
      const filterType = 0;
      const decodeMode = 0;
      final audioBluetooth = ffiSettingsPtr.ref.audio_bt_enable != 0;
      final bluetoothAptx = ffiSettingsPtr.ref.aptx_enable != 0;
      final remoteEnabled = ffiSettingsPtr.ref.remote_enable != 0;
      final multifunctionKey = ffiSettingsPtr.ref.multifunction_key;
      final usbMode = ffiSettingsPtr.ref.usb_mode;
      final screenBrightness = ffiSettingsPtr.ref.screen_brightness;
      final language = ffiSettingsPtr.ref.language;
      final sampling = ffiSettingsPtr.ref.sampling;
      final usbSelect = ffiSettingsPtr.ref.usb_select;
      final usbDsdEnabled = ffiSettingsPtr.ref.usb_dsd_enable != 0;
      final iisPhase = ffiSettingsPtr.ref.iis_phase;
      final iisChannel = ffiSettingsPtr.ref.iis_channel;
      safeAddEvent(deviceNameController, deviceName);
      safeAddEvent(volumeController, volume);
      safeAddEvent(muteController, mute);
      safeAddEvent(inputTypeController, inputType);
      safeAddEvent(outputTypeController, outputType);
      safeAddEvent(displayModeController, displayMode);
      safeAddEvent(themeController, theme);
      safeAddEvent(powerTriggerController, powerTrigger);
      safeAddEvent(balanceController, balance);
      safeAddEvent(filterController, filterType);
      safeAddEvent(decodeModeController, decodeMode);
      safeAddEvent(audioBluetoothController, audioBluetooth);
      safeAddEvent(bluetoothAptxController, bluetoothAptx);
      safeAddEvent(relayController, remoteEnabled);
      safeAddEvent(multifunctionKeyController, multifunctionKey);
      safeAddEvent(usbModeController, usbMode);
      safeAddEvent(screenBrightnessController, screenBrightness);
      safeAddEvent(languageController, language);
      safeAddEvent(samplingRateController, sampling);
      safeAddEvent(usbSelectController, usbSelect);
      safeAddEvent(usbDsdEnabledController, usbDsdEnabled);
      safeAddEvent(iisPhaseController, iisPhase);
      safeAddEvent(iisChannelController, iisChannel);
      Log.i("D900: 设备设置已更新, 音量: $volume, 输入: $inputType, 输出: $outputType");
    } catch (e, s) {
      Log.e("D900: 处理设备设置响应错误: $e\n$s");
    }
  }
  void _handleVolumeChange(int volume) {
    Log.i("D900: 处理音量变化: $volume");
    safeAddEvent(volumeController, volume);
  }
  void _handleMuteChange(bool isMute) {
    Log.i("D900: 处理静音变化: $isMute");
    safeAddEvent(muteController, isMute);
  }
  void _handleInputTypeChange(int type) {
    Log.i("D900: 处理输入类型变化: $type");
    safeAddEvent(inputTypeController, type);
  }
  void _handleOutputTypeChange(int type) {
    Log.i("D900: 处理输出类型变化: $type");
    safeAddEvent(outputTypeController, type);
  }
  void _handleDisplayModeChange(int mode) {
    Log.i("D900: 处理显示模式变化: $mode");
    safeAddEvent(displayModeController, mode);
  }
  void _handleThemeChange(int theme) {
    Log.i("D900: 处理主题变化: $theme");
    safeAddEvent(themeController, theme);
  }
  void _handlePowerTriggerChange(int trigger) {
    Log.i("D900: 处理电源触发变化: $trigger");
    safeAddEvent(powerTriggerController, trigger);
  }
  void _handleBalanceChange(int balance) {
    Log.i("D900: 处理平衡变化: $balance");
    safeAddEvent(balanceController, balance);
  }
  void _handleAudioBluetoothChange(bool enabled) {
    Log.i("D900: 处理音频蓝牙变化: $enabled");
    safeAddEvent(audioBluetoothController, enabled);
  }
  void _handleBluetoothAptxChange(bool enabled) {
    Log.i("D900: 处理蓝牙aptX变化: $enabled");
    safeAddEvent(bluetoothAptxController, enabled);
  }
  void _handleRemoteEnabledChange(bool enabled) {
    Log.i("D900: 处理远程启用变化: $enabled");
    safeAddEvent(relayController, enabled);
  }
  void _handleMultifunctionKeyChange(int key) {
    Log.i("D900: 处理多功能键变化: $key");
    safeAddEvent(multifunctionKeyController, key);
  }
  void _handleUsbModeChange(int mode) {
    Log.i("D900: 处理USB模式变化: $mode");
    safeAddEvent(usbModeController, mode);
  }
  void _handleScreenBrightnessChange(int brightness) {
    Log.i("D900: 处理屏幕亮度变化: $brightness");
    safeAddEvent(screenBrightnessController, brightness);
  }
  void _handleLanguageChange(int language) {
    Log.i("D900: 处理语言变化: $language");
    safeAddEvent(languageController, language);
  }
  void _handleSamplingRateChange(int rate) {
    Log.i("D900: 处理采样率变化: $rate");
    safeAddEvent(samplingRateController, rate);
  }
  void _handleUsbSelectChange(int select) {
    Log.i("D900: 处理USB选择变化: $select");
    safeAddEvent(usbSelectController, select);
  }
  void _handleUsbDsdEnabledChange(bool enabled) {
    Log.i("D900: 处理USB DSD启用变化: $enabled");
    safeAddEvent(usbDsdEnabledController, enabled);
  }
  void _handleIisPhaseChange(int phase) {
    Log.i("D900: 处理IIS相位变化: $phase");
    safeAddEvent(iisPhaseController, phase);
  }
  void _handleIisChannelChange(int channel) {
    Log.i("D900: 处理IIS通道变化: $channel");
    safeAddEvent(iisChannelController, channel);
  }
  @override
  void disconnect() {
    Log.i("D900: 断开连接");
    D900DeviceBindings.instance.disconnect();
  }
  @override
  void verify() {
    Log.i("D900: 开始验证设备");
    D900DeviceBindings.instance.verify();
  }
  @override
  void resetSettings() {
    Log.i("D900: 重置设置");
    D900DeviceBindings.instance.resetSettings();
    triggerResetSettings();
  }
  @override
  void restoreFactorySettings() {
    Log.i("D900: 恢复出厂设置");
    D900DeviceBindings.instance.restoreFactorySettings();
    triggerRestoreFactorySettings();
  }
  @override
  void getDeviceSettings() {
    Log.i("D900: 获取设备设置");
    D900DeviceBindings.instance.requestSettings();
  }
  @override
  void setPower(bool isOn) {
    Log.i("D900: 设置电源: $isOn");
    D900DeviceBindings.instance.powerOn(isOn);
  }
  @override
  void setVolume(int volume) {
    Log.i("D900: 设置音量: $volume");
    D900DeviceBindings.instance.setVolume(volume);
  }
  @override
  void setMute(bool isMute) {
    Log.i("D900: 设置静音: $isMute");
    D900DeviceBindings.instance.setMute(isMute);
  }
  @override
  void setInputType(int type) {
    Log.i("D900: 设置输入类型: $type");
    D900DeviceBindings.instance.setInputType(type);
  }
  @override
  void setOutputType(int type) {
    Log.i("D900: 设置输出类型: $type");
    D900DeviceBindings.instance.setOutputType(type);
  }
  @override
  void setDisplayMode(int mode) {
    Log.i("D900: 设置显示模式: $mode");
    D900DeviceBindings.instance.setDisplayMode(mode);
  }
  @override
  void setTheme(int theme) {
    Log.i("D900: 设置主题: $theme");
    D900DeviceBindings.instance.setTheme(theme);
  }
  @override
  void setPowerTrigger(int trigger) {
    Log.i("D900: 设置电源触发器: $trigger");
    D900DeviceBindings.instance.setPowerTrigger(trigger);
  }
  @override
  void setBalance(int balance) {
    Log.i("D900: 设置平衡: $balance");
    D900DeviceBindings.instance.setRightBalance(balance);
  }
  @override
  void setFilter(int filter) {
    Log.i("D900: 设置滤波器（D900不支持）: $filter");
  }
  @override
  void setDecodeMode(int mode) {
    Log.i("D900: 设置解码模式（D900不支持）: $mode");
  }
  @override
  void enableAudioBluetooth(bool enable) {
    Log.i("D900: 启用音频蓝牙: $enable");
    D900DeviceBindings.instance.enableAudioBluetooth(enable);
  }
  @override
  void enableBluetoothAptx(bool enable) {
    Log.i("D900: 启用蓝牙aptX: $enable");
    D900DeviceBindings.instance.enableBluetoothAptx(enable);
  }
  @override
  void enableRelay(bool enable) {
    Log.i("D900: 启用继电器: $enable");
    D900DeviceBindings.instance.enableRelay(enable);
  }
  @override
  void setMultifunctionKey(int key) {
    Log.i("D900: 设置多功能键: $key");
    D900DeviceBindings.instance.setMultifunctionKey(key);
  }
  @override
  void setUsbMode(int mode) {
    Log.i("D900: 设置USB模式: $mode");
    D900DeviceBindings.instance.setUsbMode(mode);
  }
  @override
  void setScreenBrightness(int brightness) {
    Log.i("D900: 设置屏幕亮度: $brightness");
    D900DeviceBindings.instance.setScreenBrightness(brightness);
  }
  @override
  void setLanguage(int language) {
    Log.i("D900: 设置语言: $language");
    D900DeviceBindings.instance.setLanguage(language);
  }
  @override
  void closeAllStreams() {
    super.closeAllStreams();
    usbSelectController.close();
    usbDsdEnabledController.close();
    iisPhaseController.close();
    iisChannelController.close();
  }
  @override
  void setUsbType(int type) {
    Log.i("D900: 设置USB类型: $type");
    D900DeviceBindings.instance.setUsbSelect(type);
  }
  @override
  void enableUsbDsdPassthrough(bool enable) {
    Log.i("D900: 启用USB DSD直通: $enable");
    D900DeviceBindings.instance.enableUsbDsd(enable);
  }
  @override
  void setIisDsdChannel(int channel) {
    Log.i("D900: 设置IIS DSD通道: $channel");
    D900DeviceBindings.instance.setIisChannel(channel);
  }
  @override
  void connect(int deviceHandle) {
    Log.i("D900: 连接到设备, 句柄: $deviceHandle");
    connectNative(deviceHandle);
  }
  @override
  void powerOn(bool isOn) {
    setPower(isOn);
  }
  @override
  BleDevice? getConnectedDevice() {
    int? handle = CurrentConnectingDevice().handle;
    if (handle == null) return null;
    return DeviceDataManager().getDeviceByHandle(handle);
  }
  @override
  Future<void> reset() async {
    Log.i("D900: 重置设备状态");
    if (CurrentConnectingDevice().handle != null) {
      disconnect();
    }
    await Future.delayed(const Duration(milliseconds: 100));
  }
  @override
  void enableHeadphone(bool enable) {
    Log.w("D900: 不支持耳机功能");
  }
  @override
  void setHeadphoneGain(int gainType) {
    Log.w("D900: 不支持耳机增益设置");
  }
  @override
  void requestSettings() {
    Log.i("D900: 请求设备设置");
    D900DeviceBindings.instance.requestSettings();
  }
  @override
  void setDeviceName(String name) {
    Log.i("D900: 设置设备名称: $name");
    D900DeviceBindings.instance.setDeviceName(name);
  }
  @override
  void setIisPhase(int phase) {
    Log.i("D900: 设置IIS相位: $phase");
    D900DeviceBindings.instance.setIisPhase(phase);
  }
  @override
  void setIisChannel(int channel) {
    Log.i("D900: 设置IIS通道: $channel");
    D900DeviceBindings.instance.setIisChannel(channel);
  }
  @override
  void deviceSpecificCommand(int command, List<int> data) {
    Log.i("D900: 设备特定命令: $command, 数据长度: ${data.length}");
  }
}

================
File: lib/device/dx5/dx5ii_device_manager.dart
================
import 'dart:async';
import 'dart:ffi';
import 'package:topping_ble_control/registry/current_connecting_device.dart';
import '../../event/connection_state_event.dart';
import '../../model/bluetooth/ble_device.dart';
import '../../model/dx5ii/dx5ii_callback.dart';
import '../../model/dx5ii/dx5ii_settings.dart';
import '../../model/enums/ble_connection_state.dart';
import '../../model/ffi/ffi_dx5ii_settings.dart';
import '../../registry/device_data_manager.dart';
import '../../utils/log_util.dart';
import '../topping_device_manager.dart';
import 'dx5ii_device_bindings.dart';
class Dx5iiDeviceManager extends ToppingDeviceManager {
  Dx5iiDeviceManager() {
    initializeControllers();
    initializeBleListeners();
    _initializeBindings();
  }
  @override
  Dx5iiDeviceBindings get deviceBindings => Dx5iiDeviceBindings.instance;
  @override
  String get deviceTypeName => "DX5II";
  @override
  void connectNative(int deviceHandle) {
    Dx5iiDeviceBindings.instance.connect(deviceHandle);
    Log.i("DX5II 底层连接函数调用完成, 时间戳: ${DateTime.now().millisecondsSinceEpoch}");
  }
  void _initializeBindings() {
    Dx5iiDeviceBindings.instance.initialize(
      Dx5iiCallback(
        onScanResults: _handleScanResults,
        onScanFailed: _handleScanFailed,
        onStateChange: _handleStateChange,
        onVerifyResult: _handleVerifyResult,
        onPowerChange: _handlePowerChange,
        onDeviceNameChange: _handleDeviceNameChange,
        onDeviceSettingsResponse: _handleSettingsResponse,
        onVolumeChange: _handleVolumeChange,
        onMuteChange: _handleMuteChange,
        onInputTypeChange: _handleInputTypeChange,
        onOutputTypeChange: _handleOutputTypeChange,
        onHeadphoneEnabledChange: _handleHeadphoneEnabledChange,
        onHeadphoneGainChange: _handleHeadphoneGainChange,
        onDisplayModeChange: _handleDisplayModeChange,
        onThemeChange: _handleThemeChange,
        onPowerTriggerChange: _handlePowerTriggerChange,
        onBalanceChange: _handleBalanceChange,
        onPcmFilterChange: _handlePcmFilterChange,
        onDecodeModeChange: _handleDecodeModeChange,
        onAudioBluetoothChange: _handleAudioBluetoothChange,
        onBluetoothAptxChange: _handleBluetoothAptxChange,
        onRemoteEnabledChange: _handleRelayChange,
        onMultifunctionKeyChange: _handleMultifunctionKeyChange,
        onUsbModeChange: _handleUsbModeChange,
        onScreenBrightnessChange: _handleScreenBrightnessChange,
        onLanguageChange: _handleLanguageChange,
        onSamplingRateChange: _handleSamplingRateChange,
      ),
    );
  }
  void _handleScanResults(List<dynamic> nativeResults) {
    Log.i("DX5II: 处理扫描结果: count=${nativeResults.length}");
  }
  void _handleScanFailed(int errorCode) {
    Log.i("DX5II: 处理扫描失败: $errorCode");
  }
  void _handleStateChange(int state) {
    BleConnectionState connectionState = BleConnectionState.fromValue(state);
    int? handle = CurrentConnectingDevice().handle;
    if (handle == null) return;
    String? deviceName;
    BleDevice? device = DeviceDataManager().getDeviceByHandle(handle);
    deviceName = device?.name;
    Log.i(
      "DX5II: 收到状态变更: ${connectionState.toString()}, 设备句柄: $handle, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    if (connectionState == BleConnectionState.connectedUnsafe) {
      verify();
      return;
    }
    final event = ConnectionStateEvent(
      connectionState,
      handle,
      deviceName: deviceName,
    );
    if (!deviceStateController.isClosed) {
      deviceStateController.add(event);
    }
  }
  void _handleVerifyResult(int type) {
    Log.i(
      "DX5II: 收到验证结果: $type, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    safeAddEvent(verifyResultController, mapVerifyTypeToEnum(type));
  }
  void _handlePowerChange(bool isOn) {
    Log.i("DX5II: 处理电源状态变化: $isOn");
    safeAddEvent(powerStateController, isOn);
  }
  void _handleDeviceNameChange(String name) {
    Log.i("DX5II: 处理设备名称变化: $name");
    safeAddEvent(deviceNameController, name);
  }
  void _handleSettingsResponse(Pointer<FFIDx5iiSettings> ffiSettingsPtr) {
    Log.i("DX5II: 处理设备设置响应");
    if (ffiSettingsPtr == nullptr) {
      Log.e("DX5II: 处理设备设置响应失败：接收到的指针为空");
      return;
    }
    try {
      final settings = Dx5iiSettings.fromFFI(ffiSettingsPtr);
      int displayVolume = -settings.volume;
      safeAddEvent(deviceNameController, settings.deviceName);
      safeAddEvent(volumeController, displayVolume);
      safeAddEvent(muteController, settings.mute);
      safeAddEvent(inputTypeController, settings.inputType);
      safeAddEvent(outputTypeController, settings.outputType);
      safeAddEvent(headphoneEnabledController, settings.headphoneEnabled);
      safeAddEvent(headphoneGainController, settings.headphoneGain);
      safeAddEvent(displayModeController, settings.displayMode);
      safeAddEvent(themeController, settings.theme);
      safeAddEvent(powerTriggerController, settings.powerTrigger);
      safeAddEvent(balanceController, settings.balance);
      safeAddEvent(filterController, settings.pcmFilter);
      safeAddEvent(decodeModeController, settings.decodeMode);
      safeAddEvent(audioBluetoothController, settings.audioBluetooth);
      safeAddEvent(bluetoothAptxController, settings.bluetoothAptx);
      safeAddEvent(relayController, settings.remoteEnabled);
      safeAddEvent(multifunctionKeyController, settings.multifunctionKey);
      safeAddEvent(usbModeController, settings.usbMode);
      safeAddEvent(screenBrightnessController, settings.screenBrightness);
      safeAddEvent(languageController, settings.language);
      safeAddEvent(samplingRateController, settings.sampling);
      safeAddEvent(settingsController, settings);
      Log.i(
        "DX5II: 设备设置已更新, 音量: ${settings.volume}, 输入: ${settings.inputType}, 输出: ${settings.outputType}",
      );
    } catch (e, s) {
      Log.e("DX5II: 处理设备设置时发生严重错误: $e\nStack trace:\n$s");
    }
  }
  void _handleVolumeChange(int volume) {
    Log.i("DX5II: 处理音量变化: $volume");
    int displayVolume = -volume;
    safeAddEvent(volumeController, displayVolume);
  }
  void _handleMuteChange(bool isMute) {
    Log.i("DX5II: 处理静音变化: $isMute");
    safeAddEvent(muteController, isMute);
  }
  void _handleInputTypeChange(int inputType) {
    Log.i("DX5II: 处理输入类型变化: $inputType");
    safeAddEvent(inputTypeController, inputType);
  }
  void _handleOutputTypeChange(int outputType) {
    Log.i("DX5II: 处理输出类型变化: $outputType");
    safeAddEvent(outputTypeController, outputType);
  }
  void _handleHeadphoneEnabledChange(bool isEnabled) {
    Log.i("DX5II: 处理耳机启用变化: $isEnabled");
    safeAddEvent(headphoneEnabledController, isEnabled);
  }
  void _handleHeadphoneGainChange(int gainType) {
    Log.i("DX5II: 处理耳机增益变化: $gainType");
    safeAddEvent(headphoneGainController, gainType);
  }
  void _handleDisplayModeChange(int displayMode) {
    Log.i("DX5II: 处理显示模式变化: $displayMode");
    safeAddEvent(displayModeController, displayMode);
  }
  void _handleThemeChange(int theme) {
    Log.i("DX5II: 处理主题变化: $theme");
    safeAddEvent(themeController, theme);
  }
  void _handlePowerTriggerChange(int triggerType) {
    Log.i("DX5II: 处理电源触发器变化: $triggerType");
    safeAddEvent(powerTriggerController, triggerType);
  }
  void _handleBalanceChange(int balance) {
    Log.i("DX5II: 处理声道平衡变化: $balance");
    safeAddEvent(balanceController, balance);
  }
  void _handlePcmFilterChange(int filterType) {
    Log.i("DX5II: 处理PCM滤波器变化: $filterType");
    safeAddEvent(filterController, filterType);
  }
  void _handleDecodeModeChange(int decodeMode) {
    Log.i("DX5II: 处理解码模式变化: $decodeMode");
    safeAddEvent(decodeModeController, decodeMode);
  }
  void _handleAudioBluetoothChange(bool enable) {
    Log.i("DX5II: 处理音频蓝牙变化: $enable");
    safeAddEvent(audioBluetoothController, enable);
  }
  void _handleBluetoothAptxChange(bool enable) {
    Log.i("DX5II: 处理蓝牙APTX变化: $enable");
    safeAddEvent(bluetoothAptxController, enable);
  }
  void _handleRelayChange(bool enable) {
    Log.i("DX5II: 处理中继变化: $enable");
    safeAddEvent(relayController, enable);
  }
  void _handleMultifunctionKeyChange(int keyType) {
    Log.i("DX5II: 处理多功能键变化: $keyType");
    safeAddEvent(multifunctionKeyController, keyType);
  }
  void _handleUsbModeChange(int usbMode) {
    Log.i("DX5II: 处理USB模式变化: $usbMode");
    safeAddEvent(usbModeController, usbMode);
  }
  void _handleScreenBrightnessChange(int brightnessType) {
    Log.i("DX5II: 处理屏幕亮度变化: $brightnessType");
    safeAddEvent(screenBrightnessController, brightnessType);
  }
  void _handleLanguageChange(int language) {
    Log.i("DX5II: 处理语言变化: $language");
    safeAddEvent(languageController, language);
  }
  void _handleSamplingRateChange(int samplingRate) {
    Log.i("DX5II: 处理采样率变化: $samplingRate");
    safeAddEvent(samplingRateController, samplingRate);
  }
  @override
  void powerOn(bool isOn) {
    Log.i("DX5II: ${isOn ? '开机' : '关机'}");
    Dx5iiDeviceBindings.instance.powerOn(isOn);
  }
  @override
  void setVolume(int volume) {
    Log.i("DX5II: 设置音量: $volume");
    Dx5iiDeviceBindings.instance.setVolume(volume);
  }
  @override
  void setMute(bool isMute) {
    Log.i("DX5II: ${isMute ? '静音' : '取消静音'}");
    Dx5iiDeviceBindings.instance.setMute(isMute);
  }
  @override
  void setInputType(int inputType) {
    Log.i("DX5II: 设置输入类型: $inputType");
    Dx5iiDeviceBindings.instance.setInputType(inputType);
  }
  @override
  void setOutputType(int outputType) {
    Log.i("DX5II: 设置输出类型: $outputType");
    Dx5iiDeviceBindings.instance.setOutputType(outputType);
  }
  @override
  void enableHeadphone(bool enable) {
    Log.i("DX5II: ${enable ? '启用' : '禁用'}耳机");
    Dx5iiDeviceBindings.instance.enableHeadphone(enable);
  }
  @override
  void setHeadphoneGain(int gainType) {
    Log.i("DX5II: 设置耳机增益: $gainType");
    Dx5iiDeviceBindings.instance.setHeadphoneGain(gainType);
  }
  @override
  void setDeviceName(String name) {
    Log.i("DX5II: 设置设备名称: $name");
    Dx5iiDeviceBindings.instance.setDeviceName(name);
  }
  @override
  void verify() {
    Log.i("DX5II: 验证设备");
    Dx5iiDeviceBindings.instance.verify();
  }
  @override
  void requestSettings() {
    Log.i("DX5II: 请求设备设置");
    Dx5iiDeviceBindings.instance.requestSettings();
  }
  void refresh() {
    Log.i("DX5II: 刷新设备状态");
    requestSettings();
  }
  @override
  void setDisplayMode(int displayMode) {
    Log.i("DX5II: 设置显示模式: $displayMode");
    Dx5iiDeviceBindings.instance.setDisplayMode(displayMode);
  }
  @override
  void setTheme(int theme) {
    Log.i("DX5II: 设置主题: $theme");
    Dx5iiDeviceBindings.instance.setTheme(theme);
  }
  @override
  void setPowerTrigger(int triggerType) {
    Log.i("DX5II: 设置电源触发器: $triggerType");
    Dx5iiDeviceBindings.instance.setPowerTrigger(triggerType);
  }
  @override
  void setBalance(int balance) {
    Log.i("DX5II: 设置平衡: $balance");
    Dx5iiDeviceBindings.instance.setBalance(balance);
  }
  @override
  void setFilter(int filterType) {
    Log.i("DX5II: 设置滤波器: $filterType");
    Dx5iiDeviceBindings.instance.setPcmFilter(filterType);
  }
  @override
  void setDecodeMode(int decodeMode) {
    Log.i("DX5II: 设置解码模式: $decodeMode");
    Dx5iiDeviceBindings.instance.setDecodeMode(decodeMode);
  }
  @override
  void enableAudioBluetooth(bool enable) {
    Log.i("DX5II: ${enable ? '启用' : '禁用'}音频蓝牙");
    Dx5iiDeviceBindings.instance.enableAudioBluetooth(enable);
  }
  @override
  void enableBluetoothAptx(bool enable) {
    Log.i("DX5II: ${enable ? '启用' : '禁用'}蓝牙APTX");
    Dx5iiDeviceBindings.instance.enableBluetoothAptx(enable);
  }
  @override
  void enableRelay(bool enable) {
    Log.i("DX5II: ${enable ? '启用' : '禁用'}中继");
    Dx5iiDeviceBindings.instance.enableRelay(enable);
  }
  @override
  void setMultifunctionKey(int keyType) {
    Log.i("DX5II: 设置多功能键: $keyType");
    Dx5iiDeviceBindings.instance.setMultifunctionKey(keyType);
  }
  @override
  void setUsbMode(int usbMode) {
    Log.i("DX5II: 设置USB模式: $usbMode");
    Dx5iiDeviceBindings.instance.setUsbMode(usbMode);
  }
  @override
  void setScreenBrightness(int brightnessType) {
    Log.i("DX5II: 设置屏幕亮度: $brightnessType");
    Dx5iiDeviceBindings.instance.setScreenBrightness(brightnessType);
  }
  @override
  void setLanguage(int language) {
    Log.i("DX5II: 设置语言: $language");
    Dx5iiDeviceBindings.instance.setLanguage(language);
  }
  @override
  void resetSettings() {
    Log.i("DX5II: 重置设置");
    Dx5iiDeviceBindings.instance.resetSettings();
    triggerResetSettings();
  }
  @override
  void restoreFactorySettings() {
    Log.i("DX5II: 恢复出厂设置");
    Dx5iiDeviceBindings.instance.restoreFactorySettings();
    triggerRestoreFactorySettings();
  }
  @override
  BleDevice? getConnectedDevice() {
    int? handle = CurrentConnectingDevice().handle;
    if (handle == null) return null;
    return DeviceDataManager().getDeviceByHandle(handle);
  }
  @override
  void connect(int deviceHandle) {
    Log.i("DX5II: 连接设备, 句柄: $deviceHandle");
    connectDevice(deviceHandle);
  }
  @override
  void disconnect() {
    Log.i("DX5II: 断开设备连接");
    Dx5iiDeviceBindings.instance.disconnect();
  }
  @override
  Future<void> reset() async {
    Log.i("DX5II: 重置设备状态");
    if (CurrentConnectingDevice().handle != null) {
      disconnect();
    }
    await Future.delayed(const Duration(milliseconds: 100));
  }
}

================
File: lib/device/device_factory.dart
================
import 'dart:async';
import 'package:topping_ble_control/utils/log_util.dart';
import 'package:rxdart/rxdart.dart';
import '../registry/device_data_manager.dart';
import 'dx5/dx5ii_device_manager.dart';
import 'd900/d900_device_manager.dart';
import '../interfaces/device_events.dart';
import '../interfaces/device_operations.dart';
import '../model/enums/device_mode_type.dart';
import '../model/bluetooth/ble_device.dart';
import '../model/enums/ble_connection_state.dart';
import '../event/connection_state_event.dart';
import '../bluetooth/ble_operation_manager.dart';
import '../bluetooth/ffi/ble_ffi_initializer.dart';
class BleDiscoveryState {
  final List<BleDevice> devices;
  final bool isScanning;
  BleDiscoveryState({required this.devices, required this.isScanning});
  static final initial = BleDiscoveryState(devices: [], isScanning: false);
}
class DeviceFactory {
  static final DeviceFactory _instance = DeviceFactory._internal();
  final BleOperationManager _bleManager = BleOperationManager();
  DeviceManager? _currentDeviceManager;
  final _connectionStateController =
      StreamController<ConnectionStateEvent>.broadcast();
  Stream<BleDiscoveryState>? _discoveryStateStream;
  final Map<int, BleDevice> _foundDeviceCache = {};
  BleOperationManager get bleManager => _bleManager;
  DeviceManager? get currentDeviceManager => _currentDeviceManager;
  Stream<ConnectionStateEvent> get connectionState =>
      _connectionStateController.stream;
  Stream<BleDiscoveryState> get discoveryState {
    _discoveryStateStream ??=
        Rx.combineLatest2(
              _bleManager.scanner.scanResults.startWith([]),
              _bleManager.scanner.isScanning.startWith(false),
              (List<BleDevice> devices, bool isScanning) =>
                  BleDiscoveryState(devices: devices, isScanning: isScanning),
            )
            .asBroadcastStream();
    return _discoveryStateStream!;
  }
  factory DeviceFactory() => _instance;
  DeviceFactory._internal();
  DeviceManager getDeviceManager(DeviceModeType deviceType) {
    switch (deviceType) {
      case DeviceModeType.dx5:
        return Dx5iiDeviceManager();
      case DeviceModeType.dx9:
        return D900DeviceManager();
      case DeviceModeType.unknown:
        return Dx5iiDeviceManager();
    }
  }
  DeviceManager getDeviceManagerForDevice(BleDevice device) {
    return getDeviceManager(device.deviceType);
  }
  static bool _isFirstRun = true;
  Future<void> startScan({
    List<DeviceModeType> deviceTypes = const [
      DeviceModeType.dx5,
      DeviceModeType.dx9,
    ],
    bool? useNativeScanner,
    int retryCount = 1,
  }) async {
    _foundDeviceCache.clear();
    bool actualUseNativeScanner = useNativeScanner ?? !_isFirstRun;
    if (_isFirstRun) {
      Log.i("DeviceFactory: 第一次运行，默认使用原生扫描器");
      _isFirstRun = false;
    }
    try {
      if (actualUseNativeScanner) {
        await BleFfiInitializer().ensureInitialized();
      }
      await _bleManager.startScan(
        deviceTypes: deviceTypes,
        useNativeScanner: actualUseNativeScanner,
      );
      Log.i("DeviceFactory: 使用${actualUseNativeScanner ? '原生' : 'Flutter'}扫描器开始扫描设备");
    } catch (e) {
      Log.e("DeviceFactory: 扫描失败: $e");
      if (actualUseNativeScanner && retryCount > 0) {
        Log.i("DeviceFactory: 原生扫描器失败，尝试再次使用原生扫描器");
        await startScan(
          deviceTypes: deviceTypes,
          useNativeScanner: true,
          retryCount: retryCount - 1,
        );
      } else {
        rethrow;
      }
    }
  }
  Future<void> stopScan() async {
    await _bleManager.stopScan();
  }
  Future<void> connectDevice(BleDevice device) async {
    await disconnectCurrentDevice();
    _currentDeviceManager = getDeviceManagerForDevice(device);
    _setupDeviceStateListener();
    _currentDeviceManager!.connect(device.nativeHandle);
  }
  Future<void> disconnectCurrentDevice() async {
    if (_currentDeviceManager != null) {
      try {
        _currentDeviceManager!.disconnect();
        await Future.delayed(const Duration(milliseconds: 500));
        _currentDeviceManager!.dispose();
        _currentDeviceManager = null;
      } catch (e) {
        Log.e("断开连接时出错: $e");
        _currentDeviceManager = null;
      }
    }
  }
  void _setupDeviceStateListener() {
    if (_currentDeviceManager == null) return;
    _currentDeviceManager!.deviceState.listen((event) {
      if (!_connectionStateController.isClosed) {
        _connectionStateController.add(event);
      }
      if (event.state == BleConnectionState.disconnected) {
        _currentDeviceManager?.dispose();
        _currentDeviceManager = null;
      }
      final device = _currentDeviceManager?.getConnectedDevice();
      if (device != null) {
        cacheFoundDevice(device);
      }
    });
  }
  BleDevice? getConnectedDevice() {
    final device = _currentDeviceManager?.getConnectedDevice();
    Log.i("当前连接的设备是 $device");
    if (device != null) {
      cacheFoundDevice(device);
    }
    return device;
  }
  void dispose() {
    disconnectCurrentDevice();
    if (!_connectionStateController.isClosed) {
      _connectionStateController.close();
    }
  }
  void cacheFoundDevice(BleDevice device) {
    _foundDeviceCache[device.nativeHandle] = device;
  }
  Future<BleDevice?> findDeviceByHandle(int handle) async {
    if (_foundDeviceCache.containsKey(handle)) {
      Log.i("DeviceFactory: 在内部缓存中找到句柄 $handle 对应的设备");
      return _foundDeviceCache[handle];
    }
    Log.i("DeviceFactory: 内部缓存未命中，查询 DeviceManager (句柄: $handle)");
    final deviceFromManager = DeviceDataManager().findDevice(handle: handle);
    if (deviceFromManager != null) {
      Log.i(
        "DeviceFactory: 在 DeviceManager 中找到句柄 $handle 对应的设备: ${deviceFromManager.name}",
      );
      return deviceFromManager;
    } else {
      Log.w("DeviceFactory: 在内部缓存和 DeviceManager 中都无法找到句柄 $handle 对应的设备");
      return null;
    }
  }
}
abstract class DeviceManager implements DeviceOperations, DeviceEvents {
  void dispose();
  BleDevice? getConnectedDevice();
  @override
  Future<void> reset();
}

================
File: lib/model/dx5ii/dx5ii_callback.dart
================
import 'dart:ffi';
import 'package:topping_ble_control/model/base/topping_device_callback.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import '../ffi/ffi_dx5ii_settings.dart';
class Dx5iiCallback extends ToppingDeviceCallback {
  final Function(Pointer<FFIDx5iiSettings>) onDeviceSettingsResponse;
  final Function(bool) onHeadphoneEnabledChange;
  final Function(int) onHeadphoneGainChange;
  final Function(int) onPcmFilterChange;
  final Function(int) onDecodeModeChange;
  Dx5iiCallback({
    required super.onScanResults,
    required super.onScanFailed,
    required super.onStateChange,
    required super.onVerifyResult,
    super.onPowerChange,
    super.onDeviceNameChange,
    required this.onDeviceSettingsResponse,
    super.onVolumeChange,
    super.onMuteChange,
    super.onInputTypeChange,
    super.onOutputTypeChange,
    this.onHeadphoneEnabledChange = _defaultOnHeadphoneEnabledChange,
    this.onHeadphoneGainChange = _defaultOnHeadphoneGainChange,
    super.onDisplayModeChange,
    super.onThemeChange,
    super.onPowerTriggerChange,
    super.onBalanceChange,
    this.onPcmFilterChange = _defaultOnPcmFilterChange,
    this.onDecodeModeChange = _defaultOnDecodeModeChange,
    super.onAudioBluetoothChange,
    super.onBluetoothAptxChange,
    super.onRemoteEnabledChange,
    super.onMultifunctionKeyChange,
    super.onUsbModeChange,
    super.onScreenBrightnessChange,
    super.onLanguageChange,
    super.onSamplingRateChange,
  });
  static void _defaultOnHeadphoneEnabledChange(bool enabled) =>
      Log.e("未处理: 耳机启用 $enabled");
  static void _defaultOnHeadphoneGainChange(int gain) =>
      Log.e("未处理: 耳机增益 $gain");
  static void _defaultOnPcmFilterChange(int filter) =>
      Log.e("未处理: PCM滤波器 $filter");
  static void _defaultOnDecodeModeChange(int mode) => Log.e("未处理: 解码模式 $mode");
}

================
File: lib/model/dx5ii/dx5ii_settings.dart
================
import 'dart:ffi';
import 'dart:typed_data';
import 'dart:convert';
import 'package:ffi/ffi.dart';
import 'package:topping_ble_control/model/base/topping_device_settings.dart';
import 'package:topping_ble_control/model/ffi/ffi_dx5ii_settings.dart';
import 'package:topping_ble_control/utils/log_util.dart';
class Dx5iiSettings extends ToppingDeviceSettings {
  final bool headphoneEnabled;
  final int headphoneGain;
  final int pcmFilter;
  final int decodeMode;
  Dx5iiSettings({
    required super.isOn,
    required super.deviceName,
    required super.volume,
    required super.mute,
    required super.inputType,
    required super.outputType,
    required this.headphoneEnabled,
    required this.headphoneGain,
    required super.displayMode,
    required super.theme,
    required super.powerTrigger,
    required super.balance,
    required this.pcmFilter,
    required this.decodeMode,
    required super.audioBluetooth,
    required super.bluetoothAptx,
    required super.remoteEnabled,
    required super.multifunctionKey,
    required super.usbMode,
    required super.screenBrightness,
    required super.language,
    required super.sampling,
  });
  factory Dx5iiSettings.fromFFI(Pointer<FFIDx5iiSettings> ffiSettingsPtr) {
    if (ffiSettingsPtr == nullptr) {
      Log.e("从FFI构造Dx5iiSettings失败：传入的指针为空");
      return _defaultSettings("空指针错误");
    }
    final ffiSettings = ffiSettingsPtr.ref;
    try {
      Log.i("开始从FFI设置构造: ${ffiSettingsPtr.address}");
      final nameBytes = Uint8List(32);
      for (int i = 0; i < 32; i++) {
        nameBytes[i] = ffiSettings.device_name[i];
      }
      String name = ToppingDeviceSettings.readDeviceNameFromFFI(nameBytes);
      int readVolume = 0;
      try {
        readVolume = ffiSettings.volume;
      } catch (e) {
        Log.e("读取volume失败: $e");
      }
      bool readMute = false;
      try {
        readMute = ffiSettings.is_mute != 0;
      } catch (e) {
        Log.e("读取is_mute失败: $e");
      }
      int readInputType = 0;
      try {
        readInputType = ffiSettings.input_type;
      } catch (e) {
        Log.e("读取input_type失败: $e");
      }
      int readOutputType = 0;
      try {
        readOutputType = ffiSettings.output_type;
      } catch (e) {
        Log.e("读取output_type失败: $e");
      }
      bool readHeadphoneEnabled = false;
      try {
        readHeadphoneEnabled = ffiSettings.headphone_enable != 0;
      } catch (e) {
        Log.e("读取headphone_enable失败: $e");
      }
      int readHeadphoneGain = 0;
      try {
        readHeadphoneGain = ffiSettings.headphone_gain;
      } catch (e) {
        Log.e("读取headphone_gain失败: $e");
      }
      int readDisplayMode = 0;
      try {
        readDisplayMode = ffiSettings.display_mode;
      } catch (e) {
        Log.e("读取display_mode失败: $e");
      }
      int readTheme = 0;
      try {
        readTheme = ffiSettings.theme;
      } catch (e) {
        Log.e("读取theme失败: $e");
      }
      int readPowerTrigger = 0;
      try {
        readPowerTrigger = ffiSettings.power_trigger;
      } catch (e) {
        Log.e("读取power_trigger失败: $e");
      }
      int readBalance = 0;
      try {
        readBalance = ffiSettings.balance;
      } catch (e) {
        Log.e("读取balance失败: $e");
      }
      int readPcmFilter = 0;
      try {
        readPcmFilter = ffiSettings.pcm_filter;
      } catch (e) {
        Log.e("读取pcm_filter失败: $e");
      }
      int readDecodeMode = 0;
      try {
        readDecodeMode = ffiSettings.decode_mode;
      } catch (e) {
        Log.e("读取decode_mode失败: $e");
      }
      bool readAudioBluetooth = false;
      try {
        readAudioBluetooth = ffiSettings.audio_bt_enable != 0;
      } catch (e) {
        Log.e("读取audio_bt_enable失败: $e");
      }
      bool readBluetoothAptx = false;
      try {
        readBluetoothAptx = ffiSettings.aptx_enable != 0;
      } catch (e) {
        Log.e("读取aptx_enable失败: $e");
      }
      bool readRemoteEnabled = false;
      try {
        readRemoteEnabled = ffiSettings.remote_enable != 0;
      } catch (e) {
        Log.e("读取remote_enable失败: $e");
      }
      int readMultifunctionKey = 0;
      try {
        readMultifunctionKey = ffiSettings.multifunction_key;
      } catch (e) {
        Log.e("读取multifunction_key失败: $e");
      }
      int readUsbMode = 0;
      try {
        readUsbMode = ffiSettings.usb_mode;
      } catch (e) {
        Log.e("读取usb_mode失败: $e");
      }
      int readScreenBrightness = 0;
      try {
        readScreenBrightness = ffiSettings.screen_brightness;
      } catch (e) {
        Log.e("读取screen_brightness失败: $e");
      }
      int readLanguage = 0;
      try {
        readLanguage = ffiSettings.language;
      } catch (e) {
        Log.e("读取language失败: $e");
      }
      int readSampling = 0;
      try {
        readSampling = ffiSettings.sampling;
      } catch (e) {
        Log.e("读取sampling失败: $e");
      }
      bool readIsOn = false;
      try {
        readIsOn = ffiSettings.is_on != 0;
      } catch (e) {
        Log.e("读取is_on失败: $e");
      }
      return Dx5iiSettings(
        isOn: readIsOn,
        deviceName: name,
        volume: readVolume,
        mute: readMute,
        inputType: readInputType,
        outputType: readOutputType,
        headphoneEnabled: readHeadphoneEnabled,
        headphoneGain: readHeadphoneGain,
        displayMode: readDisplayMode,
        theme: readTheme,
        powerTrigger: readPowerTrigger,
        balance: readBalance,
        pcmFilter: readPcmFilter,
        decodeMode: readDecodeMode,
        audioBluetooth: readAudioBluetooth,
        bluetoothAptx: readBluetoothAptx,
        remoteEnabled: readRemoteEnabled,
        multifunctionKey: readMultifunctionKey,
        usbMode: readUsbMode,
        screenBrightness: readScreenBrightness,
        language: readLanguage,
        sampling: readSampling,
      );
    } catch (e, s) {
      Log.e("从FFI创建Dx5iiSettings时发生严重错误: $e\nStack trace:\n$s");
      return _defaultSettings("构造函数错误");
    }
  }
  static Dx5iiSettings _defaultSettings(String errorContext) {
    Log.w("返回默认Dx5iiSettings，错误上下文: $errorContext");
    return Dx5iiSettings(
      isOn: false,
      deviceName: "默认名称 ($errorContext)",
      volume: -1,
      mute: false,
      inputType: 0,
      outputType: 0,
      headphoneEnabled: false,
      headphoneGain: 0,
      displayMode: 0,
      theme: 0,
      powerTrigger: 0,
      balance: 0,
      pcmFilter: 0,
      decodeMode: 0,
      audioBluetooth: false,
      bluetoothAptx: false,
      remoteEnabled: false,
      multifunctionKey: 0,
      usbMode: 0,
      screenBrightness: 0,
      language: 0,
      sampling: 0,
    );
  }
  Pointer<FFIDx5iiSettings> toFFI() {
    final ffiSettingsPtr = calloc<FFIDx5iiSettings>();
    try {
      final ffiSettings = ffiSettingsPtr.ref;
      final nameBytes = utf8.encode(deviceName);
      final List<int> nameBytesPadded = List.filled(32, 0);
      final lengthToCopy =
          nameBytes.length < 32 ? nameBytes.length : 31;
      for (int i = 0; i < lengthToCopy; i++) {
        nameBytesPadded[i] = nameBytes[i];
      }
      for (int i = 0; i < 32; i++) {
        ffiSettings.device_name[i] = nameBytesPadded[i];
      }
      ffiSettings.is_on = isOn ? 1 : 0;
      ffiSettings.volume = volume;
      ffiSettings.is_mute = mute ? 1 : 0;
      ffiSettings.input_type = inputType;
      ffiSettings.output_type = outputType;
      ffiSettings.headphone_enable = headphoneEnabled ? 1 : 0;
      ffiSettings.headphone_gain = headphoneGain;
      ffiSettings.display_mode = displayMode;
      ffiSettings.theme = theme;
      ffiSettings.power_trigger = powerTrigger;
      ffiSettings.balance = balance;
      ffiSettings.pcm_filter = pcmFilter;
      ffiSettings.decode_mode = decodeMode;
      ffiSettings.audio_bt_enable = audioBluetooth ? 1 : 0;
      ffiSettings.aptx_enable = bluetoothAptx ? 1 : 0;
      ffiSettings.remote_enable = remoteEnabled ? 1 : 0;
      ffiSettings.multifunction_key = multifunctionKey;
      ffiSettings.usb_mode = usbMode;
      ffiSettings.screen_brightness = screenBrightness;
      ffiSettings.language = language;
      ffiSettings.sampling = sampling;
      return ffiSettingsPtr;
    } catch (e) {
      Log.e("转换为FFI设置时出错: $e");
      calloc.free(ffiSettingsPtr);
      rethrow;
    }
  }
  static void freeCreatedFFIPointer(Pointer<FFIDx5iiSettings> ffiSettingsPtr) {
    if (ffiSettingsPtr != nullptr) {
      calloc.free(ffiSettingsPtr);
    }
  }
  @override
  Dx5iiSettings copy() {
    return Dx5iiSettings(
      isOn: isOn,
      deviceName: deviceName,
      volume: volume,
      mute: mute,
      inputType: inputType,
      outputType: outputType,
      headphoneEnabled: headphoneEnabled,
      headphoneGain: headphoneGain,
      displayMode: displayMode,
      theme: theme,
      powerTrigger: powerTrigger,
      balance: balance,
      pcmFilter: pcmFilter,
      decodeMode: decodeMode,
      audioBluetooth: audioBluetooth,
      bluetoothAptx: bluetoothAptx,
      remoteEnabled: remoteEnabled,
      multifunctionKey: multifunctionKey,
      usbMode: usbMode,
      screenBrightness: screenBrightness,
      language: language,
      sampling: sampling,
    );
  }
  @override
  String toString() {
    return super.toString().replaceFirst(
      '}',
      ', '
          'headphoneEnabled: $headphoneEnabled, '
          'headphoneGain: $headphoneGain, '
          'pcmFilter: $pcmFilter, '
          'decodeMode: $decodeMode}',
    );
  }
}

================
File: lib/interfaces/device_operations.dart
================
import '../model/enums/device_mode_type.dart';
abstract class DeviceOperations {
  void startScan({List<DeviceModeType> deviceTypes});
  void stopScan();
  void connect(int deviceHandle);
  void disconnect();
  void verify();
  void powerOn(bool isOn);
  void setDeviceName(String name);
  void setVolume(int volume);
  void setMute(bool isMute);
  void setInputType(int inputType);
  void setOutputType(int outputType);
  void enableHeadphone(bool enable);
  void setHeadphoneGain(int gainType);
  void setDisplayMode(int displayMode);
  void setTheme(int theme);
  void setPowerTrigger(int triggerType);
  void setBalance(int balance);
  void setFilter(int filterType);
  void setDecodeMode(int decodeMode);
  void enableAudioBluetooth(bool enable);
  void enableBluetoothAptx(bool enable);
  void enableRelay(bool enable);
  void setMultifunctionKey(int keyType);
  void setUsbMode(int usbMode);
  void setScreenBrightness(int brightnessType);
  void setLanguage(int language);
  void resetSettings();
  void restoreFactorySettings();
  void reset();
  void requestSettings();
  void setUsbType(int type);
  void enableUsbDsdPassthrough(bool enable);
  void setIisPhase(int phase);
  void setIisDsdChannel(int channel);
  void setUsbSelect(int type);
  void enableUsbDsd(bool enable);
  void setIisChannel(int channel);
}

================
File: lib/utils/log_util.dart
================
import 'package:logger/logger.dart';
class Log {
  static const int VERBOSE = 0;
  static const int DEBUG = 1;
  static const int INFO = 2;
  static const int WARNING = 3;
  static const int ERROR = 4;
  static const int WTF = 5;
  static final Logger _logger = Logger();
  static void v(String message, [dynamic error, StackTrace? stackTrace]) {
      _logger.v(message, error: error, stackTrace: stackTrace);
  }
  static void d(String message, [dynamic error, StackTrace? stackTrace]) {
      _logger.d(message, error: error, stackTrace: stackTrace);
  }
  static void i(String message, [dynamic error, StackTrace? stackTrace]) {
      _logger.i(message, error: error, stackTrace: stackTrace);
  }
  static void w(String message, [dynamic error, StackTrace? stackTrace]) {
      _logger.w(message, error: error, stackTrace: stackTrace);
  }
  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
      _logger.e(message, error: error, stackTrace: stackTrace);
  }
  static void wtf(String message, [dynamic error, StackTrace? stackTrace]) {
      _logger.wtf(message, error: error, stackTrace: stackTrace);
  }
}

================
File: pubspec.lock
================
# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  args:
    dependency: transitive
    description:
      name: args
      sha256: d0481093c50b1da8910eb0bb301626d4d8eb7284aa739614d2b394ee09e3ea04
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: d2872f9c19731c2e5f10444b14686eb7cc85c76274bd6c16e1816bff9a3bab63
      url: "https://pub.dev"
    source: hosted
    version: "2.12.0"
  bluez:
    dependency: transitive
    description:
      name: bluez
      sha256: "61a7204381925896a374301498f2f5399e59827c6498ae1e924aaa598751b545"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.3"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: ff6785f7e9e3c38ac98b2fb035701789de90154024a75b6cb926445e83197d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: "direct main"
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.11"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "6a95e56b2449df2273fd8c45a662d6947ce1ebb7aafe80e550a3f68297f3cacc"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  ffi:
    dependency: "direct main"
    description:
      name: ffi
      sha256: "289279317b4b16eb2bb7e271abccd4bf84ec9bdcbe999e278a94b804f5630418"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  ffigen:
    dependency: "direct dev"
    description:
      name: ffigen
      sha256: "10cb41647d73e0204f8d35138a3f20eb52418cce96599ad49167b1111e59a557"
      url: "https://pub.dev"
    source: hosted
    version: "13.0.0"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: "8986dec4581b4bcd4b6df5d75a2ea0bede3db802f500635d05fa8be298f9467f"
      url: "https://pub.dev"
    source: hosted
    version: "10.1.2"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_blue_plus:
    dependency: "direct main"
    description:
      name: flutter_blue_plus
      sha256: "49f474cf38d669de8ce1e9dd83fe597a7f022a406241efcde4989294a1e2c89d"
      url: "https://pub.dev"
    source: hosted
    version: "1.35.4"
  flutter_blue_plus_android:
    dependency: transitive
    description:
      name: flutter_blue_plus_android
      sha256: "31e61095adbbbf0a12189945c8d77118deaca1d39d3dc2eb22170d89fc63c0e2"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  flutter_blue_plus_darwin:
    dependency: transitive
    description:
      name: flutter_blue_plus_darwin
      sha256: f34123795352a9761e321589aa06356d3b53f007f13f7e23e3c940e733259b2d
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_blue_plus_linux:
    dependency: transitive
    description:
      name: flutter_blue_plus_linux
      sha256: "297b31fa7e3c7ce938ff01f2854dc2767abcb6c5d463aa4fb27af4900163c946"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_blue_plus_platform_interface:
    dependency: transitive
    description:
      name: flutter_blue_plus_platform_interface
      sha256: "09e94c8b75de055ca02016cfc113400060a8d04a398774f6622e6e5525281b74"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_blue_plus_web:
    dependency: transitive
    description:
      name: flutter_blue_plus_web
      sha256: "03023c259dbbba1bc5ce0fcd4e88b364f43eec01d45425f393023b9b2722cf4d"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "5398f14efa795ffb7a33e9b6a08798b26a180edac4ad7db3f231e40f82ce11e1"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: f948e346c12f8d5480d2825e03de228d0eb8c3a737e4cdaa122267b89c022b5e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.28"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: c79eeb4339f1f3deffd9ec912f8a923834bec55f7b49c9e882b8fef2c139d425
      url: "https://pub.dev"
    source: hosted
    version: "4.7.2"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: c3f1ee72c96f8f78935e18aa8cecced9ab132419e8625dc187e1c2408efc20de
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: c35baad643ba394b40aac41080300150a4f08fd0fd6a10378f8f7c6bc161acec
      url: "https://pub.dev"
    source: hosted
    version: "10.0.8"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: c35bb79562d980e9a453fc715854e1ed39e24e7d0297a880ef54e17f9874a9d7
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  logger:
    dependency: "direct main"
    description:
      name: logger
      sha256: be4b23575aac7ebf01f225a241eb7f6b5641eeaf43c6a8613510fc2f8cf187d1
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: f096c55ebb7deb7e384101542bfba8c52696c1b56fca2eb62827989ef2353bbc
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: d0d310befe2c8ab9e7f393288ccbb11b60c019c6b5afc21973eeee4dda2b35e9
      url: "https://pub.dev"
    source: hosted
    version: "2.2.17"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "59adad729136f01ea9e35a48f5d1395e25cba6cea552249ddbe9cf950f5d7849"
      url: "https://pub.dev"
    source: hosted
    version: "11.4.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: d3971dcdd76182a0c198c096b5db2f0884b0d4196723d21a866fc4cdea057ebc
      url: "https://pub.dev"
    source: hosted
    version: "12.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: f000131e755c54cf4d84a5d8bd6e4149e262cc31c5a8b1d698de1ac85fa41023
      url: "https://pub.dev"
    source: hosted
    version: "9.4.7"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: "38f000e83355abb3392140f6bc3030660cfaef189e1f87824facb76300b4ff24"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+5"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: eb99b295153abce5d683cac8c02e22faab63e50679b937fa1bf67d58bb282878
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "07c8f0b1913bcde1ff0d26e57ace2f3012ccbf2b204e070290dad3bb22797646"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: "direct main"
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "5c3004a4a8dbb94bd4bf5412a4def4acdaa12e12f269737a5751369e12d1a962"
      url: "https://pub.dev"
    source: hosted
    version: "0.28.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "0968250880a6c5fe7edc067ed0a13d4bae1577fe2771dcf3010d52c4a9d3ca14"
      url: "https://pub.dev"
    source: hosted
    version: "14.3.1"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "868d88a33d8a87b18ffc05f9f030ba328ffefba92d6c127917a2ba740f9cfe4a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: dc6ecaa00a7c708e5b4d10ee7bec8c270e9276dfcab1783f57e9962d7884305f
      url: "https://pub.dev"
    source: hosted
    version: "5.12.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  yaml_edit:
    dependency: transitive
    description:
      name: yaml_edit
      sha256: fb38626579fb345ad00e674e2af3a5c9b0cc4b9bfb8fd7f7ff322c7c9e62aef5
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
sdks:
  dart: ">=3.7.0 <4.0.0"
  flutter: ">=3.27.0"

================
File: lib/bluetooth/ffi/ble_native_notifier.dart
================
import 'package:topping_ble_control/utils/log_util.dart';
import '../../model/bluetooth/ble_device.dart';
import '../../model/bluetooth/gatt_characteristic.dart';
import '../../model/ffi/ffi_bluetooth_profile_state.dart';
import '../ble_bindings.dart';
import 'ble_ffi_registry.dart';
class BleNativeNotifier {
  static final BleNativeNotifier _instance = BleNativeNotifier._internal();
  factory BleNativeNotifier() => _instance;
  BleNativeNotifier._internal();
  void reportScanResults(List<BleDevice> devices) {
    Log.i("准备报告扫描结果给原生层，设备数量: ${devices.length}");
    if (devices.isEmpty) {
      Log.i("设备列表为空，不报告扫描结果");
      return;
    }
    final scannerMap = BleFfiRegistry().nativeToFlutterScannerMap;
    Log.i("当前扫描器映射状态: $scannerMap");
    if (scannerMap.isEmpty) {
      Log.e("错误: 扫描器映射为空，无法报告扫描结果给原生层");
      Log.i("当扫描器映射为空时，跳过报告扫描结果给原生层的步骤");
      return;
    }
    Log.i("报告扫描结果给原生层: ${devices.length} 个设备，注册的原生对象数量: ${scannerMap.keys.length}");
    for (var nativeObject in scannerMap.keys) {
      try {
        Log.i("向原生对象 $nativeObject 报告扫描结果");
        BleBindings.instance.reportScanResults(nativeObject, devices);
      } catch (e) {
        Log.e("向原生对象 $nativeObject 报告扫描结果时出错: $e");
      }
    }
  }
  void reportScanFailed(int errorCode) {
    for (var nativeObject in BleFfiRegistry().nativeToFlutterScannerMap.keys) {
      BleBindings.instance.reportScanFailed(nativeObject, errorCode);
    }
  }
  void notifyConnectionStateChange(FfiBluetoothProfileState state) {
    Log.i("通知连接状态变化: $state -> $state BleFfiRegistry().nativeToFlutterGattMap.keys: ${BleFfiRegistry().nativeToFlutterGattMap.keys}");
    for (var nativeObject in BleFfiRegistry().nativeToFlutterGattMap.keys) {
      BleBindings.instance.notifyConnectionStateChange(
        nativeObject,
        state.value,
        state.value,
      );
    }
  }
  void notifyServicesDiscovered() {
    Log.i("通知服务发现完成 BleFfiRegistry().nativeToFlutterGattMap.keys: ${BleFfiRegistry().nativeToFlutterGattMap.keys}");
    for (var nativeObject in BleFfiRegistry().nativeToFlutterGattMap.keys) {
      BleBindings.instance.notifyServicesDiscovered(nativeObject);
    }
  }
  void notifyCharacteristicChanged(GattCharacteristic characteristic) {
    final nativeObjectKeys = BleFfiRegistry().nativeToFlutterGattMap.keys;
    Log.i(
      "准备通知特征值变化到C++层, uuid: ${characteristic.uuid}, "
          "值长度: ${characteristic.value.length}, "
          "注册的nativeObject数量: ${nativeObjectKeys.length}, "
          "nativeObjects: $nativeObjectKeys",
    );
    if (nativeObjectKeys.isEmpty) {
      Log.e("错误: 没有注册的nativeObject，特征值变化通知无法发送到C++层！");
      return;
    }
    for (var nativeObject in nativeObjectKeys) {
      BleBindings.instance.notifyCharacteristicChanged(
        nativeObject,
        characteristic,
      );
    }
  }
}

================
File: lib/interfaces/bluetooth_operations.dart
================
import '../config/ble_config.dart';
import '../model/enums/device_mode_type.dart';
abstract class BluetoothOperations {
  Future<void> startScan({List<DeviceModeType> deviceTypes});
  void stopScan();
  void connect({required int deviceHandle});
  void disconnect();
  Future<void> writeCharacteristic(String uuid, List<int> value);
  Future<void> setCharacteristicNotification(String uuid, bool enable);
}

================
File: pubspec.yaml
================
name: topping_ble_control
description: "A new Flutter project."
version: 0.0.1
homepage:
environment:
  sdk: ^3.7.0
  flutter: '>=3.3.0'
dependencies:
  ffi: ^2.1.3
  flutter:
    sdk: flutter
  plugin_platform_interface: ^2.0.2
  flutter_blue_plus: ^1.35.4
  permission_handler: ^11.4.0
  logger: ^2.5.0
  file_picker: ^10.1.2
  path_provider: ^2.1.1
  crypto: ^3.0.2
  get: ^4.7.2
dev_dependencies:
  ffigen: ^13.0.0
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
flutter:
  plugin:
    platforms:
      android:
        ffiPlugin: true
        package: com.topping.control
        pluginClass: ToppingBleControlPlugin
      ios:
        ffiPlugin: true
        pluginClass: ToppingBleControlPlugin

================
File: lib/model/bluetooth/ble_device.dart
================
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../../config/ble_config.dart';
import '../../utils/log_util.dart';
import '../enums/device_mode_type.dart';
class BleDevice {
  final int id;
  final String name;
  int rssi;
  final int manufacturerId;
  final List<int> manufacturerData;
  final List<int>? completeManufacturerData;
  BluetoothDevice? flutterDevice;
  final int nativeHandle;
  bool isPersistent = false;
  DeviceModeType deviceType;
  BleDevice({
    required this.id,
    required this.name,
    required this.rssi,
    this.manufacturerId = 0,
    this.manufacturerData = const [],
    this.completeManufacturerData,
    this.flutterDevice,
    this.nativeHandle = 0,
    this.deviceType = DeviceModeType.unknown,
  });
  factory BleDevice.fromScanResult(ScanResult result) {
    final device = result.device;
    final rssi = result.rssi;
    final name =
        device.platformName.isNotEmpty ? device.platformName : 'Unknown Device';
    int manufacturerId = 0;
    List<int> manufacturerData = [];
    List<int> completeManufacturerData = [];
    DeviceModeType deviceType = DeviceModeType.unknown;
    if (result.advertisementData.manufacturerData.isNotEmpty) {
      final entry = result.advertisementData.manufacturerData.entries.first;
      manufacturerId = entry.key;
      manufacturerData = entry.value;
      completeManufacturerData = [
        manufacturerId & 0xFF,
        (manufacturerId >> 8) & 0xFF,
        ...manufacturerData,
      ];
      if (manufacturerData.length >= 2) {
        int productId = (manufacturerData[0] << 8) | manufacturerData[1];
        deviceType = BleConfig().identifyDeviceType(manufacturerId, productId);
        Log.i("识别到设备类型: $deviceType");
      }
    }
    return BleDevice(
      id: device.remoteId.str.hashCode,
      name: name,
      rssi: rssi,
      manufacturerId: manufacturerId,
      manufacturerData: manufacturerData,
      completeManufacturerData: completeManufacturerData,
      flutterDevice: device,
      nativeHandle: device.hashCode,
      deviceType: deviceType,
    );
  }
  @override
  String toString() {
    final highByte = (manufacturerId >> 8) & 0xFF;
    final lowByte = manufacturerId & 0xFF;
    final bigEndianId = (lowByte << 8) | highByte;
    final bigEndianIdHex =
        bigEndianId.toRadixString(16).padLeft(4, '0').toUpperCase();
    final dataHex = manufacturerData
        .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
        .join('');
    final completeHex =
        completeManufacturerData
            ?.map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
            .join('') ??
        '';
    return 'BleDevice{id: $id, name: $name, rssi: $rssi, '
        'deviceType: ${deviceType.toString().split('.').last}, '
        'manufacturerId: 0x$bigEndianIdHex, '
        'manufacturerData: 0x$dataHex, '
        'completeData: 0x$completeHex, '
        'nativeHandle: $nativeHandle'
        '${flutterDevice != null ? ', flutterDevice: $flutterDevice' : ''}'
        '}';
  }
}

================
File: lib/bluetooth/gatt/characteristic_handler.dart
================
import 'dart:async';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../../model/bluetooth/gatt_characteristic.dart';
import '../../registry/service_registry.dart';
import '../../utils/ble_uuid_util.dart';
import '../../utils/log_util.dart';
import '../connection/ble_connector.dart';
import '../ffi/ble_native_notifier.dart';
class BleCharacteristicHandler {
  final StreamController<bool> _writeResultController =
  StreamController.broadcast();
  Stream<bool> get writeResult => _writeResultController.stream;
  final Map<String, StreamSubscription> _activeSubscriptions = {};
  final Map<String, List<Function(List<int>)>> _valueListeners = {};
  final BleConnector _connector;
  BleCharacteristicHandler(this._connector);
  Future<bool> writeCharacteristic(String uuid, List<int> value) async {
    Log.i("写入特征值: $uuid, 数据: $value");
    var join = value.map((e) => value).join('');
    Log.i("原值：$join");
    // value转为16进制字符串
    var str = value.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ');
    Log.i(
      "写入特征值: $uuid, 数据: $str,\n"
          "字符串：${String.fromCharCodes(value)}, \n"
          "时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    if (_connector.connectedDevice == null) {
      _writeResultController.add(false);
      return false;
    }
    try {
      final device = _connector.connectedDevice!;
      final services = device.flutterDevice!.servicesList;
      if (services.isEmpty) {
        _writeResultController.add(false);
        return false;
      }
      Log.i("写入操作成功, 时间戳: ${DateTime.now().millisecondsSinceEpoch}");
      return await _findAndWriteToCharacteristic(uuid, value, services);
    } catch (e) {
      Log.e("写入特征值错误: $e");
      _writeResultController.add(false);
      return false;
    }
  }
  Future<bool> _findAndWriteToCharacteristic(
      String uuid,
      List<int> value,
      List<BluetoothService> services,
      ) async {
    String normalizedTargetUuid = BleUuidUtil.normalizeUuid(uuid);
    for (var service in services) {
      if (service.characteristics.isEmpty) continue;
      for (var char in service.characteristics) {
        String normalizedCharUuid = BleUuidUtil.normalizeUuid(
          char.uuid.toString(),
        );
        if (normalizedCharUuid == normalizedTargetUuid) {
          return await _writeToCharacteristic(char, value);
        }
      }
    }
    Log.e("未找到UUID为 $uuid 的特征值");
    _writeResultController.add(false);
    return false;
  }
  Future<bool> _writeToCharacteristic(
      BluetoothCharacteristic char,
      List<int> value,
      ) async {
    if (!char.properties.write && !char.properties.writeWithoutResponse) {
      Log.e("错误: 该特征值不支持写入");
      _writeResultController.add(false);
      return false;
    }
    bool withoutResponse = char.properties.writeWithoutResponse;
    final writeCompleter = Completer<bool>();
    char
        .write(value, withoutResponse: withoutResponse,allowLongWrite: true)
        .then((_) {
      if (!writeCompleter.isCompleted) {
        writeCompleter.complete(true);
      }
    })
        .catchError((error) {
      Log.e("写入失败: $error");
      if (error.toString().contains("device is disconnected")) {
        Log.i("检测到设备断开，主动断开连接");
        _connector.disconnectDevice();
      }
      if (withoutResponse && char.properties.write) {
        Log.i("尝试带响应写入...");
        return char
            .write(value, withoutResponse: false)
            .then((_) {
          if (!writeCompleter.isCompleted) {
            writeCompleter.complete(true);
          }
        })
            .catchError((error) {
          Log.e("带响应写入失败: $error");
          if (error.toString().contains("device is disconnected")) {
            _connector.disconnectDevice();
          }
          if (!writeCompleter.isCompleted) {
            writeCompleter.complete(false);
          }
        });
      }
      if (!writeCompleter.isCompleted) {
        writeCompleter.complete(false);
      }
    });
    try {
      bool result = await writeCompleter.future.timeout(
        Duration(seconds: 5),
        onTimeout: () {
          Log.e("写入操作超时");
          if (_connector.connectedDevice == null ||
              !(_connector.connectedDevice!.flutterDevice?.isConnected ??
                  false)) {
            _connector.disconnectDevice();
          }
          return false;
        },
      );
      Log.i(
        "写入操作${result ? '成功' : '失败'}, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
      );
      _writeResultController.add(result);
      return result;
    } on TimeoutException {
      Log.e("写入操作超时");
      if (_connector.connectedDevice == null ||
          !(_connector.connectedDevice!.flutterDevice?.isConnected ?? false)) {
        _connector.disconnectDevice();
      }
      _writeResultController.add(false);
      return false;
    }
  }
  Future<bool> setCharacteristicNotification(String uuid, bool enable) async {
    if (_connector.connectedDevice == null) return false;
    try {
      final device = _connector.connectedDevice!;
      Log.i(
        "设置特征值通知: $uuid, 启用: $enable, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
      );
      if (_activeSubscriptions.containsKey(uuid)) {
        await _activeSubscriptions[uuid]?.cancel();
        _activeSubscriptions.remove(uuid);
        Log.d("已取消之前的订阅: $uuid");
      }
      List<BluetoothService> services =
      await device.flutterDevice!.discoverServices();
      String normalizedTargetUuid = BleUuidUtil.normalizeUuid(uuid);
      for (var service in services) {
        for (var char in service.characteristics) {
          String normalizedCharUuid = BleUuidUtil.normalizeUuid(
            char.uuid.toString(),
          );
          if (normalizedCharUuid == normalizedTargetUuid) {
            if (enable) {
              final subscription = char.onValueReceived.listen(
                    (value) {
                      var str = value.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ');
                  Log.i(
                    "收到特征值通知: $uuid, 值:  $str,  length: ${value.length}, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
                  );
                  var valueStr = String.fromCharCodes(value);
                  Log.i("特征值通知字符串: $valueStr");
                  Log.d("通知详情 - 长度: ${value.length}, 原始数据: $value");
                  try {
                    BleNativeNotifier().notifyCharacteristicChanged(
                      GattCharacteristic(
                        uuid: uuid,
                        property: GattCharacteristic.PROPERTY_NOTIFY,
                        value: value,
                      ),
                    );
                    _notifyValueListeners(uuid, value);
                  } catch (e) {
                    Log.e("向C++通知特征值变化失败: $e");
                  }
                },
                onError: (error) {
                  Log.e("特征值通知错误: $error");
                },
              );
              _activeSubscriptions[uuid] = subscription;
              device.flutterDevice?.cancelWhenDisconnected(subscription);
            }
            await char.setNotifyValue(enable);
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      Log.e("设置特征值通知错误: $e");
      return false;
    }
  }
  void addCharacteristicValueListener(
      String uuid,
      Function(List<int>) listener,
      ) {
    if (!_valueListeners.containsKey(uuid)) {
      _valueListeners[uuid] = [];
    }
    _valueListeners[uuid]!.add(listener);
  }
  void removeCharacteristicValueListener(
      String uuid, [
        Function(List<int>)? listener,
      ]) {
    if (!_valueListeners.containsKey(uuid)) return;
    if (listener == null) {
      _valueListeners.remove(uuid);
    } else {
      _valueListeners[uuid]!.remove(listener);
      if (_valueListeners[uuid]!.isEmpty) {
        _valueListeners.remove(uuid);
      }
    }
  }
  void _notifyValueListeners(String uuid, List<int> value) {
    if (!_valueListeners.containsKey(uuid)) return;
    for (var listener in _valueListeners[uuid]!) {
      try {
        listener(value);
      } catch (e) {
        Log.e("通知特征值监听器错误: $e");
      }
    }
  }
  int getService(String uuid) {
    if (_connector.connectedDevice == null) return 0;
    String normalizedUuid = BleUuidUtil.normalizeUuid(uuid);
    for (var service
    in _connector.connectedDevice!.flutterDevice!.servicesList) {
      if (BleUuidUtil.normalizeUuid(service.uuid.toString()) ==
          normalizedUuid) {
        final serviceHandle = service.hashCode;
        ServiceRegistry().registerService(serviceHandle, service);
        return serviceHandle;
      }
    }
    return 0;
  }
  void dispose() {
    for (var subscription in _activeSubscriptions.values) {
      subscription.cancel();
    }
    _activeSubscriptions.clear();
    _writeResultController.close();
  }
}

================
File: lib/bluetooth/ble_notification_helper.dart
================
import 'package:topping_ble_control/bluetooth/ble_operation_manager.dart';
import 'package:topping_ble_control/config/ble_config.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import '../model/enums/device_mode_type.dart';
class BleNotificationHelper {
  static Future<void> enableNotifications({DeviceModeType deviceType = DeviceModeType.dx5}) async {
    try {
      final manager = BleOperationManager();
      final config = BleConfig().getConfig(deviceType);
      await manager.setCharacteristicNotification(
        config.notifyCharacteristicUuid,
        true,
      );
      Log.i('已启用 ${config.deviceName} 的通知特征');
    } catch (e) {
      Log.e('启用通知特征失败: $e');
      rethrow;
    }
  }
  static Future<void> disableNotifications({DeviceModeType deviceType = DeviceModeType.dx5}) async {
    try {
      final manager = BleOperationManager();
      final config = BleConfig().getConfig(deviceType);
      await manager.setCharacteristicNotification(
        config.notifyCharacteristicUuid,
        false,
      );
      Log.i('已禁用 ${config.deviceName} 的通知特征');
    } catch (e) {
      Log.e('禁用通知特征失败: $e');
      rethrow;
    }
  }
}

================
File: lib/bluetooth/connection/ble_connector.dart
================
import 'dart:async';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../../model/bluetooth/ble_device.dart';
import '../../model/ffi/ffi_bluetooth_profile_state.dart';
import '../../registry/device_data_manager.dart';
import '../../registry/service_registry.dart';
import '../../utils/errors.dart';
import '../../utils/log_util.dart';
import '../ble_notification_helper.dart';
import '../ffi/ble_native_notifier.dart';
class BleConnector {
  BleDevice? _connectedDevice;
  BleDevice? get connectedDevice => _connectedDevice;
  Timer? _connectionMonitorTimer;
  StreamSubscription<BluetoothConnectionState>? _connectionStateSubscription;
  void connectToDevice(int deviceHandle) async {
    Log.i("尝试连接设备，句柄: $deviceHandle");
    if (!_prepareDeviceForConnection(deviceHandle)) {
      Log.e("设备准备失败，无法连接");
      _notifyDisconnected();
      return;
    }
    _connectionStateSubscription?.cancel();
    _setupConnectionStateListener();
    _connectWithTimeout();
  }
  void _setupConnectionStateListener() {
    if (_connectedDevice?.flutterDevice == null) return;
    _connectionStateSubscription = _connectedDevice!
        .flutterDevice!
        .connectionState
        .skipWhile((state) => state == BluetoothConnectionState.disconnected)
        .listen((state) async {
      Log.i(
        "监听到连接状态变化: $state, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
      );
      switch (state) {
        case BluetoothConnectionState.connected:
          if (_connectedDevice != null) {
            DeviceDataManager().clearScanResultsExceptConnected(_connectedDevice!);
          } else {
            Log.e("连接设备为空，无法清理扫描结果");
          }
          final services = await _discoverServices();
          if (services.isEmpty) {
            throw ConnectionError("未能发现任何服务");
          }
          _registerServices(services);
          break;
        case BluetoothConnectionState.disconnected:
          if (_connectedDevice != null) {
            _notifyDisconnected();
          }
          break;
        default:
          break;
      }
    });
    final subscription = _connectedDevice!.flutterDevice!.mtu.listen((int mtu) {
      Log.i("mtu $mtu");
    });
    _connectedDevice!.flutterDevice?.cancelWhenDisconnected(subscription);
  }
  bool _prepareDeviceForConnection(int deviceHandle) {
    Log.i("准备设备连接，句柄: $deviceHandle");
    _connectedDevice = DeviceDataManager().getDeviceByHandle(deviceHandle);
    if (_connectedDevice == null) {
      Log.e("找不到句柄为 $deviceHandle 的设备");
      return false;
    }
    if (_connectedDevice!.flutterDevice == null) {
      Log.w("设备 ${_connectedDevice!.name} 没有 Flutter 设备对象，尝试查找匹配项");
      var matchingDevice = DeviceDataManager().findDevice(
        name: _connectedDevice!.name,
        id: _connectedDevice!.id,
      );
      if (matchingDevice != null && matchingDevice.flutterDevice != null) {
        Log.i("找到匹配设备，使用其 Flutter 设备对象");
        _connectedDevice!.flutterDevice = matchingDevice.flutterDevice;
        DeviceDataManager().registerDevice(_connectedDevice!);
        return true;
      }
      Log.i("尝试从最近的扫描结果中查找匹配设备");
      var devices = DeviceDataManager().getAllDevices();
      for (var device in devices) {
        if (device.flutterDevice != null &&
            (device.name == _connectedDevice!.name ||
             device.id == _connectedDevice!.id)) {
          Log.i("在扫描结果中找到匹配设备: ${device.name}");
          _connectedDevice!.flutterDevice = device.flutterDevice;
          DeviceDataManager().registerDevice(_connectedDevice!);
          return true;
        }
      }
      Log.e("无法找到匹配的 Flutter 设备对象");
      return false;
    }
    Log.i("设备准备就绪");
    return true;
  }
  void _connectWithTimeout() {
    if (_connectedDevice == null) {
      Log.e("连接失败: 设备对象为空");
      _notifyDisconnected();
      return;
    }
    if (_connectedDevice!.flutterDevice == null) {
      Log.e("连接失败: Flutter设备对象为空，设备名称: ${_connectedDevice!.name}");
      _notifyDisconnected();
      return;
    }
    Log.i(
      "尝试连接设备: ${_connectedDevice!.name}, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    const timeout = Duration(seconds: 15);
    try {
      _connectedDevice!.flutterDevice!.connect(
        timeout: timeout,
        autoConnect: false,
      ).catchError((error) {
        Log.e("连接设备时发生错误: $error");
        _notifyDisconnected();
        return Future.error(error);
      });
    } catch (e) {
      Log.e("连接设备时发生同步错误: $e");
      _notifyDisconnected();
    }
  }
  Future<List<BluetoothService>> _discoverServices() async {
    try {
      final services =
      await _connectedDevice!.flutterDevice!.discoverServices();
      Log.i(
        "服务发现完成，发现 ${services.length} 个服务, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
      );
      return services;
    } catch (e) {
      _notifyDisconnected();
      return [];
    }
  }
  void _registerServices(List<BluetoothService> services) {
    Log.i("注册 ${services.length} 个服务");
    ServiceRegistry().clear();
    for (var service in services) {
      final serviceHandle = service.hashCode;
      ServiceRegistry().registerService(serviceHandle, service);
    }
    final _ = ServiceRegistry().getServices();
    BleNativeNotifier().notifyConnectionStateChange(
      FfiBluetoothProfileState.stateConnected,
    );
    BleNativeNotifier().notifyServicesDiscovered();
    _enableNotifications();
  }
  void _enableNotifications() {
    Future(() async {
      try {
        await BleNotificationHelper.enableNotifications();
        Log.i("设备通知启用成功");
      } catch (e) {
      }
    });
  }
  void _notifyDisconnected() {
    Log.i("通知断开连接状态");
    BleNativeNotifier().notifyConnectionStateChange(
      FfiBluetoothProfileState.stateDisconnected,
    );
    _connectionMonitorTimer?.cancel();
    _connectedDevice = null;
  }
  void disconnectDevice() {
    Log.i("断开设备连接");
    if (_connectedDevice == null) {
      return;
    }
  }
  void dispose() {
    Log.i("释放 BleConnector 资源");
    disconnectDevice();
    _connectionMonitorTimer?.cancel();
    _connectionStateSubscription?.cancel();
  }
}

================
File: lib/bluetooth/ble_bindings.dart
================
import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';
import '../mappers/ffi_mapper.dart';
import '../model/bluetooth/ble_device.dart';
import '../model/bluetooth/gatt_characteristic.dart';
import '../model/ffi/ffi_ble_scanner_functions.dart';
import '../model/ffi/ffi_gatt_characteristic.dart';
import '../model/ffi/ffi_gatt_functions.dart';
import '../model/ffi/ffi_scan_result.dart';
import '../utils/log_util.dart';
import 'ffi/ble_ffi_registry.dart';
class BleBindings {
  static const String TAG = "BleBindings";
  static BleBindings? _instance;
  static BleBindings get instance {
    _instance ??= BleBindings._();
    return _instance!;
  }
  late final DynamicLibrary _dylib;
  late final void Function(Pointer<FFIBleScannerFunctions>)
  _flutterBleScannerRegisterFunctions;
  late final void Function(int, Pointer<FFIScanResult>, int)
  _flutterBleScannerScanResults;
  late final void Function(int, int) _flutterBleScannerScanFailed;
  late final void Function() _flutterAdapterRegisterDefault;
  late final void Function(Pointer<FFIGattFunctions>)
  _flutterGattRegisterFunctions;
  late final void Function(int, int, int) _flutterGattOnConnectionStateChange;
  late final void Function(int) _flutterGattOnServicesDiscovered;
  late final void Function(int, Pointer<FFIGattCharacteristic>)
  _flutterGattOnCharacteristicChanged;
  static int Function(int)? _bleScannerInitCallback;
  static void Function(int)? _bleScannerUninitCallback;
  static void Function(int)? _bleScannerStartScanCallback;
  static void Function(int)? _bleScannerStopScanCallback;
  static int Function(int)? _gattInitCallback;
  static void Function(int)? _gattUninitCallback;
  static void Function(int)? _gattCloseCallback;
  static void Function(int)? _gattConnectCallback;
  static void Function(int)? _gattDisconnectCallback;
  static int Function(int, int)? _gattRequestMtuCallback;
  static int Function(int, Pointer<FFIGattCharacteristic>)?
  _gattWriteCharacteristicCallback;
  static int Function(int, Pointer<FFIGattCharacteristic>, int)?
  _gattSetCharacteristicNotificationCallback;
  static int Function(int, Pointer<Char>)? _gattGetServiceCallback;
  static int _bleScannerInit(int nativeObject) {
    return _bleScannerInitCallback?.call(nativeObject) ?? 0;
  }
  static void _bleScannerUninit(int flutterObject) {
    _bleScannerUninitCallback?.call(flutterObject);
  }
  static void _bleScannerStartScan(int flutterObject) {
    Log.i("$TAG: 开始扫描");
    _bleScannerStartScanCallback?.call(flutterObject);
  }
  static void _bleScannerStopScan(int flutterObject) {
    _bleScannerStopScanCallback?.call(flutterObject);
  }
  static int _gattInit(int nativeObject) {
    return _gattInitCallback?.call(nativeObject) ?? 0;
  }
  static void _gattUninit(int flutterObject) {
    _gattUninitCallback?.call(flutterObject);
  }
  static void _gattClose(int flutterObject) {
    _gattCloseCallback?.call(flutterObject);
  }
  static void _gattConnect(int flutterObject) {
    Log.i("Connecting to device");
    _gattConnectCallback?.call(flutterObject);
  }
  static void _gattDisconnect(int flutterObject) {
    _gattDisconnectCallback?.call(flutterObject);
  }
  static int _gattRequestMtu(int flutterObject, int mtu) {
    return _gattRequestMtuCallback?.call(flutterObject, mtu) ?? 0;
  }
  static int _gattWriteCharacteristic(
    int flutterObject,
    Pointer<FFIGattCharacteristic> characteristic,
  ) {
    return _gattWriteCharacteristicCallback?.call(
          flutterObject,
          characteristic,
        ) ??
        0;
  }
  static int _gattSetCharacteristicNotification(
    int flutterObject,
    Pointer<FFIGattCharacteristic> characteristic,
    int enable,
  ) {
    return _gattSetCharacteristicNotificationCallback?.call(
          flutterObject,
          characteristic,
          enable,
        ) ??
        0;
  }
  static int _gattGetService(int flutterObject, Pointer<Char> uuid) {
    return _gattGetServiceCallback?.call(flutterObject, uuid) ?? 0;
  }
  BleBindings._() {
    _dylib = _nativeLib;
    _loadFunctions();
  }
  final DynamicLibrary _nativeLib =
      Platform.isAndroid
          ? DynamicLibrary.open("libtopping_controller.so")
          : DynamicLibrary.process();
  void _loadFunctions() {
    _flutterBleScannerRegisterFunctions =
        _dylib
            .lookup<
              NativeFunction<Void Function(Pointer<FFIBleScannerFunctions>)>
            >('flutter_ble_scanner_register_functions')
            .asFunction();
    _flutterBleScannerScanResults =
        _dylib
            .lookup<
              NativeFunction<Void Function(Int64, Pointer<FFIScanResult>, Size)>
            >('flutter_ble_scanner_scan_results')
            .asFunction();
    _flutterBleScannerScanFailed =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32)>>(
              'flutter_ble_scanner_scan_failed',
            )
            .asFunction();
    _flutterAdapterRegisterDefault =
        _dylib
            .lookup<NativeFunction<Void Function()>>(
              'flutter_adapter_register_default',
            )
            .asFunction();
    _flutterGattRegisterFunctions =
        _dylib
            .lookup<NativeFunction<Void Function(Pointer<FFIGattFunctions>)>>(
              'flutter_gatt_register_functions',
            )
            .asFunction();
    _flutterGattOnConnectionStateChange =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32, Int32)>>(
              'flutter_gatt_on_connection_state_change',
            )
            .asFunction();
    _flutterGattOnServicesDiscovered =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'flutter_gatt_on_services_discovered',
            )
            .asFunction();
    _flutterGattOnCharacteristicChanged =
        _dylib
            .lookup<
              NativeFunction<
                Void Function(Int64, Pointer<FFIGattCharacteristic>)
              >
            >('flutter_gatt_on_characteristic_changed')
            .asFunction();
  }
  void registerBleScannerFunctions({
    required int Function(int nativeObject) init,
    required void Function(int flutterObject) uninit,
    required void Function(int flutterObject) startScan,
    required void Function(int flutterObject) stopScan,
  }) {
    _bleScannerInitCallback = init;
    _bleScannerUninitCallback = uninit;
    _bleScannerStartScanCallback = startScan;
    _bleScannerStopScanCallback = stopScan;
    final nativeFunctions = calloc<FFIBleScannerFunctions>();
    nativeFunctions.ref.init = Pointer.fromFunction<Int64 Function(Int64)>(
      _bleScannerInit,
      0,
    );
    nativeFunctions.ref.uninit = Pointer.fromFunction<Void Function(Int64)>(
      _bleScannerUninit,
    );
    nativeFunctions.ref.start_scan = Pointer.fromFunction<Void Function(Int64)>(
      _bleScannerStartScan,
    );
    nativeFunctions.ref.stop_scan = Pointer.fromFunction<Void Function(Int64)>(
      _bleScannerStopScan,
    );
    _flutterBleScannerRegisterFunctions(nativeFunctions);
    calloc.free(nativeFunctions);
  }
  void reportScanResults(int nativeObject, List<BleDevice> devices) {
    Log.i("开始向原生层报告扫描结果，nativeObject: $nativeObject, 设备数量: ${devices.length}");
    if (devices.isEmpty) {
      Log.i("设备列表为空，不报告扫描结果");
      return;
    }
    if (nativeObject <= 0) {
      Log.e("错误: nativeObject无效 ($nativeObject)，不能报告扫描结果");
      return;
    }
    if (!BleFfiRegistry().nativeToFlutterScannerMap.containsKey(nativeObject)) {
      Log.e("错误: nativeObject ($nativeObject) 不存在于扫描器映射中，不能报告扫描结果");
      return;
    }
    Pointer<FFIScanResult>? nativeResults;
    try {
      nativeResults = calloc<FFIScanResult>(devices.length);
      Log.i("已分配内存，设备数量: ${devices.length}");
      for (var i = 0; i < devices.length; i++) {
        nativeResults[i].name = nullptr;
        nativeResults[i].manufacturer_data = nullptr;
        nativeResults[i].manufacturer_data_len = 0;
      }
      for (var i = 0; i < devices.length; i++) {
        final device = devices[i];
        if (device.name.isNotEmpty) {
          try {
            final namePtr = device.name.toNativeUtf8();
            nativeResults[i].name = namePtr.cast();
          } catch (e) {
            Log.e("转换设备名称时出错: $e");
            nativeResults[i].name = nullptr;
          }
        } else {
          nativeResults[i].name = "".toNativeUtf8().cast();
        }
        // 设置设备ID和RSSI
        nativeResults[i].device = device.id;
        nativeResults[i].rssi = device.rssi;
        // 使用completeManufacturerData来传递完整的制造商数据
        if (device.completeManufacturerData != null &&
            device.completeManufacturerData!.isNotEmpty) {
          try {
            final dataLength = device.completeManufacturerData!.length;
            // 分配制造商数据内存并进行错误检查
            final dataPtr = calloc<Uint8>(dataLength);
            // 逐字节复制制造商数据
            for (var j = 0; j < dataLength; j++) {
              dataPtr[j] = device.completeManufacturerData![j];
            }
            nativeResults[i].manufacturer_data = dataPtr;
            nativeResults[i].manufacturer_data_len = dataLength;
            Log.i("已复制设备 $i 的制造商数据，长度: $dataLength");
          } catch (e) {
            Log.e("复制制造商数据时出错: $e");
            nativeResults[i].manufacturer_data = nullptr;
            nativeResults[i].manufacturer_data_len = 0;
          }
        } else {
          nativeResults[i].manufacturer_data = nullptr;
          nativeResults[i].manufacturer_data_len = 0;
        }
      }
      if (BleFfiRegistry().nativeToFlutterScannerMap.containsKey(
        nativeObject,
      )) {
        try {
          Log.i(
            "调用原生函数 _flutterBleScannerScanResults，nativeObject: $nativeObject, 设备数量: ${devices.length}",
          );
          _flutterBleScannerScanResults(
            nativeObject,
            nativeResults,
            devices.length,
          );
          Log.i("成功向原生层报告扫描结果");
        } catch (e) {
          Log.e("调用_flutterBleScannerScanResults时崩溃: $e");
        }
      } else {
        Log.e("调用前nativeObject已变为无效");
      }
    } catch (e) {
      Log.e("处理扫描结果时出错: $e");
    } finally {
      if (nativeResults != null) {
        try {
          for (var i = 0; i < devices.length; i++) {
            try {
              if (nativeResults[i].name != nullptr) {
                calloc.free(nativeResults[i].name.cast<NativeType>());
              }
            } catch (e) {
              Log.e("释放设备 $i 名称内存时出错: $e");
            }
            try {
              if (nativeResults[i].manufacturer_data != nullptr) {
                calloc.free(nativeResults[i].manufacturer_data);
              }
            } catch (e) {
              Log.e("释放设备 $i 制造商数据内存时出错: $e");
            }
          }
          calloc.free(nativeResults);
          Log.i("已释放所有扫描结果内存");
        } catch (e) {
          Log.e("释放内存时发生未知错误: $e");
        }
      }
    }
  }
  void reportScanFailed(int nativeObject, int errorCode) {
    _flutterBleScannerScanFailed(nativeObject, errorCode);
  }
  void registerDefaultAdapter() {
    _flutterAdapterRegisterDefault();
  }
  void registerGattFunctions({
    required int Function(int nativeObject) init,
    required void Function(int flutterObject) uninit,
    required void Function(int flutterObject) close,
    required void Function(int flutterObject) connect,
    required void Function(int flutterObject) disconnect,
    required int Function(int flutterObject, int mtu) requestMtu,
    required int Function(
      int flutterObject,
      Pointer<FFIGattCharacteristic> characteristic,
    )
    writeCharacteristic,
    required int Function(
      int flutterObject,
      Pointer<FFIGattCharacteristic> characteristic,
      int enable,
    )
    setCharacteristicNotification,
    required int Function(int flutterObject, Pointer<Char> uuid) getService,
  }) {
    _gattInitCallback = init;
    _gattUninitCallback = uninit;
    _gattCloseCallback = close;
    _gattConnectCallback = connect;
    _gattDisconnectCallback = disconnect;
    _gattRequestMtuCallback = requestMtu;
    _gattWriteCharacteristicCallback = writeCharacteristic;
    _gattSetCharacteristicNotificationCallback = setCharacteristicNotification;
    _gattGetServiceCallback = getService;
    final nativeFunctions = calloc<FFIGattFunctions>();
    nativeFunctions.ref.init = Pointer.fromFunction<Int64 Function(Int64)>(
      _gattInit,
      0,
    );
    nativeFunctions.ref.uninit = Pointer.fromFunction<Void Function(Int64)>(
      _gattUninit,
    );
    nativeFunctions.ref.close = Pointer.fromFunction<Void Function(Int64)>(
      _gattClose,
    );
    nativeFunctions.ref.connect = Pointer.fromFunction<Void Function(Int64)>(
      _gattConnect,
    );
    nativeFunctions.ref.disconnect = Pointer.fromFunction<Void Function(Int64)>(
      _gattDisconnect,
    );
    nativeFunctions.ref.request_mtu =
        Pointer.fromFunction<Int32 Function(Int64, Int32)>(_gattRequestMtu, 0);
    nativeFunctions.ref.write_characteristic = Pointer.fromFunction<
      Int32 Function(Int64, Pointer<FFIGattCharacteristic>)
    >(_gattWriteCharacteristic, 0);
    nativeFunctions.ref.set_characteristic_notification = Pointer.fromFunction<
      Int32 Function(Int64, Pointer<FFIGattCharacteristic>, Int32)
    >(_gattSetCharacteristicNotification, 0);
    nativeFunctions.ref.get_service =
        Pointer.fromFunction<Int64 Function(Int64, Pointer<Char>)>(
          _gattGetService,
          0,
        );
    _flutterGattRegisterFunctions(nativeFunctions);
    calloc.free(nativeFunctions);
  }
  void notifyConnectionStateChange(int nativeObject, int state, int newState) {
    _flutterGattOnConnectionStateChange(nativeObject, state, newState);
  }
  void notifyServicesDiscovered(int nativeObject) {
    _flutterGattOnServicesDiscovered(nativeObject);
  }
  void notifyCharacteristicChanged(
    int nativeObject,
    GattCharacteristic characteristic,
  ) {
    final nativeChar = calloc<FFIGattCharacteristic>();
    try {
      final nativeChar = FFIMapper.toFFIGattCharacteristic(
        characteristic,
        calloc,
      );
      _flutterGattOnCharacteristicChanged(nativeObject, nativeChar);
    } catch (e) {
      Log.e("BleBindings.notifyCharacteristicChanged - 调用失败: $e");
    } finally {
      if (nativeChar.ref.uuid != nullptr) {
        calloc.free(nativeChar.ref.uuid.cast<NativeType>());
      }
      if (nativeChar.ref.value != nullptr) {
        calloc.free(nativeChar.ref.value);
      }
      calloc.free(nativeChar);
    }
  }
}

================
File: lib/bluetooth/ffi/ble_ffi_initializer.dart
================
import 'dart:async';
import 'dart:ffi';
import 'package:ffi/ffi.dart';
import '../../config/ble_config.dart';
import '../../model/enums/device_mode_type.dart';
import '../../model/ffi/ffi_bluetooth_profile_state.dart';
import '../../model/ffi/ffi_gatt_characteristic.dart';
import '../../registry/current_connecting_device.dart';
import '../../utils/log_util.dart';
import '../ble_bindings.dart';
import '../ble_operation_manager.dart';
import 'ble_ffi_registry.dart';
import 'ble_native_notifier.dart';
class BleFfiInitializer {
  static final BleFfiInitializer _instance = BleFfiInitializer._internal();
  factory BleFfiInitializer() => _instance;
  BleFfiInitializer._internal();
  static bool _isInitialized = false;
  static bool get isInitialized => _isInitialized;
  static final Completer<void> _initCompleter = Completer<void>();
  static Future<void> get initializationComplete => _initCompleter.future;
  Future<void> initialize() async {
    if (_isInitialized) {
      Log.i("BLE FFI系统已初始化");
      if (!_initCompleter.isCompleted) {
        _initCompleter.complete();
      }
      return;
    }
    try {
      Log.i("开始初始化BLE FFI系统");
      _registerBleScannerFunctions();
      _registerGattFunctions();
      await Future.delayed(Duration(milliseconds: 100));
      Log.i("注册默认蓝牙适配器");
      BleBindings.instance.registerDefaultAdapter();
      await Future.delayed(Duration(milliseconds: 100));
      _isInitialized = true;
      if (!_initCompleter.isCompleted) {
        _initCompleter.complete();
      }
      Log.i("BLE FFI系统初始化完成");
    } catch (e) {
      Log.e("初始化BLE FFI系统失败: $e");
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(e);
      }
      rethrow;
    }
  }
  Future<bool> ensureInitialized({
    Duration timeout = const Duration(seconds: 5),
  }) async {
    if (_isInitialized) return true;
    Log.i("等待BLE FFI初始化...");
    try {
      if (!_initCompleter.isCompleted && !_isInitialized) {
        initialize().catchError((e) {
          if (!_initCompleter.isCompleted) {
            _initCompleter.completeError(e);
          }
          Log.e("ensureInitialized 中调用 initialize 失败: $e");
        });
      }
      await initializationComplete.timeout(timeout);
      Log.i("BLE FFI初始化已完成 (通过ensureInitialized检查)");
      return _isInitialized;
    } catch (e) {
      Log.e("等待BLE FFI初始化超时或失败: $e");
      return false;
    }
  }
  void _registerBleScannerFunctions() {
    BleBindings.instance.registerBleScannerFunctions(
      init: (nativeObject) {
        Log.i("C++调用init, nativeObject: $nativeObject");
        final flutterObjectId = DateTime.now().millisecondsSinceEpoch;
        Log.i("注册扫描器映射前状态: ${BleFfiRegistry().nativeToFlutterScannerMap}");
        BleFfiRegistry().registerScannerMapping(nativeObject, flutterObjectId);
        Log.i("注册扫描器映射后状态: ${BleFfiRegistry().nativeToFlutterScannerMap}");
        return flutterObjectId;
      },
      uninit: (flutterObject) {
        Log.i("C++调用uninit, flutterObject: $flutterObject");
        Log.i("移除扫描器映射前状态: ${BleFfiRegistry().nativeToFlutterScannerMap}");
        BleFfiRegistry().removeScannerMapping(flutterObject);
        Log.i("移除扫描器映射后状态: ${BleFfiRegistry().nativeToFlutterScannerMap}");
        if (BleFfiRegistry().nativeToFlutterScannerMap.isEmpty) {
          Log.i("扫描器映射现在为空，将在下一次初始化时重新创建");
        }
      },
      startScan: (flutterObject) {
        Log.i("C++调用startScan, flutterObject: $flutterObject");
        Future(() async {
          await ensureInitialized();
          BleOperationManager().startScan(
            deviceTypes: [DeviceModeType.dx5, DeviceModeType.dx9],
            useNativeScanner: true,
          );
        });
      },
      stopScan: (flutterObject) {
        Log.i("C++调用stopScan, flutterObject: $flutterObject");
        BleOperationManager().stopScan();
      },
    );
  }
  void _registerGattFunctions() {
    BleBindings.instance.registerGattFunctions(
      init: (nativeObject) {
        Log.i("C++调用init, nativeObject: $nativeObject");
        final flutterObjectId = DateTime.now().millisecondsSinceEpoch;
        BleFfiRegistry().registerGattMapping(nativeObject, flutterObjectId);
        return flutterObjectId;
      },
      uninit: (flutterObject) {
        Log.i("C++调用uninit, flutterObject: $flutterObject");
        BleFfiRegistry().removeGattMapping(flutterObject);
      },
      close: (flutterObject) {
        Log.i("C++调用close, flutterObject: $flutterObject");
        Future(() async {
          BleOperationManager().disconnect();
        });
      },
      connect: (flutterObject) {
        Log.i("C++调用connect, flutterObject: $flutterObject");
        _handleConnect();
      },
      disconnect: (flutterObject) {
        Log.i("C++调用disconnect, flutterObject: $flutterObject");
        Future(() async {
          BleOperationManager().disconnect();
        });
      },
      requestMtu: (flutterObject, mtu) => 1,
      writeCharacteristic: (flutterObject, characteristic) {
        Log.i("C++调用writeCharacteristic, flutterObject: $flutterObject");
        _handleWriteCharacteristic(characteristic);
        return 1;
      },
      setCharacteristicNotification: (flutterObject, characteristic, enable) {
        Log.i(
          "C++调用setCharacteristicNotification, flutterObject: $flutterObject, enable: $enable",
        );
        return 1;
      },
      getService: (flutterObject, uuid) {
        try {
          final uuidStr = uuid.cast<Utf8>().toDartString();
          Log.i(
            "C++调用getService: flutterObject: $flutterObject, uuid: $uuidStr",
          );
          final manager = BleOperationManager();
          final serviceId = manager.characteristicHandler.getService(uuidStr);
          return serviceId;
        } catch (e) {
          Log.e("获取服务错误: $e");
          return 0;
        }
      },
    );
  }
  void _handleConnect() {
    int handle = CurrentConnectingDevice().handle ?? 0;
    if (handle == 0) {
      BleNativeNotifier().notifyConnectionStateChange(
        FfiBluetoothProfileState.stateDisconnected,
      );
      return;
    }
    Future(() async {
      BleOperationManager().connect(deviceHandle: handle);
    });
  }
  void _handleWriteCharacteristic(
    Pointer<FFIGattCharacteristic> characteristic,
  ) {
    try {
      final config = BleConfig().getConfig(DeviceModeType.dx5);
      String writeUUID = config.writeCharacteristicUuid;
      List<int> value = [];
      for (int i = 0; i < characteristic.ref.value_len; i++) {
        value.add(characteristic.ref.value[i]);
      }
      Future(() async {
        BleOperationManager().writeCharacteristic(writeUUID, value);
      });
    } catch (e) {
      Log.e('处理写特征值请求失败: $e');
    }
  }
}

================
File: lib/bluetooth/ble_operation_manager.dart
================
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart';
import 'package:topping_ble_control/bluetooth/scanner/ble_scanner.dart';
import '../config/ble_config.dart';
import '../interfaces/bluetooth_operations.dart';
import '../model/enums/device_mode_type.dart';
import '../utils/log_util.dart';
import 'gatt/characteristic_handler.dart';
import 'connection/ble_connector.dart';
import '../model/bluetooth/ble_device.dart';
class BleOperationManager implements BluetoothOperations {
  final Completer<void> _initializedCompleter = Completer<void>();
  Future<void> get initialized => _initializedCompleter.future;
  static final BleOperationManager _instance = BleOperationManager._internal();
  factory BleOperationManager() => _instance;
  late final BleScanner _scanner;
  late final BleConnector _connector;
  late final BleCharacteristicHandler _characteristicHandler;
  StreamSubscription<List<BleDevice>>? scanResultsSubscription;
  BleScanner get scanner => _scanner;
  BleConnector get connector => _connector;
  BleCharacteristicHandler get characteristicHandler => _characteristicHandler;
  BleOperationManager._internal() {
    _initializeComponents();
    BleFfiInitializer().initialize();
  }
  Future<void> _initializeComponents() async {
    try {
      _scanner = BleScanner();
      _setupScanResultsListener();
      _connector = BleConnector();
      _characteristicHandler = BleCharacteristicHandler(_connector);
      await _requestPermissions();
      _initializedCompleter.complete();
    } catch (e) {
      _initializedCompleter.completeError(e);
    }
  }
  void _setupScanResultsListener() {
    scanResultsSubscription?.cancel();
  }
  Future<void> _requestPermissions() async {
    try {
      if (await FlutterBluePlus.isSupported == false) {
        throw Exception("Bluetooth is not supported by this device");
      }
      BluetoothAdapterState currentState =
          await FlutterBluePlus.adapterState.first;
      if (currentState != BluetoothAdapterState.on) {
        if (!kIsWeb && Platform.isAndroid) {
          await FlutterBluePlus.turnOn();
        }
        await FlutterBluePlus.adapterState
            .where((state) => state == BluetoothAdapterState.on)
            .first
            .timeout(
              Duration(seconds: 10),
              onTimeout: () => throw Exception("Bluetooth turn on timeout"),
            );
      }
      Log.i("蓝牙权限已获取并就绪");
    } catch (e) {
      Log.e("请求蓝牙权限失败: $e");
      rethrow;
    }
  }
  @override
  Future<void> startScan({
    List<DeviceModeType> deviceTypes = const [DeviceModeType.dx5],
    bool useNativeScanner = false,
  }) async {
    try {
      await initialized;
      if (useNativeScanner) {
        await BleFfiInitializer().ensureInitialized();
      }
      await _scanner.startScan(
        deviceTypes: deviceTypes,
        useNativeScanner: useNativeScanner,
      );
      final deviceNames = deviceTypes
          .map((type) => BleConfig().getConfig(type).deviceName)
          .join(', ');
      Log.i('开始${useNativeScanner ? "原生" : "Flutter"}扫描设备: $deviceNames');
    } catch (e) {
      Log.e('开始扫描失败: $e');
      rethrow;
    }
  }
  @override
  Future<void> stopScan() async {
    _scanner.stopScan();
  }
  @override
  Future<void> connect({required int deviceHandle}) async {
    try {
      Log.i("开始连接设备，句柄: $deviceHandle");
      _connector.connectToDevice(deviceHandle);
      return Future.value();
    } catch (e) {
      Log.e("连接设备失败: $e");
      return Future.error(e);
    }
  }
  @override
  void disconnect() {
    try {
      _connector.disconnectDevice();
      Log.i("断开设备连接成功");
    } catch (e) {
      Log.e("断开设备连接失败: $e");
      rethrow;
    }
  }
  @override
  Future<void> writeCharacteristic(String uuid, List<int> value) async {
    try {
      await initialized;
      await _characteristicHandler.writeCharacteristic(uuid, value);
      Log.i("写特征值成功");
    } catch (e) {
      Log.e("写特征值失败: $e");
      rethrow;
    }
  }
  @override
  Future<void> setCharacteristicNotification(String uuid, bool enable) async {
    try {
      await initialized;
      _characteristicHandler.setCharacteristicNotification(uuid, enable);
    } catch (e) {
      Log.e("设置特征值通知失败: $e");
      rethrow;
    }
  }
  void dispose() {
    Log.i("释放BLE管理器资源");
    scanResultsSubscription?.cancel();
    _scanner.dispose();
    _connector.dispose();
    _characteristicHandler.dispose();
  }
}

================
File: lib/config/ble_config.dart
================
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../model/enums/device_mode_type.dart';
import '../utils/log_util.dart';
abstract class BaseBleConfig {
  final Duration scanTimeout = const Duration(seconds: 15);
  final Duration scanThrottle = const Duration(milliseconds: 500);
  final int connectionRetryCount = 3;
  final Duration connectionTimeout = const Duration(seconds: 15);
  final Duration connectionMonitorInterval = const Duration(seconds: 30);
  final int defaultMtu = 512;
  final Duration writeTimeout = const Duration(seconds: 5);
  final bool verboseLogging = false;
  String get serviceUuid;
  String get writeCharacteristicUuid;
  String get notifyCharacteristicUuid;
  int get vendorId;
  int get productId;
  List<MsdFilter> get manufacturerFilters;
  String get deviceName;
}
class DX5IIBleConfig extends BaseBleConfig {
  @override
  String get serviceUuid => "000090FB-0000-1000-8000-00805F9B34FB";
  @override
  String get writeCharacteristicUuid => "00008efa-0000-1000-8000-00805f9b34fb";
  @override
  String get notifyCharacteristicUuid => "00009CF1-0000-1000-8000-00805F9B34FB";
  @override
  int get vendorId => 19295;
  @override
  int get productId => 0x6ac0;
  @override
  List<MsdFilter> get manufacturerFilters => [
    MsdFilter(vendorId, data: [0x6a, 0xc0]),
  ];
  @override
  String get deviceName => "DX5 II";
}
class DX9BleConfig extends BaseBleConfig {
  @override
  String get serviceUuid => "000090FB-0000-1000-8000-00805F9B34FB";
  @override
  String get writeCharacteristicUuid => "00008efa-0000-1000-8000-00805f9b34fb";
  @override
  String get notifyCharacteristicUuid => "00009CF1-0000-1000-8000-00805F9B34FB";
  @override
  int get vendorId => 19295;
  @override
  int get productId => 0x6ac1;
  @override
  List<MsdFilter> get manufacturerFilters => [
    MsdFilter(vendorId, data: [0x6a, 0xc1]),
  ];
  @override
  String get deviceName => "DX9";
}
class BleConfig {
  static final BleConfig _instance = BleConfig._internal();
  BleConfig._internal();
  factory BleConfig() => _instance;
  BaseBleConfig getConfig(DeviceModeType deviceType) {
    switch (deviceType) {
      case DeviceModeType.dx5:
        return DX5IIBleConfig();
      case DeviceModeType.dx9:
      return DX9BleConfig();
      case DeviceModeType.unknown:
        return DX5IIBleConfig();
    }
  }
  DeviceModeType identifyDeviceType(int vendorId, int productId) {
    Log.i("identifyDeviceType: vendorId: $vendorId, productId: $productId");
    if (vendorId == 19295) {
      if (productId == 0x6ac0) {
        return DeviceModeType.dx5;
      } else if (productId == 0x6ac1) {
        return DeviceModeType.dx9;
      }
    }
    return DeviceModeType.unknown;
  }
  static BaseBleConfig get dx5II => _instance.getConfig(DeviceModeType.dx5);
  static BaseBleConfig get dx9 => _instance.getConfig(DeviceModeType.dx9);
}

================
File: lib/bluetooth/scanner/ble_scanner.dart
================
import 'dart:async';
import 'dart:ffi';
import 'package:ffi/ffi.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../../config/ble_config.dart';
import '../../model/bluetooth/ble_device.dart';
import '../../model/enums/device_mode_type.dart';
import '../../registry/device_data_manager.dart';
import '../../utils/errors.dart';
import '../../utils/log_util.dart';
import '../ble_bindings.dart';
import '../ffi/ble_ffi_initializer.dart';
import '../ffi/ble_ffi_registry.dart';
import 'controller_scanner_bindings.dart';
class BleScanner {
  static final BleScanner _instance = BleScanner._internal();
  factory BleScanner() => _instance;
  final StreamController<List<BleDevice>> _scanResultsController =
      StreamController.broadcast();
  Stream<List<BleDevice>> get scanResults => _scanResultsController.stream;
  StreamSubscription<List<ScanResult>>? _scanResultsSubscription;
  StreamSubscription<bool>? _scanStatusSubscription;
  final StreamController<bool> _scanningController =
      StreamController.broadcast();
  bool _useNativeScanner = false;
  Stream<bool> get isScanning =>
      _useNativeScanner
          ? _scanningController.stream
          : FlutterBluePlus.isScanning;
  int? _nativeScannerId;
  final Map<int, BleScanner> _instanceMap = {};
  int _flutterObjectId = 0;
  Pointer<FFIControllerScannerCallback>? _scannerCallback;
  List<DeviceModeType> _currentDeviceTypes = [];
  bool _isNativeScanning = false;
  final List<BleDevice> _scannedDevices = [];
  bool _waitingForNativeResult = false;
  BleScanner._internal() {
    _initNativeScanner();
  }
  void _initNativeScanner() {
    try {
      _flutterObjectId = hashCode;
      _instanceMap[_flutterObjectId] = this;
      _scannerCallback = calloc<FFIControllerScannerCallback>();
      _setupNativeCallbacks();
      _createNativeScanner();
    } catch (e) {
      Log.e('初始化原生扫描器失败: $e');
    }
  }
  void _setupNativeCallbacks() {
    if (_scannerCallback == null) return;
    _scannerCallback!.ref.on_scan_results = Pointer.fromFunction<
      Void Function(Int64, Pointer<FFIControllerScanResult>, IntPtr)
    >(_onScanResults);
    _scannerCallback!.ref.on_scan_failed =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onScanFailed);
  }
  void _createNativeScanner() {
    try {
      if (_scannerCallback == null) return;
      _nativeScannerId = ControllerScannerBindings.instance.createScanner(
        _flutterObjectId,
        _scannerCallback!,
      );
      Log.i('原生扫描器创建成功，ID: $_nativeScannerId');
    } catch (e) {
      Log.e('创建原生扫描器失败: $e');
    }
  }
  Future<void> startScan({
    List<DeviceModeType> deviceTypes = const [
      DeviceModeType.dx5,
      DeviceModeType.dx9,
    ],
    bool useNativeScanner = false,
  }) async {
    try {
      _currentDeviceTypes = deviceTypes;
      _useNativeScanner = useNativeScanner;
      final deviceNamesStr = deviceTypes
          .map((t) => BleConfig().getConfig(t).deviceName)
          .join(', ');
      if (useNativeScanner) {
        await _startNativeScan(deviceTypes);
        Log.i('使用原生扫描器扫描设备类型: $deviceNamesStr');
      } else {
        await _startFlutterScan(deviceTypes);
        Log.i('使用Flutter扫描器扫描设备类型: $deviceNamesStr');
      }
    } catch (e) {
      if (_useNativeScanner) {
        _isNativeScanning = false;
        _scanningController.add(false);
      }
      Log.e('启动扫描失败: $e');
      BleErrorHandler.handleError(e);
      rethrow;
    }
  }
  Future<void> _startFlutterScan(List<DeviceModeType> deviceTypes) async {
    await _scanResultsSubscription?.cancel();
    _scanResultsSubscription = null;
    final List<MsdFilter> allFilters = [];
    Duration scanTimeout = const Duration(seconds: 15);
    var config = BleConfig().getConfig(DeviceModeType.dx5);
    allFilters.addAll(config.manufacturerFilters);
    scanTimeout = config.scanTimeout;
    await FlutterBluePlus.startScan(timeout: scanTimeout);
    _scanResultsSubscription = FlutterBluePlus.scanResults.listen(
      _processScanResults,
    );
    _scanStatusSubscription?.cancel();
    _scanStatusSubscription = FlutterBluePlus.isScanning.listen((isScanning) {
      if (!isScanning) {
        Log.i('检测到Flutter扫描停止');
      }
    });
  }
  Future<void> _startNativeScan(List<DeviceModeType> deviceTypes) async {
    try {
      await BleFfiInitializer().ensureInitialized();
      if (_nativeScannerId == null) {
        Log.i('原生扫描器ID为空，尝试重新创建');
        _createNativeScanner();
        if (_nativeScannerId == null) {
          throw Exception('原生扫描器尚未创建或创建失败');
        }
      }
      _scannedDevices.clear();
      _isNativeScanning = true;
      _scanningController.add(true);
      Log.i('开始原生扫描，扫描器ID: $_nativeScannerId');
      ControllerScannerBindings.instance.startScan(_nativeScannerId!);
      await Future.delayed(Duration(seconds: 2));
      if (_scannedDevices.isEmpty) {
        Log.i('原生扫描启动后2秒内没有发现设备，可能需要检查原生扫描器');
      }
    } catch (e) {
      Log.e('原生扫描器启动失败: $e');
      _isNativeScanning = false;
      _scanningController.add(false);
      rethrow;
    }
  }
  void stopScan() {
    try {
      if (_useNativeScanner) {
        if (_nativeScannerId != null) {
          ControllerScannerBindings.instance.stopScan(_nativeScannerId!);
        }
        _isNativeScanning = false;
        _scanningController.add(false);
      } else {
        FlutterBluePlus.stopScan();
        _scanResultsSubscription?.cancel();
        _scanResultsSubscription = null;
        _scanStatusSubscription?.cancel();
        _scanStatusSubscription = null;
      }
      Log.i(_useNativeScanner ? "原生扫描已停止" : "Flutter扫描已停止");
    } catch (e) {
      Log.e("停止扫描时出错: $e");
      BleErrorHandler.handleError(e);
    }
  }
  static void _onScanResults(
    int flutterObjectId,
    Pointer<FFIControllerScanResult> results,
    int count,
  ) {
    final scanner = _instance._instanceMap[flutterObjectId];
    if (scanner != null) {
      Log.i("Native回调扫描结果，找到对应实例，处理 $count 个设备");
      scanner._handleNativeScanResults(results, count);
    } else {
      Log.e('找不到flutterObjectId为$flutterObjectId的扫描器实例');
    }
  }
  static void _onScanFailed(int flutterObjectId, int errorCode) {
    final scanner = _instance._instanceMap[flutterObjectId];
    if (scanner != null) {
      scanner._handleNativeScanFailed(errorCode);
    } else {
      Log.e('找不到flutterObjectId为$flutterObjectId的扫描器实例');
    }
  }
  void _processScanResults(List<ScanResult> results) {
    if (_waitingForNativeResult) {
      Log.i("已有等待Native回调的请求，跳过此次Flutter扫描结果处理");
      return;
    }
    Log.i("Flutter扫描到 ${results.length} 个设备");
    final List<BleDevice> devices = List<BleDevice>.empty(growable: true);
    for (var result in results) {
      try {
        final device = BleDevice.fromScanResult(result);
        devices.add(device);
      } catch (e) {
        Log.e("处理扫描结果时出错: $e");
        continue;
      }
    }
    if (devices.isNotEmpty) {
      try {
        final nativeToFlutterMap = BleFfiRegistry().nativeToFlutterScannerMap;
        int? nativeObject;
        if (nativeToFlutterMap.isNotEmpty) {
          nativeObject = nativeToFlutterMap.keys.first;
          if (nativeObject != null && nativeObject > 0) {
            Log.i("获取到nativeObject: $nativeObject，准备发送扫描结果到C++");
            _waitingForNativeResult = true;
            _setupNativeCallbackTimeout();
            try {
              BleBindings.instance.reportScanResults(nativeObject, devices);
              Log.i("已将${devices.length}个设备发送到C++进行过滤");
              return;
            } catch (e) {
              Log.e("发送扫描结果到C++时出错: $e");
              _waitingForNativeResult = false;
            }
          } else {
            Log.e("获取到的nativeObject无效: $nativeObject");
          }
        } else {
          Log.e("没有找到有效的nativeObject，无法发送扫描结果到C++");
        }
        _fallbackToFlutterResults(devices);
      } catch (e) {
        Log.e("处理扫描结果过程中出错: $e");
        _waitingForNativeResult = false;
        _fallbackToFlutterResults(devices);
      }
    } else {
      Log.i("没有有效的设备，不进行处理");
    }
  }
  void _setupNativeCallbackTimeout() {
    Future.delayed(Duration(seconds: 3), () {
      if (_waitingForNativeResult) {
        Log.e("等待Native回调超时，重置等待状态");
        _waitingForNativeResult = false;
      }
    });
  }
  void _handleNativeScanResults(
    Pointer<FFIControllerScanResult> results,
    int count,
  ) {
    Log.i("原生扫描到 $count 个设备");
    _waitingForNativeResult = false;
    if (results == null || count <= 0) {
      Log.e("原生扫描结果无效: 指针=${results != null}, 数量=$count");
      return;
    }
    try {
      DeviceDataManager().clearScanResults();
      _scannedDevices.clear();
      for (int i = 0; i < count; i++) {
        try {
          final result = results[i];
          String deviceName = "";
          try {
            if (result.name != nullptr) {
              deviceName = result.name.toDartString();
            }
          } catch (e) {
            Log.e("获取设备名称时出错: $e");
          }
          final deviceHandle = result.device;
          final rssi = result.rssi;
          final deviceType = _determineDeviceType(deviceName);
          if (!_currentDeviceTypes.contains(deviceType)) {
            continue;
          }
          final device = BleDevice(
            id: deviceHandle.hashCode,
            name: deviceName,
            rssi: rssi,
            nativeHandle: deviceHandle,
            deviceType: deviceType,
          );
          _processDevice(device);
        } catch (e) {
          Log.e("处理第 $i 个原生扫描结果时出错: $e");
          continue;
        }
      }
      if (_scannedDevices.isNotEmpty) {
        Log.i("发送Native过滤后的 ${_scannedDevices.length} 个设备到结果流");
        _scanResultsController.add(List.from(_scannedDevices));
      } else {
        Log.i("原生扫描没有返回任何设备");
      }
    } catch (e) {
      Log.e("处理原生扫描结果时出错: $e");
    }
  }
  void _handleNativeScanFailed(int errorCode) {
    Log.e("原生扫描失败，错误码: $errorCode");
    _waitingForNativeResult = false;
    _isNativeScanning = false;
    _scanningController.add(false);
  }
  void _fallbackToFlutterResults(List<BleDevice> devices) {
    _waitingForNativeResult = false;
    DeviceDataManager().clearScanResults();
    _scannedDevices.clear();
    for (var device in devices) {
      _processDevice(device);
    }
    if (_scannedDevices.isNotEmpty) {
      Log.i("回退：发送Flutter扫描的 ${_scannedDevices.length} 个设备到结果流");
      _scanResultsController.add(List.from(_scannedDevices));
    }
  }
  void _processDevice(BleDevice device) {
    final existingDevice = DeviceDataManager().findDevice(
      macAddress: device.flutterDevice?.remoteId.str,
      handle: device.nativeHandle,
    );
    if (existingDevice != null && existingDevice.isPersistent) {
      existingDevice.rssi = device.rssi;
      _updateScannedDevicesList(existingDevice);
    } else {
      DeviceDataManager().registerDevice(device);
      _updateScannedDevicesList(device);
    }
  }
  void _updateScannedDevicesList(BleDevice device) {
    final existingIndex = _scannedDevices.indexWhere(
      (d) => d.nativeHandle == device.nativeHandle,
    );
    if (existingIndex >= 0) {
      _scannedDevices[existingIndex] = device;
    } else {
      _scannedDevices.add(device);
    }
  }
  DeviceModeType _determineDeviceType(String deviceName) {
    if (deviceName.contains('DX5 II') || deviceName.contains('DX5II')) {
      return DeviceModeType.dx5;
    } else if (deviceName.contains('D90') || deviceName.contains('D900')) {
      return DeviceModeType.dx9;
    }
    return DeviceModeType.unknown;
  }
  void dispose() {
    stopScan();
    if (_nativeScannerId != null) {
      ControllerScannerBindings.instance.destroyScanner(_nativeScannerId!);
      _nativeScannerId = null;
    }
    _instanceMap.remove(_flutterObjectId);
    if (_scannerCallback != null) {
      calloc.free(_scannerCallback!);
      _scannerCallback = null;
    }
    _scanResultsController.close();
    _scanningController.close();
  }
}



================================================================
End of Codebase
================================================================
