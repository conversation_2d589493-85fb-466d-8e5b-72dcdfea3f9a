#!/bin/bash
# 为不同环境构建Flutter应用的脚本

# 检查参数
if [ "$1" != "dev" ] && [ "$1" != "prod" ]; then
  echo "用法: ./build_flavors.sh [dev|prod] [apk|appbundle|ios]"
  echo "  dev: 测试环境，使用较小的versionCode和带有-dev后缀的versionName"
  echo "  prod: 生产环境，使用较大的versionCode和正式的versionName"
  echo "  apk: 构建Android APK"
  echo "  appbundle: 构建Android App Bundle"
  echo "  ios: 构建iOS应用"
  exit 1
fi

# 获取参数
FLAVOR=$1
BUILD_TYPE=$2

# 检查构建类型
if [ "$BUILD_TYPE" != "apk" ] && [ "$BUILD_TYPE" != "appbundle" ] && [ "$BUILD_TYPE" != "ios" ]; then
  echo "错误: 构建类型必须是 apk, appbundle 或 ios"
  exit 1
fi

# 设置环境变量
export FLAVOR=$FLAVOR

# 显示当前构建信息
echo "开始构建 $FLAVOR 环境的应用 ($BUILD_TYPE)..."

# 根据构建类型执行不同的命令
if [ "$BUILD_TYPE" = "apk" ]; then
  flutter build apk --flavor $FLAVOR -t lib/main.dart
elif [ "$BUILD_TYPE" = "appbundle" ]; then
  flutter build appbundle --flavor $FLAVOR -t lib/main.dart
elif [ "$BUILD_TYPE" = "ios" ]; then
  # iOS构建不支持--flavor选项，但我们可以使用--dart-define传递环境变量
  flutter build ios --dart-define=FLAVOR=$FLAVOR -t lib/main.dart
fi

# 显示构建完成信息
echo "构建完成！"
echo "已生成 $FLAVOR 环境的 $BUILD_TYPE 应用。"

# 对于iOS构建，提供额外说明
if [ "$BUILD_TYPE" = "ios" ]; then
  echo "注意：对于iOS应用，您需要在Xcode中打开项目并设置正确的版本号。"
  echo "位置：Runner > Info.plist > CFBundleVersion 和 CFBundleShortVersionString"
fi 