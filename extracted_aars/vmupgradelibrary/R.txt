int anim abc_fade_in 0x7f010001
int anim abc_fade_out 0x7f010002
int anim abc_grow_fade_in_from_bottom 0x7f010003
int anim abc_popup_enter 0x7f010004
int anim abc_popup_exit 0x7f010005
int anim abc_shrink_fade_out_from_bottom 0x7f010006
int anim abc_slide_in_bottom 0x7f010007
int anim abc_slide_in_top 0x7f010008
int anim abc_slide_out_bottom 0x7f010009
int anim abc_slide_out_top 0x7f01000a
int anim abc_tooltip_enter 0x7f01000b
int anim abc_tooltip_exit 0x7f01000c
int attr actionBarDivider 0x7f040001
int attr actionBarItemBackground 0x7f040002
int attr actionBarPopupTheme 0x7f040003
int attr actionBarSize 0x7f040004
int attr actionBarSplitStyle 0x7f040005
int attr actionBarStyle 0x7f040006
int attr actionBarTabBarStyle 0x7f040007
int attr actionBarTabStyle 0x7f040008
int attr actionBarTabTextStyle 0x7f040009
int attr actionBarTheme 0x7f04000a
int attr actionBarWidgetTheme 0x7f04000b
int attr actionButtonStyle 0x7f04000c
int attr actionDropDownStyle 0x7f04000d
int attr actionLayout 0x7f04000e
int attr actionMenuTextAppearance 0x7f04000f
int attr actionMenuTextColor 0x7f040010
int attr actionModeBackground 0x7f040011
int attr actionModeCloseButtonStyle 0x7f040012
int attr actionModeCloseDrawable 0x7f040013
int attr actionModeCopyDrawable 0x7f040014
int attr actionModeCutDrawable 0x7f040015
int attr actionModeFindDrawable 0x7f040016
int attr actionModePasteDrawable 0x7f040017
int attr actionModePopupWindowStyle 0x7f040018
int attr actionModeSelectAllDrawable 0x7f040019
int attr actionModeShareDrawable 0x7f04001a
int attr actionModeSplitBackground 0x7f04001b
int attr actionModeStyle 0x7f04001c
int attr actionModeWebSearchDrawable 0x7f04001d
int attr actionOverflowButtonStyle 0x7f04001e
int attr actionOverflowMenuStyle 0x7f04001f
int attr actionProviderClass 0x7f040020
int attr actionViewClass 0x7f040021
int attr activityChooserViewStyle 0x7f040022
int attr alertDialogButtonGroupStyle 0x7f040023
int attr alertDialogCenterButtons 0x7f040024
int attr alertDialogStyle 0x7f040025
int attr alertDialogTheme 0x7f040026
int attr allowStacking 0x7f040027
int attr alpha 0x7f040028
int attr alphabeticModifiers 0x7f040029
int attr arrowHeadLength 0x7f04002a
int attr arrowShaftLength 0x7f04002b
int attr autoCompleteTextViewStyle 0x7f04002c
int attr autoSizeMaxTextSize 0x7f04002d
int attr autoSizeMinTextSize 0x7f04002e
int attr autoSizePresetSizes 0x7f04002f
int attr autoSizeStepGranularity 0x7f040030
int attr autoSizeTextType 0x7f040031
int attr background 0x7f040032
int attr backgroundSplit 0x7f040033
int attr backgroundStacked 0x7f040034
int attr backgroundTint 0x7f040035
int attr backgroundTintMode 0x7f040036
int attr barLength 0x7f040037
int attr borderlessButtonStyle 0x7f040038
int attr buttonBarButtonStyle 0x7f040039
int attr buttonBarNegativeButtonStyle 0x7f04003a
int attr buttonBarNeutralButtonStyle 0x7f04003b
int attr buttonBarPositiveButtonStyle 0x7f04003c
int attr buttonBarStyle 0x7f04003d
int attr buttonGravity 0x7f04003e
int attr buttonIconDimen 0x7f04003f
int attr buttonPanelSideLayout 0x7f040040
int attr buttonStyle 0x7f040041
int attr buttonStyleSmall 0x7f040042
int attr buttonTint 0x7f040043
int attr buttonTintMode 0x7f040044
int attr checkboxStyle 0x7f040045
int attr checkedTextViewStyle 0x7f040046
int attr closeIcon 0x7f040047
int attr closeItemLayout 0x7f040048
int attr collapseContentDescription 0x7f040049
int attr collapseIcon 0x7f04004a
int attr color 0x7f04004b
int attr colorAccent 0x7f04004c
int attr colorBackgroundFloating 0x7f04004d
int attr colorButtonNormal 0x7f04004e
int attr colorControlActivated 0x7f04004f
int attr colorControlHighlight 0x7f040050
int attr colorControlNormal 0x7f040051
int attr colorError 0x7f040052
int attr colorPrimary 0x7f040053
int attr colorPrimaryDark 0x7f040054
int attr colorSwitchThumbNormal 0x7f040055
int attr commitIcon 0x7f040056
int attr contentDescription 0x7f040057
int attr contentInsetEnd 0x7f040058
int attr contentInsetEndWithActions 0x7f040059
int attr contentInsetLeft 0x7f04005a
int attr contentInsetRight 0x7f04005b
int attr contentInsetStart 0x7f04005c
int attr contentInsetStartWithNavigation 0x7f04005d
int attr controlBackground 0x7f04005e
int attr coordinatorLayoutStyle 0x7f04005f
int attr customNavigationLayout 0x7f040060
int attr defaultQueryHint 0x7f040061
int attr dialogPreferredPadding 0x7f040062
int attr dialogTheme 0x7f040063
int attr displayOptions 0x7f040064
int attr divider 0x7f040065
int attr dividerHorizontal 0x7f040066
int attr dividerPadding 0x7f040067
int attr dividerVertical 0x7f040068
int attr drawableSize 0x7f040069
int attr drawerArrowStyle 0x7f04006a
int attr dropDownListViewStyle 0x7f04006b
int attr dropdownListPreferredItemHeight 0x7f04006c
int attr editTextBackground 0x7f04006d
int attr editTextColor 0x7f04006e
int attr editTextStyle 0x7f04006f
int attr elevation 0x7f040070
int attr expandActivityOverflowButtonDrawable 0x7f040071
int attr font 0x7f040072
int attr fontFamily 0x7f040073
int attr fontProviderAuthority 0x7f040074
int attr fontProviderCerts 0x7f040075
int attr fontProviderFetchStrategy 0x7f040076
int attr fontProviderFetchTimeout 0x7f040077
int attr fontProviderPackage 0x7f040078
int attr fontProviderQuery 0x7f040079
int attr fontStyle 0x7f04007a
int attr fontWeight 0x7f04007b
int attr gapBetweenBars 0x7f04007c
int attr goIcon 0x7f04007d
int attr height 0x7f04007e
int attr hideOnContentScroll 0x7f04007f
int attr homeAsUpIndicator 0x7f040080
int attr homeLayout 0x7f040081
int attr icon 0x7f040082
int attr iconTint 0x7f040083
int attr iconTintMode 0x7f040084
int attr iconifiedByDefault 0x7f040085
int attr imageButtonStyle 0x7f040086
int attr indeterminateProgressStyle 0x7f040087
int attr initialActivityCount 0x7f040088
int attr isLightTheme 0x7f040089
int attr itemPadding 0x7f04008a
int attr keylines 0x7f04008b
int attr layout 0x7f04008c
int attr layout_anchor 0x7f04008d
int attr layout_anchorGravity 0x7f04008e
int attr layout_behavior 0x7f04008f
int attr layout_dodgeInsetEdges 0x7f040090
int attr layout_insetEdge 0x7f040091
int attr layout_keyline 0x7f040092
int attr listChoiceBackgroundIndicator 0x7f040093
int attr listDividerAlertDialog 0x7f040094
int attr listItemLayout 0x7f040095
int attr listLayout 0x7f040096
int attr listMenuViewStyle 0x7f040097
int attr listPopupWindowStyle 0x7f040098
int attr listPreferredItemHeight 0x7f040099
int attr listPreferredItemHeightLarge 0x7f04009a
int attr listPreferredItemHeightSmall 0x7f04009b
int attr listPreferredItemPaddingLeft 0x7f04009c
int attr listPreferredItemPaddingRight 0x7f04009d
int attr logo 0x7f04009e
int attr logoDescription 0x7f04009f
int attr maxButtonHeight 0x7f0400a0
int attr measureWithLargestChild 0x7f0400a1
int attr multiChoiceItemLayout 0x7f0400a2
int attr navigationContentDescription 0x7f0400a3
int attr navigationIcon 0x7f0400a4
int attr navigationMode 0x7f0400a5
int attr numericModifiers 0x7f0400a6
int attr overlapAnchor 0x7f0400a7
int attr paddingBottomNoButtons 0x7f0400a8
int attr paddingEnd 0x7f0400a9
int attr paddingStart 0x7f0400aa
int attr paddingTopNoTitle 0x7f0400ab
int attr panelBackground 0x7f0400ac
int attr panelMenuListTheme 0x7f0400ad
int attr panelMenuListWidth 0x7f0400ae
int attr popupMenuStyle 0x7f0400af
int attr popupTheme 0x7f0400b0
int attr popupWindowStyle 0x7f0400b1
int attr preserveIconSpacing 0x7f0400b2
int attr progressBarPadding 0x7f0400b3
int attr progressBarStyle 0x7f0400b4
int attr queryBackground 0x7f0400b5
int attr queryHint 0x7f0400b6
int attr radioButtonStyle 0x7f0400b7
int attr ratingBarStyle 0x7f0400b8
int attr ratingBarStyleIndicator 0x7f0400b9
int attr ratingBarStyleSmall 0x7f0400ba
int attr searchHintIcon 0x7f0400bb
int attr searchIcon 0x7f0400bc
int attr searchViewStyle 0x7f0400bd
int attr seekBarStyle 0x7f0400be
int attr selectableItemBackground 0x7f0400bf
int attr selectableItemBackgroundBorderless 0x7f0400c0
int attr showAsAction 0x7f0400c1
int attr showDividers 0x7f0400c2
int attr showText 0x7f0400c3
int attr showTitle 0x7f0400c4
int attr singleChoiceItemLayout 0x7f0400c5
int attr spinBars 0x7f0400c6
int attr spinnerDropDownItemStyle 0x7f0400c7
int attr spinnerStyle 0x7f0400c8
int attr splitTrack 0x7f0400c9
int attr srcCompat 0x7f0400ca
int attr state_above_anchor 0x7f0400cb
int attr statusBarBackground 0x7f0400cc
int attr subMenuArrow 0x7f0400cd
int attr submitBackground 0x7f0400ce
int attr subtitle 0x7f0400cf
int attr subtitleTextAppearance 0x7f0400d0
int attr subtitleTextColor 0x7f0400d1
int attr subtitleTextStyle 0x7f0400d2
int attr suggestionRowLayout 0x7f0400d3
int attr switchMinWidth 0x7f0400d4
int attr switchPadding 0x7f0400d5
int attr switchStyle 0x7f0400d6
int attr switchTextAppearance 0x7f0400d7
int attr textAllCaps 0x7f0400d8
int attr textAppearanceLargePopupMenu 0x7f0400d9
int attr textAppearanceListItem 0x7f0400da
int attr textAppearanceListItemSecondary 0x7f0400db
int attr textAppearanceListItemSmall 0x7f0400dc
int attr textAppearancePopupMenuHeader 0x7f0400dd
int attr textAppearanceSearchResultSubtitle 0x7f0400de
int attr textAppearanceSearchResultTitle 0x7f0400df
int attr textAppearanceSmallPopupMenu 0x7f0400e0
int attr textColorAlertDialogListItem 0x7f0400e1
int attr textColorSearchUrl 0x7f0400e2
int attr theme 0x7f0400e3
int attr thickness 0x7f0400e4
int attr thumbTextPadding 0x7f0400e5
int attr thumbTint 0x7f0400e6
int attr thumbTintMode 0x7f0400e7
int attr tickMark 0x7f0400e8
int attr tickMarkTint 0x7f0400e9
int attr tickMarkTintMode 0x7f0400ea
int attr tint 0x7f0400eb
int attr tintMode 0x7f0400ec
int attr title 0x7f0400ed
int attr titleMargin 0x7f0400ee
int attr titleMarginBottom 0x7f0400ef
int attr titleMarginEnd 0x7f0400f0
int attr titleMarginStart 0x7f0400f1
int attr titleMarginTop 0x7f0400f2
int attr titleMargins 0x7f0400f3
int attr titleTextAppearance 0x7f0400f4
int attr titleTextColor 0x7f0400f5
int attr titleTextStyle 0x7f0400f6
int attr toolbarNavigationButtonStyle 0x7f0400f7
int attr toolbarStyle 0x7f0400f8
int attr tooltipForegroundColor 0x7f0400f9
int attr tooltipFrameBackground 0x7f0400fa
int attr tooltipText 0x7f0400fb
int attr track 0x7f0400fc
int attr trackTint 0x7f0400fd
int attr trackTintMode 0x7f0400fe
int attr viewInflaterClass 0x7f0400ff
int attr voiceIcon 0x7f040100
int attr windowActionBar 0x7f040101
int attr windowActionBarOverlay 0x7f040102
int attr windowActionModeOverlay 0x7f040103
int attr windowFixedHeightMajor 0x7f040104
int attr windowFixedHeightMinor 0x7f040105
int attr windowFixedWidthMajor 0x7f040106
int attr windowFixedWidthMinor 0x7f040107
int attr windowMinWidthMajor 0x7f040108
int attr windowMinWidthMinor 0x7f040109
int attr windowNoTitle 0x7f04010a
int bool abc_action_bar_embed_tabs 0x7f050001
int bool abc_allow_stacked_button_bar 0x7f050002
int bool abc_config_actionMenuItemAllCaps 0x7f050003
int bool abc_config_showMenuShortcutsWhenKeyboardPresent 0x7f050004
int color abc_background_cache_hint_selector_material_dark 0x7f060001
int color abc_background_cache_hint_selector_material_light 0x7f060002
int color abc_btn_colored_borderless_text_material 0x7f060003
int color abc_btn_colored_text_material 0x7f060004
int color abc_color_highlight_material 0x7f060005
int color abc_hint_foreground_material_dark 0x7f060006
int color abc_hint_foreground_material_light 0x7f060007
int color abc_input_method_navigation_guard 0x7f060008
int color abc_primary_text_disable_only_material_dark 0x7f060009
int color abc_primary_text_disable_only_material_light 0x7f06000a
int color abc_primary_text_material_dark 0x7f06000b
int color abc_primary_text_material_light 0x7f06000c
int color abc_search_url_text 0x7f06000d
int color abc_search_url_text_normal 0x7f06000e
int color abc_search_url_text_pressed 0x7f06000f
int color abc_search_url_text_selected 0x7f060010
int color abc_secondary_text_material_dark 0x7f060011
int color abc_secondary_text_material_light 0x7f060012
int color abc_tint_btn_checkable 0x7f060013
int color abc_tint_default 0x7f060014
int color abc_tint_edittext 0x7f060015
int color abc_tint_seek_thumb 0x7f060016
int color abc_tint_spinner 0x7f060017
int color abc_tint_switch_track 0x7f060018
int color accent_material_dark 0x7f060019
int color accent_material_light 0x7f06001a
int color background_floating_material_dark 0x7f06001b
int color background_floating_material_light 0x7f06001c
int color background_material_dark 0x7f06001d
int color background_material_light 0x7f06001e
int color bright_foreground_disabled_material_dark 0x7f06001f
int color bright_foreground_disabled_material_light 0x7f060020
int color bright_foreground_inverse_material_dark 0x7f060021
int color bright_foreground_inverse_material_light 0x7f060022
int color bright_foreground_material_dark 0x7f060023
int color bright_foreground_material_light 0x7f060024
int color button_material_dark 0x7f060025
int color button_material_light 0x7f060026
int color dim_foreground_disabled_material_dark 0x7f060027
int color dim_foreground_disabled_material_light 0x7f060028
int color dim_foreground_material_dark 0x7f060029
int color dim_foreground_material_light 0x7f06002a
int color error_color_material 0x7f06002b
int color foreground_material_dark 0x7f06002c
int color foreground_material_light 0x7f06002d
int color highlighted_text_material_dark 0x7f06002e
int color highlighted_text_material_light 0x7f06002f
int color material_blue_grey_800 0x7f060030
int color material_blue_grey_900 0x7f060031
int color material_blue_grey_950 0x7f060032
int color material_deep_teal_200 0x7f060033
int color material_deep_teal_500 0x7f060034
int color material_grey_100 0x7f060035
int color material_grey_300 0x7f060036
int color material_grey_50 0x7f060037
int color material_grey_600 0x7f060038
int color material_grey_800 0x7f060039
int color material_grey_850 0x7f06003a
int color material_grey_900 0x7f06003b
int color notification_action_color_filter 0x7f06003c
int color notification_icon_bg_color 0x7f06003d
int color primary_dark_material_dark 0x7f06003e
int color primary_dark_material_light 0x7f06003f
int color primary_material_dark 0x7f060040
int color primary_material_light 0x7f060041
int color primary_text_default_material_dark 0x7f060042
int color primary_text_default_material_light 0x7f060043
int color primary_text_disabled_material_dark 0x7f060044
int color primary_text_disabled_material_light 0x7f060045
int color ripple_material_dark 0x7f060046
int color ripple_material_light 0x7f060047
int color secondary_text_default_material_dark 0x7f060048
int color secondary_text_default_material_light 0x7f060049
int color secondary_text_disabled_material_dark 0x7f06004a
int color secondary_text_disabled_material_light 0x7f06004b
int color switch_thumb_disabled_material_dark 0x7f06004c
int color switch_thumb_disabled_material_light 0x7f06004d
int color switch_thumb_material_dark 0x7f06004e
int color switch_thumb_material_light 0x7f06004f
int color switch_thumb_normal_material_dark 0x7f060050
int color switch_thumb_normal_material_light 0x7f060051
int color tooltip_background_dark 0x7f060052
int color tooltip_background_light 0x7f060053
int dimen abc_action_bar_content_inset_material 0x7f080001
int dimen abc_action_bar_content_inset_with_nav 0x7f080002
int dimen abc_action_bar_default_height_material 0x7f080003
int dimen abc_action_bar_default_padding_end_material 0x7f080004
int dimen abc_action_bar_default_padding_start_material 0x7f080005
int dimen abc_action_bar_elevation_material 0x7f080006
int dimen abc_action_bar_icon_vertical_padding_material 0x7f080007
int dimen abc_action_bar_overflow_padding_end_material 0x7f080008
int dimen abc_action_bar_overflow_padding_start_material 0x7f080009
int dimen abc_action_bar_progress_bar_size 0x7f08000a
int dimen abc_action_bar_stacked_max_height 0x7f08000b
int dimen abc_action_bar_stacked_tab_max_width 0x7f08000c
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f08000d
int dimen abc_action_bar_subtitle_top_margin_material 0x7f08000e
int dimen abc_action_button_min_height_material 0x7f08000f
int dimen abc_action_button_min_width_material 0x7f080010
int dimen abc_action_button_min_width_overflow_material 0x7f080011
int dimen abc_alert_dialog_button_bar_height 0x7f080012
int dimen abc_alert_dialog_button_dimen 0x7f080013
int dimen abc_button_inset_horizontal_material 0x7f080014
int dimen abc_button_inset_vertical_material 0x7f080015
int dimen abc_button_padding_horizontal_material 0x7f080016
int dimen abc_button_padding_vertical_material 0x7f080017
int dimen abc_cascading_menus_min_smallest_width 0x7f080018
int dimen abc_config_prefDialogWidth 0x7f080019
int dimen abc_control_corner_material 0x7f08001a
int dimen abc_control_inset_material 0x7f08001b
int dimen abc_control_padding_material 0x7f08001c
int dimen abc_dialog_fixed_height_major 0x7f08001d
int dimen abc_dialog_fixed_height_minor 0x7f08001e
int dimen abc_dialog_fixed_width_major 0x7f08001f
int dimen abc_dialog_fixed_width_minor 0x7f080020
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f080021
int dimen abc_dialog_list_padding_top_no_title 0x7f080022
int dimen abc_dialog_min_width_major 0x7f080023
int dimen abc_dialog_min_width_minor 0x7f080024
int dimen abc_dialog_padding_material 0x7f080025
int dimen abc_dialog_padding_top_material 0x7f080026
int dimen abc_dialog_title_divider_material 0x7f080027
int dimen abc_disabled_alpha_material_dark 0x7f080028
int dimen abc_disabled_alpha_material_light 0x7f080029
int dimen abc_dropdownitem_icon_width 0x7f08002a
int dimen abc_dropdownitem_text_padding_left 0x7f08002b
int dimen abc_dropdownitem_text_padding_right 0x7f08002c
int dimen abc_edit_text_inset_bottom_material 0x7f08002d
int dimen abc_edit_text_inset_horizontal_material 0x7f08002e
int dimen abc_edit_text_inset_top_material 0x7f08002f
int dimen abc_floating_window_z 0x7f080030
int dimen abc_list_item_padding_horizontal_material 0x7f080031
int dimen abc_panel_menu_list_width 0x7f080032
int dimen abc_progress_bar_height_material 0x7f080033
int dimen abc_search_view_preferred_height 0x7f080034
int dimen abc_search_view_preferred_width 0x7f080035
int dimen abc_seekbar_track_background_height_material 0x7f080036
int dimen abc_seekbar_track_progress_height_material 0x7f080037
int dimen abc_select_dialog_padding_start_material 0x7f080038
int dimen abc_switch_padding 0x7f080039
int dimen abc_text_size_body_1_material 0x7f08003a
int dimen abc_text_size_body_2_material 0x7f08003b
int dimen abc_text_size_button_material 0x7f08003c
int dimen abc_text_size_caption_material 0x7f08003d
int dimen abc_text_size_display_1_material 0x7f08003e
int dimen abc_text_size_display_2_material 0x7f08003f
int dimen abc_text_size_display_3_material 0x7f080040
int dimen abc_text_size_display_4_material 0x7f080041
int dimen abc_text_size_headline_material 0x7f080042
int dimen abc_text_size_large_material 0x7f080043
int dimen abc_text_size_medium_material 0x7f080044
int dimen abc_text_size_menu_header_material 0x7f080045
int dimen abc_text_size_menu_material 0x7f080046
int dimen abc_text_size_small_material 0x7f080047
int dimen abc_text_size_subhead_material 0x7f080048
int dimen abc_text_size_subtitle_material_toolbar 0x7f080049
int dimen abc_text_size_title_material 0x7f08004a
int dimen abc_text_size_title_material_toolbar 0x7f08004b
int dimen compat_button_inset_horizontal_material 0x7f08004c
int dimen compat_button_inset_vertical_material 0x7f08004d
int dimen compat_button_padding_horizontal_material 0x7f08004e
int dimen compat_button_padding_vertical_material 0x7f08004f
int dimen compat_control_corner_material 0x7f080050
int dimen disabled_alpha_material_dark 0x7f080051
int dimen disabled_alpha_material_light 0x7f080052
int dimen highlight_alpha_material_colored 0x7f080053
int dimen highlight_alpha_material_dark 0x7f080054
int dimen highlight_alpha_material_light 0x7f080055
int dimen hint_alpha_material_dark 0x7f080056
int dimen hint_alpha_material_light 0x7f080057
int dimen hint_pressed_alpha_material_dark 0x7f080058
int dimen hint_pressed_alpha_material_light 0x7f080059
int dimen notification_action_icon_size 0x7f08005a
int dimen notification_action_text_size 0x7f08005b
int dimen notification_big_circle_margin 0x7f08005c
int dimen notification_content_margin_start 0x7f08005d
int dimen notification_large_icon_height 0x7f08005e
int dimen notification_large_icon_width 0x7f08005f
int dimen notification_main_column_padding_top 0x7f080060
int dimen notification_media_narrow_margin 0x7f080061
int dimen notification_right_icon_size 0x7f080062
int dimen notification_right_side_padding_top 0x7f080063
int dimen notification_small_icon_background_padding 0x7f080064
int dimen notification_small_icon_size_as_large 0x7f080065
int dimen notification_subtext_size 0x7f080066
int dimen notification_top_pad 0x7f080067
int dimen notification_top_pad_large_text 0x7f080068
int dimen tooltip_corner_radius 0x7f080069
int dimen tooltip_horizontal_padding 0x7f08006a
int dimen tooltip_margin 0x7f08006b
int dimen tooltip_precise_anchor_extra_offset 0x7f08006c
int dimen tooltip_precise_anchor_threshold 0x7f08006d
int dimen tooltip_vertical_padding 0x7f08006e
int dimen tooltip_y_offset_non_touch 0x7f08006f
int dimen tooltip_y_offset_touch 0x7f080070
int drawable abc_ab_share_pack_mtrl_alpha 0x7f090001
int drawable abc_action_bar_item_background_material 0x7f090002
int drawable abc_btn_borderless_material 0x7f090003
int drawable abc_btn_check_material 0x7f090004
int drawable abc_btn_check_to_on_mtrl_000 0x7f090005
int drawable abc_btn_check_to_on_mtrl_015 0x7f090006
int drawable abc_btn_colored_material 0x7f090007
int drawable abc_btn_default_mtrl_shape 0x7f090008
int drawable abc_btn_radio_material 0x7f090009
int drawable abc_btn_radio_to_on_mtrl_000 0x7f09000a
int drawable abc_btn_radio_to_on_mtrl_015 0x7f09000b
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f09000c
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f09000d
int drawable abc_cab_background_internal_bg 0x7f09000e
int drawable abc_cab_background_top_material 0x7f09000f
int drawable abc_cab_background_top_mtrl_alpha 0x7f090010
int drawable abc_control_background_material 0x7f090011
int drawable abc_dialog_material_background 0x7f090012
int drawable abc_edit_text_material 0x7f090013
int drawable abc_ic_ab_back_material 0x7f090014
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f090015
int drawable abc_ic_clear_material 0x7f090016
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f090017
int drawable abc_ic_go_search_api_material 0x7f090018
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f090019
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f09001a
int drawable abc_ic_menu_overflow_material 0x7f09001b
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f09001c
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f09001d
int drawable abc_ic_menu_share_mtrl_alpha 0x7f09001e
int drawable abc_ic_search_api_material 0x7f09001f
int drawable abc_ic_star_black_16dp 0x7f090020
int drawable abc_ic_star_black_36dp 0x7f090021
int drawable abc_ic_star_black_48dp 0x7f090022
int drawable abc_ic_star_half_black_16dp 0x7f090023
int drawable abc_ic_star_half_black_36dp 0x7f090024
int drawable abc_ic_star_half_black_48dp 0x7f090025
int drawable abc_ic_voice_search_api_material 0x7f090026
int drawable abc_item_background_holo_dark 0x7f090027
int drawable abc_item_background_holo_light 0x7f090028
int drawable abc_list_divider_mtrl_alpha 0x7f090029
int drawable abc_list_focused_holo 0x7f09002a
int drawable abc_list_longpressed_holo 0x7f09002b
int drawable abc_list_pressed_holo_dark 0x7f09002c
int drawable abc_list_pressed_holo_light 0x7f09002d
int drawable abc_list_selector_background_transition_holo_dark 0x7f09002e
int drawable abc_list_selector_background_transition_holo_light 0x7f09002f
int drawable abc_list_selector_disabled_holo_dark 0x7f090030
int drawable abc_list_selector_disabled_holo_light 0x7f090031
int drawable abc_list_selector_holo_dark 0x7f090032
int drawable abc_list_selector_holo_light 0x7f090033
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f090034
int drawable abc_popup_background_mtrl_mult 0x7f090035
int drawable abc_ratingbar_indicator_material 0x7f090036
int drawable abc_ratingbar_material 0x7f090037
int drawable abc_ratingbar_small_material 0x7f090038
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f090039
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f09003a
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f09003b
int drawable abc_scrubber_primary_mtrl_alpha 0x7f09003c
int drawable abc_scrubber_track_mtrl_alpha 0x7f09003d
int drawable abc_seekbar_thumb_material 0x7f09003e
int drawable abc_seekbar_tick_mark_material 0x7f09003f
int drawable abc_seekbar_track_material 0x7f090040
int drawable abc_spinner_mtrl_am_alpha 0x7f090041
int drawable abc_spinner_textfield_background_material 0x7f090042
int drawable abc_switch_thumb_material 0x7f090043
int drawable abc_switch_track_mtrl_alpha 0x7f090044
int drawable abc_tab_indicator_material 0x7f090045
int drawable abc_tab_indicator_mtrl_alpha 0x7f090046
int drawable abc_text_cursor_material 0x7f090047
int drawable abc_text_select_handle_left_mtrl_dark 0x7f090048
int drawable abc_text_select_handle_left_mtrl_light 0x7f090049
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f09004a
int drawable abc_text_select_handle_middle_mtrl_light 0x7f09004b
int drawable abc_text_select_handle_right_mtrl_dark 0x7f09004c
int drawable abc_text_select_handle_right_mtrl_light 0x7f09004d
int drawable abc_textfield_activated_mtrl_alpha 0x7f09004e
int drawable abc_textfield_default_mtrl_alpha 0x7f09004f
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f090050
int drawable abc_textfield_search_default_mtrl_alpha 0x7f090051
int drawable abc_textfield_search_material 0x7f090052
int drawable abc_vector_test 0x7f090053
int drawable notification_action_background 0x7f090054
int drawable notification_bg 0x7f090055
int drawable notification_bg_low 0x7f090056
int drawable notification_bg_low_normal 0x7f090057
int drawable notification_bg_low_pressed 0x7f090058
int drawable notification_bg_normal 0x7f090059
int drawable notification_bg_normal_pressed 0x7f09005a
int drawable notification_icon_background 0x7f09005b
int drawable notification_template_icon_bg 0x7f09005c
int drawable notification_template_icon_low_bg 0x7f09005d
int drawable notification_tile_bg 0x7f09005e
int drawable notify_panel_notification_icon_bg 0x7f09005f
int drawable tooltip_frame_dark 0x7f090060
int drawable tooltip_frame_light 0x7f090061
int id action_bar 0x7f0c0001
int id action_bar_activity_content 0x7f0c0002
int id action_bar_container 0x7f0c0003
int id action_bar_root 0x7f0c0004
int id action_bar_spinner 0x7f0c0005
int id action_bar_subtitle 0x7f0c0006
int id action_bar_title 0x7f0c0007
int id action_container 0x7f0c0008
int id action_context_bar 0x7f0c0009
int id action_divider 0x7f0c000a
int id action_image 0x7f0c000b
int id action_menu_divider 0x7f0c000c
int id action_menu_presenter 0x7f0c000d
int id action_mode_bar 0x7f0c000e
int id action_mode_bar_stub 0x7f0c000f
int id action_mode_close_button 0x7f0c0010
int id action_text 0x7f0c0011
int id actions 0x7f0c0012
int id activity_chooser_view_content 0x7f0c0013
int id add 0x7f0c0014
int id alertTitle 0x7f0c0015
int id async 0x7f0c0016
int id blocking 0x7f0c0017
int id bottom 0x7f0c0018
int id buttonPanel 0x7f0c0019
int id checkbox 0x7f0c001a
int id chronometer 0x7f0c001b
int id contentPanel 0x7f0c001c
int id custom 0x7f0c001d
int id customPanel 0x7f0c001e
int id decor_content_parent 0x7f0c001f
int id default_activity_button 0x7f0c0020
int id edit_query 0x7f0c0021
int id end 0x7f0c0022
int id expand_activities_button 0x7f0c0023
int id expanded_menu 0x7f0c0024
int id forever 0x7f0c0025
int id home 0x7f0c0026
int id icon 0x7f0c0027
int id icon_group 0x7f0c0028
int id image 0x7f0c0029
int id info 0x7f0c002a
int id italic 0x7f0c002b
int id left 0x7f0c002c
int id line1 0x7f0c002d
int id line3 0x7f0c002e
int id listMode 0x7f0c002f
int id list_item 0x7f0c0030
int id message 0x7f0c0031
int id multiply 0x7f0c0032
int id none 0x7f0c0033
int id normal 0x7f0c0034
int id notification_background 0x7f0c0035
int id notification_main_column 0x7f0c0036
int id notification_main_column_container 0x7f0c0037
int id parentPanel 0x7f0c0038
int id progress_circular 0x7f0c0039
int id progress_horizontal 0x7f0c003a
int id radio 0x7f0c003b
int id right 0x7f0c003c
int id right_icon 0x7f0c003d
int id right_side 0x7f0c003e
int id screen 0x7f0c003f
int id scrollIndicatorDown 0x7f0c0040
int id scrollIndicatorUp 0x7f0c0041
int id scrollView 0x7f0c0042
int id search_badge 0x7f0c0043
int id search_bar 0x7f0c0044
int id search_button 0x7f0c0045
int id search_close_btn 0x7f0c0046
int id search_edit_frame 0x7f0c0047
int id search_go_btn 0x7f0c0048
int id search_mag_icon 0x7f0c0049
int id search_plate 0x7f0c004a
int id search_src_text 0x7f0c004b
int id search_voice_btn 0x7f0c004c
int id select_dialog_listview 0x7f0c004d
int id shortcut 0x7f0c004e
int id spacer 0x7f0c004f
int id split_action_bar 0x7f0c0050
int id src_atop 0x7f0c0051
int id src_in 0x7f0c0052
int id src_over 0x7f0c0053
int id start 0x7f0c0054
int id submenuarrow 0x7f0c0055
int id submit_area 0x7f0c0056
int id tabMode 0x7f0c0057
int id tag_transition_group 0x7f0c0058
int id text 0x7f0c0059
int id text2 0x7f0c005a
int id textSpacerNoButtons 0x7f0c005b
int id textSpacerNoTitle 0x7f0c005c
int id time 0x7f0c005d
int id title 0x7f0c005e
int id titleDividerNoCustom 0x7f0c005f
int id title_template 0x7f0c0060
int id top 0x7f0c0061
int id topPanel 0x7f0c0062
int id uniform 0x7f0c0063
int id up 0x7f0c0064
int id wrap_content 0x7f0c0065
int integer abc_config_activityDefaultDur 0x7f0d0001
int integer abc_config_activityShortDur 0x7f0d0002
int integer cancel_button_image_alpha 0x7f0d0003
int integer config_tooltipAnimTime 0x7f0d0004
int integer status_bar_notification_info_maxnum 0x7f0d0005
int layout abc_action_bar_title_item 0x7f0f0001
int layout abc_action_bar_up_container 0x7f0f0002
int layout abc_action_menu_item_layout 0x7f0f0003
int layout abc_action_menu_layout 0x7f0f0004
int layout abc_action_mode_bar 0x7f0f0005
int layout abc_action_mode_close_item_material 0x7f0f0006
int layout abc_activity_chooser_view 0x7f0f0007
int layout abc_activity_chooser_view_list_item 0x7f0f0008
int layout abc_alert_dialog_button_bar_material 0x7f0f0009
int layout abc_alert_dialog_material 0x7f0f000a
int layout abc_alert_dialog_title_material 0x7f0f000b
int layout abc_dialog_title_material 0x7f0f000c
int layout abc_expanded_menu_layout 0x7f0f000d
int layout abc_list_menu_item_checkbox 0x7f0f000e
int layout abc_list_menu_item_icon 0x7f0f000f
int layout abc_list_menu_item_layout 0x7f0f0010
int layout abc_list_menu_item_radio 0x7f0f0011
int layout abc_popup_menu_header_item_layout 0x7f0f0012
int layout abc_popup_menu_item_layout 0x7f0f0013
int layout abc_screen_content_include 0x7f0f0014
int layout abc_screen_simple 0x7f0f0015
int layout abc_screen_simple_overlay_action_mode 0x7f0f0016
int layout abc_screen_toolbar 0x7f0f0017
int layout abc_search_dropdown_item_icons_2line 0x7f0f0018
int layout abc_search_view 0x7f0f0019
int layout abc_select_dialog_material 0x7f0f001a
int layout abc_tooltip 0x7f0f001b
int layout notification_action 0x7f0f001c
int layout notification_action_tombstone 0x7f0f001d
int layout notification_template_custom_big 0x7f0f001e
int layout notification_template_icon_group 0x7f0f001f
int layout notification_template_part_chronometer 0x7f0f0020
int layout notification_template_part_time 0x7f0f0021
int layout select_dialog_item_material 0x7f0f0022
int layout select_dialog_multichoice_material 0x7f0f0023
int layout select_dialog_singlechoice_material 0x7f0f0024
int layout support_simple_spinner_dropdown_item 0x7f0f0025
int string abc_action_bar_home_description 0x7f150001
int string abc_action_bar_up_description 0x7f150002
int string abc_action_menu_overflow_description 0x7f150003
int string abc_action_mode_done 0x7f150004
int string abc_activity_chooser_view_see_all 0x7f150005
int string abc_activitychooserview_choose_application 0x7f150006
int string abc_capital_off 0x7f150007
int string abc_capital_on 0x7f150008
int string abc_font_family_body_1_material 0x7f150009
int string abc_font_family_body_2_material 0x7f15000a
int string abc_font_family_button_material 0x7f15000b
int string abc_font_family_caption_material 0x7f15000c
int string abc_font_family_display_1_material 0x7f15000d
int string abc_font_family_display_2_material 0x7f15000e
int string abc_font_family_display_3_material 0x7f15000f
int string abc_font_family_display_4_material 0x7f150010
int string abc_font_family_headline_material 0x7f150011
int string abc_font_family_menu_material 0x7f150012
int string abc_font_family_subhead_material 0x7f150013
int string abc_font_family_title_material 0x7f150014
int string abc_search_hint 0x7f150015
int string abc_searchview_description_clear 0x7f150016
int string abc_searchview_description_query 0x7f150017
int string abc_searchview_description_search 0x7f150018
int string abc_searchview_description_submit 0x7f150019
int string abc_searchview_description_voice 0x7f15001a
int string abc_shareactionprovider_share_with 0x7f15001b
int string abc_shareactionprovider_share_with_application 0x7f15001c
int string abc_toolbar_collapse_description 0x7f15001d
int string app_name 0x7f15001e
int string search_menu_title 0x7f15001f
int string status_bar_notification_info_overflow 0x7f150020
int style AlertDialog_AppCompat 0x7f160001
int style AlertDialog_AppCompat_Light 0x7f160002
int style Animation_AppCompat_Dialog 0x7f160003
int style Animation_AppCompat_DropDownUp 0x7f160004
int style Animation_AppCompat_Tooltip 0x7f160005
int style Base_AlertDialog_AppCompat 0x7f160006
int style Base_AlertDialog_AppCompat_Light 0x7f160007
int style Base_Animation_AppCompat_Dialog 0x7f160008
int style Base_Animation_AppCompat_DropDownUp 0x7f160009
int style Base_Animation_AppCompat_Tooltip 0x7f16000a
int style Base_DialogWindowTitleBackground_AppCompat 0x7f16000b
int style Base_DialogWindowTitle_AppCompat 0x7f16000c
int style Base_TextAppearance_AppCompat 0x7f16000d
int style Base_TextAppearance_AppCompat_Body1 0x7f16000e
int style Base_TextAppearance_AppCompat_Body2 0x7f16000f
int style Base_TextAppearance_AppCompat_Button 0x7f160010
int style Base_TextAppearance_AppCompat_Caption 0x7f160011
int style Base_TextAppearance_AppCompat_Display1 0x7f160012
int style Base_TextAppearance_AppCompat_Display2 0x7f160013
int style Base_TextAppearance_AppCompat_Display3 0x7f160014
int style Base_TextAppearance_AppCompat_Display4 0x7f160015
int style Base_TextAppearance_AppCompat_Headline 0x7f160016
int style Base_TextAppearance_AppCompat_Inverse 0x7f160017
int style Base_TextAppearance_AppCompat_Large 0x7f160018
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f160019
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f16001a
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f16001b
int style Base_TextAppearance_AppCompat_Medium 0x7f16001c
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f16001d
int style Base_TextAppearance_AppCompat_Menu 0x7f16001e
int style Base_TextAppearance_AppCompat_SearchResult 0x7f16001f
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f160020
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f160021
int style Base_TextAppearance_AppCompat_Small 0x7f160022
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f160023
int style Base_TextAppearance_AppCompat_Subhead 0x7f160024
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f160025
int style Base_TextAppearance_AppCompat_Title 0x7f160026
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f160027
int style Base_TextAppearance_AppCompat_Tooltip 0x7f160028
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f160029
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f16002a
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f16002b
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f16002c
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f16002d
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f16002e
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f16002f
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f160030
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f160031
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f160032
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f160033
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f160034
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f160035
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f160036
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f160037
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f160038
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f160039
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f16003a
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f16003b
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f16003c
int style Base_ThemeOverlay_AppCompat 0x7f16003d
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f16003e
int style Base_ThemeOverlay_AppCompat_Dark 0x7f16003f
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f160040
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f160041
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f160042
int style Base_ThemeOverlay_AppCompat_Light 0x7f160043
int style Base_Theme_AppCompat 0x7f160044
int style Base_Theme_AppCompat_CompactMenu 0x7f160045
int style Base_Theme_AppCompat_Dialog 0x7f160046
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f160047
int style Base_Theme_AppCompat_Dialog_Alert 0x7f160048
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f160049
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f16004a
int style Base_Theme_AppCompat_Light 0x7f16004b
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f16004c
int style Base_Theme_AppCompat_Light_Dialog 0x7f16004d
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f16004e
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f16004f
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f160050
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f160051
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f160052
int style Base_V21_Theme_AppCompat 0x7f160053
int style Base_V21_Theme_AppCompat_Dialog 0x7f160054
int style Base_V21_Theme_AppCompat_Light 0x7f160055
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f160056
int style Base_V22_Theme_AppCompat 0x7f160057
int style Base_V22_Theme_AppCompat_Light 0x7f160058
int style Base_V23_Theme_AppCompat 0x7f160059
int style Base_V23_Theme_AppCompat_Light 0x7f16005a
int style Base_V26_Theme_AppCompat 0x7f16005b
int style Base_V26_Theme_AppCompat_Light 0x7f16005c
int style Base_V26_Widget_AppCompat_Toolbar 0x7f16005d
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f16005e
int style Base_V7_Theme_AppCompat 0x7f16005f
int style Base_V7_Theme_AppCompat_Dialog 0x7f160060
int style Base_V7_Theme_AppCompat_Light 0x7f160061
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f160062
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f160063
int style Base_V7_Widget_AppCompat_EditText 0x7f160064
int style Base_V7_Widget_AppCompat_Toolbar 0x7f160065
int style Base_Widget_AppCompat_ActionBar 0x7f160066
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f160067
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f160068
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f160069
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f16006a
int style Base_Widget_AppCompat_ActionButton 0x7f16006b
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f16006c
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f16006d
int style Base_Widget_AppCompat_ActionMode 0x7f16006e
int style Base_Widget_AppCompat_ActivityChooserView 0x7f16006f
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f160070
int style Base_Widget_AppCompat_Button 0x7f160071
int style Base_Widget_AppCompat_ButtonBar 0x7f160072
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f160073
int style Base_Widget_AppCompat_Button_Borderless 0x7f160074
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f160075
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f160076
int style Base_Widget_AppCompat_Button_Colored 0x7f160077
int style Base_Widget_AppCompat_Button_Small 0x7f160078
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f160079
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f16007a
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f16007b
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f16007c
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f16007d
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f16007e
int style Base_Widget_AppCompat_EditText 0x7f16007f
int style Base_Widget_AppCompat_ImageButton 0x7f160080
int style Base_Widget_AppCompat_Light_ActionBar 0x7f160081
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f160082
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f160083
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f160084
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f160085
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f160086
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f160087
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f160088
int style Base_Widget_AppCompat_ListMenuView 0x7f160089
int style Base_Widget_AppCompat_ListPopupWindow 0x7f16008a
int style Base_Widget_AppCompat_ListView 0x7f16008b
int style Base_Widget_AppCompat_ListView_DropDown 0x7f16008c
int style Base_Widget_AppCompat_ListView_Menu 0x7f16008d
int style Base_Widget_AppCompat_PopupMenu 0x7f16008e
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f16008f
int style Base_Widget_AppCompat_PopupWindow 0x7f160090
int style Base_Widget_AppCompat_ProgressBar 0x7f160091
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f160092
int style Base_Widget_AppCompat_RatingBar 0x7f160093
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f160094
int style Base_Widget_AppCompat_RatingBar_Small 0x7f160095
int style Base_Widget_AppCompat_SearchView 0x7f160096
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f160097
int style Base_Widget_AppCompat_SeekBar 0x7f160098
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f160099
int style Base_Widget_AppCompat_Spinner 0x7f16009a
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f16009b
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f16009c
int style Base_Widget_AppCompat_Toolbar 0x7f16009d
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f16009e
int style Platform_AppCompat 0x7f16009f
int style Platform_AppCompat_Light 0x7f1600a0
int style Platform_ThemeOverlay_AppCompat 0x7f1600a1
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f1600a2
int style Platform_ThemeOverlay_AppCompat_Light 0x7f1600a3
int style Platform_V21_AppCompat 0x7f1600a4
int style Platform_V21_AppCompat_Light 0x7f1600a5
int style Platform_V25_AppCompat 0x7f1600a6
int style Platform_V25_AppCompat_Light 0x7f1600a7
int style Platform_Widget_AppCompat_Spinner 0x7f1600a8
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f1600a9
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f1600aa
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f1600ab
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f1600ac
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f1600ad
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f1600ae
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f1600af
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f1600b0
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f1600b1
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f1600b2
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f1600b3
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f1600b4
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f1600b5
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f1600b6
int style TextAppearance_AppCompat 0x7f1600b7
int style TextAppearance_AppCompat_Body1 0x7f1600b8
int style TextAppearance_AppCompat_Body2 0x7f1600b9
int style TextAppearance_AppCompat_Button 0x7f1600ba
int style TextAppearance_AppCompat_Caption 0x7f1600bb
int style TextAppearance_AppCompat_Display1 0x7f1600bc
int style TextAppearance_AppCompat_Display2 0x7f1600bd
int style TextAppearance_AppCompat_Display3 0x7f1600be
int style TextAppearance_AppCompat_Display4 0x7f1600bf
int style TextAppearance_AppCompat_Headline 0x7f1600c0
int style TextAppearance_AppCompat_Inverse 0x7f1600c1
int style TextAppearance_AppCompat_Large 0x7f1600c2
int style TextAppearance_AppCompat_Large_Inverse 0x7f1600c3
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f1600c4
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f1600c5
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f1600c6
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f1600c7
int style TextAppearance_AppCompat_Medium 0x7f1600c8
int style TextAppearance_AppCompat_Medium_Inverse 0x7f1600c9
int style TextAppearance_AppCompat_Menu 0x7f1600ca
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f1600cb
int style TextAppearance_AppCompat_SearchResult_Title 0x7f1600cc
int style TextAppearance_AppCompat_Small 0x7f1600cd
int style TextAppearance_AppCompat_Small_Inverse 0x7f1600ce
int style TextAppearance_AppCompat_Subhead 0x7f1600cf
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f1600d0
int style TextAppearance_AppCompat_Title 0x7f1600d1
int style TextAppearance_AppCompat_Title_Inverse 0x7f1600d2
int style TextAppearance_AppCompat_Tooltip 0x7f1600d3
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f1600d4
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f1600d5
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f1600d6
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f1600d7
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f1600d8
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f1600d9
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f1600da
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f1600db
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f1600dc
int style TextAppearance_AppCompat_Widget_Button 0x7f1600dd
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f1600de
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f1600df
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f1600e0
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f1600e1
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f1600e2
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f1600e3
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f1600e4
int style TextAppearance_AppCompat_Widget_Switch 0x7f1600e5
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f1600e6
int style TextAppearance_Compat_Notification 0x7f1600e7
int style TextAppearance_Compat_Notification_Info 0x7f1600e8
int style TextAppearance_Compat_Notification_Line2 0x7f1600e9
int style TextAppearance_Compat_Notification_Time 0x7f1600ea
int style TextAppearance_Compat_Notification_Title 0x7f1600eb
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f1600ec
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f1600ed
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f1600ee
int style ThemeOverlay_AppCompat 0x7f1600ef
int style ThemeOverlay_AppCompat_ActionBar 0x7f1600f0
int style ThemeOverlay_AppCompat_Dark 0x7f1600f1
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f1600f2
int style ThemeOverlay_AppCompat_Dialog 0x7f1600f3
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f1600f4
int style ThemeOverlay_AppCompat_Light 0x7f1600f5
int style Theme_AppCompat 0x7f1600f6
int style Theme_AppCompat_CompactMenu 0x7f1600f7
int style Theme_AppCompat_DayNight 0x7f1600f8
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f1600f9
int style Theme_AppCompat_DayNight_Dialog 0x7f1600fa
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f1600fb
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f1600fc
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f1600fd
int style Theme_AppCompat_DayNight_NoActionBar 0x7f1600fe
int style Theme_AppCompat_Dialog 0x7f1600ff
int style Theme_AppCompat_DialogWhenLarge 0x7f160100
int style Theme_AppCompat_Dialog_Alert 0x7f160101
int style Theme_AppCompat_Dialog_MinWidth 0x7f160102
int style Theme_AppCompat_Light 0x7f160103
int style Theme_AppCompat_Light_DarkActionBar 0x7f160104
int style Theme_AppCompat_Light_Dialog 0x7f160105
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f160106
int style Theme_AppCompat_Light_Dialog_Alert 0x7f160107
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f160108
int style Theme_AppCompat_Light_NoActionBar 0x7f160109
int style Theme_AppCompat_NoActionBar 0x7f16010a
int style Widget_AppCompat_ActionBar 0x7f16010b
int style Widget_AppCompat_ActionBar_Solid 0x7f16010c
int style Widget_AppCompat_ActionBar_TabBar 0x7f16010d
int style Widget_AppCompat_ActionBar_TabText 0x7f16010e
int style Widget_AppCompat_ActionBar_TabView 0x7f16010f
int style Widget_AppCompat_ActionButton 0x7f160110
int style Widget_AppCompat_ActionButton_CloseMode 0x7f160111
int style Widget_AppCompat_ActionButton_Overflow 0x7f160112
int style Widget_AppCompat_ActionMode 0x7f160113
int style Widget_AppCompat_ActivityChooserView 0x7f160114
int style Widget_AppCompat_AutoCompleteTextView 0x7f160115
int style Widget_AppCompat_Button 0x7f160116
int style Widget_AppCompat_ButtonBar 0x7f160117
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f160118
int style Widget_AppCompat_Button_Borderless 0x7f160119
int style Widget_AppCompat_Button_Borderless_Colored 0x7f16011a
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f16011b
int style Widget_AppCompat_Button_Colored 0x7f16011c
int style Widget_AppCompat_Button_Small 0x7f16011d
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f16011e
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f16011f
int style Widget_AppCompat_CompoundButton_Switch 0x7f160120
int style Widget_AppCompat_DrawerArrowToggle 0x7f160121
int style Widget_AppCompat_DropDownItem_Spinner 0x7f160122
int style Widget_AppCompat_EditText 0x7f160123
int style Widget_AppCompat_ImageButton 0x7f160124
int style Widget_AppCompat_Light_ActionBar 0x7f160125
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f160126
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f160127
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f160128
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f160129
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f16012a
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f16012b
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f16012c
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f16012d
int style Widget_AppCompat_Light_ActionButton 0x7f16012e
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f16012f
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f160130
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f160131
int style Widget_AppCompat_Light_ActivityChooserView 0x7f160132
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f160133
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f160134
int style Widget_AppCompat_Light_ListPopupWindow 0x7f160135
int style Widget_AppCompat_Light_ListView_DropDown 0x7f160136
int style Widget_AppCompat_Light_PopupMenu 0x7f160137
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f160138
int style Widget_AppCompat_Light_SearchView 0x7f160139
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f16013a
int style Widget_AppCompat_ListMenuView 0x7f16013b
int style Widget_AppCompat_ListPopupWindow 0x7f16013c
int style Widget_AppCompat_ListView 0x7f16013d
int style Widget_AppCompat_ListView_DropDown 0x7f16013e
int style Widget_AppCompat_ListView_Menu 0x7f16013f
int style Widget_AppCompat_PopupMenu 0x7f160140
int style Widget_AppCompat_PopupMenu_Overflow 0x7f160141
int style Widget_AppCompat_PopupWindow 0x7f160142
int style Widget_AppCompat_ProgressBar 0x7f160143
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f160144
int style Widget_AppCompat_RatingBar 0x7f160145
int style Widget_AppCompat_RatingBar_Indicator 0x7f160146
int style Widget_AppCompat_RatingBar_Small 0x7f160147
int style Widget_AppCompat_SearchView 0x7f160148
int style Widget_AppCompat_SearchView_ActionBar 0x7f160149
int style Widget_AppCompat_SeekBar 0x7f16014a
int style Widget_AppCompat_SeekBar_Discrete 0x7f16014b
int style Widget_AppCompat_Spinner 0x7f16014c
int style Widget_AppCompat_Spinner_DropDown 0x7f16014d
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f16014e
int style Widget_AppCompat_Spinner_Underlined 0x7f16014f
int style Widget_AppCompat_TextView_SpinnerItem 0x7f160150
int style Widget_AppCompat_Toolbar 0x7f160151
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f160152
int style Widget_Compat_NotificationActionContainer 0x7f160153
int style Widget_Compat_NotificationActionText 0x7f160154
int style Widget_Support_CoordinatorLayout 0x7f160155
int[] styleable ActionBar { 0x7f040032, 0x7f040033, 0x7f040034, 0x7f040058, 0x7f040059, 0x7f04005a, 0x7f04005b, 0x7f04005c, 0x7f04005d, 0x7f040060, 0x7f040064, 0x7f040065, 0x7f040070, 0x7f04007e, 0x7f04007f, 0x7f040080, 0x7f040081, 0x7f040082, 0x7f040087, 0x7f04008a, 0x7f04009e, 0x7f0400a5, 0x7f0400b0, 0x7f0400b3, 0x7f0400b4, 0x7f0400cf, 0x7f0400d2, 0x7f0400ed, 0x7f0400f6 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMode { 0x7f040032, 0x7f040033, 0x7f040048, 0x7f04007e, 0x7f0400d2, 0x7f0400f6 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f040071, 0x7f040088 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable AlertDialog { 0x010100f2, 0x7f04003f, 0x7f040040, 0x7f040095, 0x7f040096, 0x7f0400a2, 0x7f0400c4, 0x7f0400c5 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AppCompatImageView { 0x01010119, 0x7f0400ca, 0x7f0400eb, 0x7f0400ec }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f0400e8, 0x7f0400e9, 0x7f0400ea }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x0101016e, 0x01010393, 0x0101016f, 0x01010170, 0x01010392, 0x0101016d, 0x01010034 }
int styleable AppCompatTextHelper_android_drawableBottom 0
int styleable AppCompatTextHelper_android_drawableEnd 1
int styleable AppCompatTextHelper_android_drawableLeft 2
int styleable AppCompatTextHelper_android_drawableRight 3
int styleable AppCompatTextHelper_android_drawableStart 4
int styleable AppCompatTextHelper_android_drawableTop 5
int styleable AppCompatTextHelper_android_textAppearance 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f04002d, 0x7f04002e, 0x7f04002f, 0x7f040030, 0x7f040031, 0x7f040073, 0x7f0400d8 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_fontFamily 6
int styleable AppCompatTextView_textAllCaps 7
int[] styleable AppCompatTheme { 0x7f040001, 0x7f040002, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000d, 0x7f04000f, 0x7f040010, 0x7f040011, 0x7f040012, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f040018, 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f04001f, 0x7f040022, 0x7f040023, 0x7f040024, 0x7f040025, 0x7f040026, 0x010100ae, 0x01010057, 0x7f04002c, 0x7f040038, 0x7f040039, 0x7f04003a, 0x7f04003b, 0x7f04003c, 0x7f04003d, 0x7f040041, 0x7f040042, 0x7f040045, 0x7f040046, 0x7f04004c, 0x7f04004d, 0x7f04004e, 0x7f04004f, 0x7f040050, 0x7f040051, 0x7f040052, 0x7f040053, 0x7f040054, 0x7f040055, 0x7f04005e, 0x7f040062, 0x7f040063, 0x7f040066, 0x7f040068, 0x7f04006b, 0x7f04006c, 0x7f04006d, 0x7f04006e, 0x7f04006f, 0x7f040080, 0x7f040086, 0x7f040093, 0x7f040094, 0x7f040097, 0x7f040098, 0x7f040099, 0x7f04009a, 0x7f04009b, 0x7f04009c, 0x7f04009d, 0x7f0400ac, 0x7f0400ad, 0x7f0400ae, 0x7f0400af, 0x7f0400b1, 0x7f0400b7, 0x7f0400b8, 0x7f0400b9, 0x7f0400ba, 0x7f0400bd, 0x7f0400be, 0x7f0400bf, 0x7f0400c0, 0x7f0400c7, 0x7f0400c8, 0x7f0400d6, 0x7f0400d9, 0x7f0400da, 0x7f0400db, 0x7f0400dc, 0x7f0400dd, 0x7f0400de, 0x7f0400df, 0x7f0400e0, 0x7f0400e1, 0x7f0400e2, 0x7f0400f7, 0x7f0400f8, 0x7f0400f9, 0x7f0400fa, 0x7f0400ff, 0x7f040101, 0x7f040102, 0x7f040103, 0x7f040104, 0x7f040105, 0x7f040106, 0x7f040107, 0x7f040108, 0x7f040109, 0x7f04010a }
int styleable AppCompatTheme_actionBarDivider 0
int styleable AppCompatTheme_actionBarItemBackground 1
int styleable AppCompatTheme_actionBarPopupTheme 2
int styleable AppCompatTheme_actionBarSize 3
int styleable AppCompatTheme_actionBarSplitStyle 4
int styleable AppCompatTheme_actionBarStyle 5
int styleable AppCompatTheme_actionBarTabBarStyle 6
int styleable AppCompatTheme_actionBarTabStyle 7
int styleable AppCompatTheme_actionBarTabTextStyle 8
int styleable AppCompatTheme_actionBarTheme 9
int styleable AppCompatTheme_actionBarWidgetTheme 10
int styleable AppCompatTheme_actionButtonStyle 11
int styleable AppCompatTheme_actionDropDownStyle 12
int styleable AppCompatTheme_actionMenuTextAppearance 13
int styleable AppCompatTheme_actionMenuTextColor 14
int styleable AppCompatTheme_actionModeBackground 15
int styleable AppCompatTheme_actionModeCloseButtonStyle 16
int styleable AppCompatTheme_actionModeCloseDrawable 17
int styleable AppCompatTheme_actionModeCopyDrawable 18
int styleable AppCompatTheme_actionModeCutDrawable 19
int styleable AppCompatTheme_actionModeFindDrawable 20
int styleable AppCompatTheme_actionModePasteDrawable 21
int styleable AppCompatTheme_actionModePopupWindowStyle 22
int styleable AppCompatTheme_actionModeSelectAllDrawable 23
int styleable AppCompatTheme_actionModeShareDrawable 24
int styleable AppCompatTheme_actionModeSplitBackground 25
int styleable AppCompatTheme_actionModeStyle 26
int styleable AppCompatTheme_actionModeWebSearchDrawable 27
int styleable AppCompatTheme_actionOverflowButtonStyle 28
int styleable AppCompatTheme_actionOverflowMenuStyle 29
int styleable AppCompatTheme_activityChooserViewStyle 30
int styleable AppCompatTheme_alertDialogButtonGroupStyle 31
int styleable AppCompatTheme_alertDialogCenterButtons 32
int styleable AppCompatTheme_alertDialogStyle 33
int styleable AppCompatTheme_alertDialogTheme 34
int styleable AppCompatTheme_android_windowAnimationStyle 35
int styleable AppCompatTheme_android_windowIsFloating 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogPreferredPadding 59
int styleable AppCompatTheme_dialogTheme 60
int styleable AppCompatTheme_dividerHorizontal 61
int styleable AppCompatTheme_dividerVertical 62
int styleable AppCompatTheme_dropDownListViewStyle 63
int styleable AppCompatTheme_dropdownListPreferredItemHeight 64
int styleable AppCompatTheme_editTextBackground 65
int styleable AppCompatTheme_editTextColor 66
int styleable AppCompatTheme_editTextStyle 67
int styleable AppCompatTheme_homeAsUpIndicator 68
int styleable AppCompatTheme_imageButtonStyle 69
int styleable AppCompatTheme_listChoiceBackgroundIndicator 70
int styleable AppCompatTheme_listDividerAlertDialog 71
int styleable AppCompatTheme_listMenuViewStyle 72
int styleable AppCompatTheme_listPopupWindowStyle 73
int styleable AppCompatTheme_listPreferredItemHeight 74
int styleable AppCompatTheme_listPreferredItemHeightLarge 75
int styleable AppCompatTheme_listPreferredItemHeightSmall 76
int styleable AppCompatTheme_listPreferredItemPaddingLeft 77
int styleable AppCompatTheme_listPreferredItemPaddingRight 78
int styleable AppCompatTheme_panelBackground 79
int styleable AppCompatTheme_panelMenuListTheme 80
int styleable AppCompatTheme_panelMenuListWidth 81
int styleable AppCompatTheme_popupMenuStyle 82
int styleable AppCompatTheme_popupWindowStyle 83
int styleable AppCompatTheme_radioButtonStyle 84
int styleable AppCompatTheme_ratingBarStyle 85
int styleable AppCompatTheme_ratingBarStyleIndicator 86
int styleable AppCompatTheme_ratingBarStyleSmall 87
int styleable AppCompatTheme_searchViewStyle 88
int styleable AppCompatTheme_seekBarStyle 89
int styleable AppCompatTheme_selectableItemBackground 90
int styleable AppCompatTheme_selectableItemBackgroundBorderless 91
int styleable AppCompatTheme_spinnerDropDownItemStyle 92
int styleable AppCompatTheme_spinnerStyle 93
int styleable AppCompatTheme_switchStyle 94
int styleable AppCompatTheme_textAppearanceLargePopupMenu 95
int styleable AppCompatTheme_textAppearanceListItem 96
int styleable AppCompatTheme_textAppearanceListItemSecondary 97
int styleable AppCompatTheme_textAppearanceListItemSmall 98
int styleable AppCompatTheme_textAppearancePopupMenuHeader 99
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 100
int styleable AppCompatTheme_textAppearanceSearchResultTitle 101
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 102
int styleable AppCompatTheme_textColorAlertDialogListItem 103
int styleable AppCompatTheme_textColorSearchUrl 104
int styleable AppCompatTheme_toolbarNavigationButtonStyle 105
int styleable AppCompatTheme_toolbarStyle 106
int styleable AppCompatTheme_tooltipForegroundColor 107
int styleable AppCompatTheme_tooltipFrameBackground 108
int styleable AppCompatTheme_viewInflaterClass 109
int styleable AppCompatTheme_windowActionBar 110
int styleable AppCompatTheme_windowActionBarOverlay 111
int styleable AppCompatTheme_windowActionModeOverlay 112
int styleable AppCompatTheme_windowFixedHeightMajor 113
int styleable AppCompatTheme_windowFixedHeightMinor 114
int styleable AppCompatTheme_windowFixedWidthMajor 115
int styleable AppCompatTheme_windowFixedWidthMinor 116
int styleable AppCompatTheme_windowMinWidthMajor 117
int styleable AppCompatTheme_windowMinWidthMinor 118
int styleable AppCompatTheme_windowNoTitle 119
int[] styleable ButtonBarLayout { 0x7f040027 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable ColorStateListItem { 0x7f040028, 0x0101031f, 0x010101a5 }
int styleable ColorStateListItem_alpha 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_color 2
int[] styleable CompoundButton { 0x01010107, 0x7f040043, 0x7f040044 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonTint 1
int styleable CompoundButton_buttonTintMode 2
int[] styleable CoordinatorLayout { 0x7f04008b, 0x7f0400cc }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f04008d, 0x7f04008e, 0x7f04008f, 0x7f040090, 0x7f040091, 0x7f040092 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable DrawerArrowToggle { 0x7f04002a, 0x7f04002b, 0x7f040037, 0x7f04004b, 0x7f040069, 0x7f04007c, 0x7f0400c6, 0x7f0400e4 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FontFamily { 0x7f040074, 0x7f040075, 0x7f040076, 0x7f040077, 0x7f040078, 0x7f040079 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int[] styleable FontFamilyFont { 0x01010532, 0x0101053f, 0x01010533, 0x7f040072, 0x7f04007a, 0x7f04007b }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontStyle 1
int styleable FontFamilyFont_android_fontWeight 2
int styleable FontFamilyFont_font 3
int styleable FontFamilyFont_fontStyle 4
int styleable FontFamilyFont_fontWeight 5
int[] styleable LinearLayoutCompat { 0x01010126, 0x01010127, 0x010100af, 0x010100c4, 0x01010128, 0x7f040065, 0x7f040067, 0x7f0400a1, 0x7f0400c2 }
int styleable LinearLayoutCompat_android_baselineAligned 0
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
int styleable LinearLayoutCompat_android_gravity 2
int styleable LinearLayoutCompat_android_orientation 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f5, 0x01010181, 0x010100f4 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_height 1
int styleable LinearLayoutCompat_Layout_android_layout_weight 2
int styleable LinearLayoutCompat_Layout_android_layout_width 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MenuGroup { 0x010101e0, 0x0101000e, 0x010100d0, 0x010101de, 0x010101df, 0x01010194 }
int styleable MenuGroup_android_checkableBehavior 0
int styleable MenuGroup_android_enabled 1
int styleable MenuGroup_android_id 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_visible 5
int[] styleable MenuItem { 0x7f04000e, 0x7f040020, 0x7f040021, 0x7f040029, 0x010101e3, 0x010101e5, 0x01010106, 0x0101000e, 0x01010002, 0x010100d0, 0x010101de, 0x010101e4, 0x0101026f, 0x010101df, 0x010101e1, 0x010101e2, 0x01010194, 0x7f040057, 0x7f040083, 0x7f040084, 0x7f0400a6, 0x7f0400c1, 0x7f0400fb }
int styleable MenuItem_actionLayout 0
int styleable MenuItem_actionProviderClass 1
int styleable MenuItem_actionViewClass 2
int styleable MenuItem_alphabeticModifiers 3
int styleable MenuItem_android_alphabeticShortcut 4
int styleable MenuItem_android_checkable 5
int styleable MenuItem_android_checked 6
int styleable MenuItem_android_enabled 7
int styleable MenuItem_android_icon 8
int styleable MenuItem_android_id 9
int styleable MenuItem_android_menuCategory 10
int styleable MenuItem_android_numericShortcut 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_android_orderInCategory 13
int styleable MenuItem_android_title 14
int styleable MenuItem_android_titleCondensed 15
int styleable MenuItem_android_visible 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x0101012f, 0x0101012d, 0x01010130, 0x01010131, 0x0101012c, 0x0101012e, 0x010100ae, 0x7f0400b2, 0x7f0400cd }
int styleable MenuView_android_headerBackground 0
int styleable MenuView_android_horizontalDivider 1
int styleable MenuView_android_itemBackground 2
int styleable MenuView_android_itemIconDisabledAlpha 3
int styleable MenuView_android_itemTextAppearance 4
int styleable MenuView_android_verticalDivider 5
int styleable MenuView_android_windowAnimationStyle 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable PopupWindow { 0x010102c9, 0x01010176, 0x7f0400a7 }
int styleable PopupWindow_android_popupAnimationStyle 0
int styleable PopupWindow_android_popupBackground 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f0400cb }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable RecycleListView { 0x7f0400a8, 0x7f0400ab }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable SearchView { 0x010100da, 0x01010264, 0x01010220, 0x0101011f, 0x7f040047, 0x7f040056, 0x7f040061, 0x7f04007d, 0x7f040085, 0x7f04008c, 0x7f0400b5, 0x7f0400b6, 0x7f0400bb, 0x7f0400bc, 0x7f0400ce, 0x7f0400d3, 0x7f040100 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_imeOptions 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_maxWidth 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable Spinner { 0x01010262, 0x010100b2, 0x01010176, 0x0101017b, 0x7f0400b0 }
int styleable Spinner_android_dropDownWidth 0
int styleable Spinner_android_entries 1
int styleable Spinner_android_popupBackground 2
int styleable Spinner_android_prompt 3
int styleable Spinner_popupTheme 4
int[] styleable SwitchCompat { 0x01010125, 0x01010124, 0x01010142, 0x7f0400c3, 0x7f0400c9, 0x7f0400d4, 0x7f0400d5, 0x7f0400d7, 0x7f0400e5, 0x7f0400e6, 0x7f0400e7, 0x7f0400fc, 0x7f0400fd, 0x7f0400fe }
int styleable SwitchCompat_android_textOff 0
int styleable SwitchCompat_android_textOn 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TextAppearance { 0x010103ac, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x01010098, 0x0101009a, 0x0101009b, 0x01010095, 0x01010097, 0x01010096, 0x7f040073, 0x7f0400d8 }
int styleable TextAppearance_android_fontFamily 0
int styleable TextAppearance_android_shadowColor 1
int styleable TextAppearance_android_shadowDx 2
int styleable TextAppearance_android_shadowDy 3
int styleable TextAppearance_android_shadowRadius 4
int styleable TextAppearance_android_textColor 5
int styleable TextAppearance_android_textColorHint 6
int styleable TextAppearance_android_textColorLink 7
int styleable TextAppearance_android_textSize 8
int styleable TextAppearance_android_textStyle 9
int styleable TextAppearance_android_typeface 10
int styleable TextAppearance_fontFamily 11
int styleable TextAppearance_textAllCaps 12
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f04003e, 0x7f040049, 0x7f04004a, 0x7f040058, 0x7f040059, 0x7f04005a, 0x7f04005b, 0x7f04005c, 0x7f04005d, 0x7f04009e, 0x7f04009f, 0x7f0400a0, 0x7f0400a3, 0x7f0400a4, 0x7f0400b0, 0x7f0400cf, 0x7f0400d0, 0x7f0400d1, 0x7f0400ed, 0x7f0400ee, 0x7f0400ef, 0x7f0400f0, 0x7f0400f1, 0x7f0400f2, 0x7f0400f3, 0x7f0400f4, 0x7f0400f5 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_navigationContentDescription 14
int styleable Toolbar_navigationIcon 15
int styleable Toolbar_popupTheme 16
int styleable Toolbar_subtitle 17
int styleable Toolbar_subtitleTextAppearance 18
int styleable Toolbar_subtitleTextColor 19
int styleable Toolbar_title 20
int styleable Toolbar_titleMargin 21
int styleable Toolbar_titleMarginBottom 22
int styleable Toolbar_titleMarginEnd 23
int styleable Toolbar_titleMarginStart 24
int styleable Toolbar_titleMarginTop 25
int styleable Toolbar_titleMargins 26
int styleable Toolbar_titleTextAppearance 27
int styleable Toolbar_titleTextColor 28
int[] styleable View { 0x010100da, 0x01010000, 0x7f0400a9, 0x7f0400aa, 0x7f0400e3 }
int styleable View_android_focusable 0
int styleable View_android_theme 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f040035, 0x7f040036 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f3, 0x010100f2 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_inflatedId 1
int styleable ViewStubCompat_android_layout 2
