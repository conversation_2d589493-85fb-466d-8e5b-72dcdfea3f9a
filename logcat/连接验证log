2025-05-15 18:06:33.279 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.280 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.280 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   _ExamplesMenuState._addLog (package:test1_example/examples_menu.dart:84:9)[0m
2025-05-15 18:06:33.280 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.280 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试直接连接QCC5125设备 (MAC: 53:4A:52:FE:02:37)...[0m
2025-05-15 18:06:33.280 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.280 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.280 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   _ExamplesMenuState._addLog (package:test1_example/examples_menu.dart:84:9)[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 已将设备注册到DeviceDataManager: QCC5125, 句柄: 10001[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   _ExamplesMenuState._addLog (package:test1_example/examples_menu.dart:84:9)[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 创建直连设备成功，准备连接: QCC5125 (MAC: 53:4A:52:FE:02:37)[0m
2025-05-15 18:06:33.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager.connect (package:topping_ble_control/device/d900/d900_device_manager.dart:565:9)[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900: 连接到设备, 句柄: 10001[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[D900] 连接状态: 连接到设备, 句柄: 10001[0m
2025-05-15 18:06:33.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:334:9)[0m
2025-05-15 18:06:33.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 连接时间打印 --- 准备连接设备，句柄: 10001, 时间戳: 1747303593283[0m
2025-05-15 18:06:33.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:395:9)[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 发送连接中事件完成, 时间戳: 1747303593283[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:398:9)[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 调用底层连接函数, 时间戳: 1747303593284[0m
2025-05-15 18:06:33.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleOperationManager.connect (package:topping_ble_control/bluetooth/ble_operation_manager.dart:152:11)[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 开始连接设备，句柄: 10001[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector.connectToDevice (package:topping_ble_control/bluetooth/connection/ble_connector.dart:34:9)[0m
2025-05-15 18:06:33.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试连接设备，句柄: 10001[0m
2025-05-15 18:06:33.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:33.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[设备] 连接状态: 尝试连接设备 (句柄: 10001)[0m
2025-05-15 18:06:33.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:115:9)[0m
2025-05-15 18:06:33.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 准备设备连接，句柄: 10001[0m
2025-05-15 18:06:33.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.288 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.288 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:160:9)[0m
2025-05-15 18:06:33.289 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.289 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备准备就绪[0m
2025-05-15 18:06:33.289 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.289 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.290 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.290 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._connectWithTimeout (package:topping_ble_control/bluetooth/connection/ble_connector.dart:179:9)[0m
2025-05-15 18:06:33.290 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.290 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试连接设备: QCC5125, 时间戳: 1747303593289[0m
2025-05-15 18:06:33.290 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.290 20958-20958 ViewRootImplExtImpl     com.example.test1_example            D  the up motion event handled by client, just return
2025-05-15 18:06:33.292 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.292 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.292 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceBindings.connect (package:topping_ble_control/device/d900/d900_device_bindings.dart:496:11)[0m
2025-05-15 18:06:33.292 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.292 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900DeviceBindings: D900DeviceBindings: 连接设备, 句柄: 10001[0m
2025-05-15 18:06:33.292 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.292 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient::connect被调用, callback: 0xb4000078ce8fc868
2025-05-15 18:06:33.292 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 构造FlutterGatt对象, 回调指针: 0xb4000078ce8fc888
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:129:13)[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用init, gatt nativeObject: -5476376629577531136[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.293 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] FlutterGatt回调状态:
2025-05-15 18:06:33.293 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb400007871c7c100
2025-05-15 18:06:33.293 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback指针: 0xb4000078ce8fc888
2025-05-15 18:06:33.293 20958-20958 Topping Controller      com.example.test1_example            I    - mFlutterObject: 1747303593293
2025-05-15 18:06:33.293 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback有效
2025-05-15 18:06:33.293 20958-20958 Topping Controller      com.example.test1_example            I    - mFunctions.init: 0x77d7300458
2025-05-15 18:06:33.293 20958-20958 Topping Controller      com.example.test1_example            I    - mFunctions.write_characteristic: 0x77d7300488
2025-05-15 18:06:33.293 20958-20958 Topping Controller      com.example.test1_example            I    - mFunctions.set_characteristic_notification: 0x77d7300490
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleBindings._gattConnect (package:topping_ble_control/bluetooth/ble_bindings.dart:98:9)[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 Connecting to device[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:142:13)[0m
2025-05-15 18:06:33.293 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用connect, gatt flutterObject: 1747303593293[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._handleConnect (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:186:9)[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 连接请求，句柄: 10001[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.294 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(connect完成):
2025-05-15 18:06:33.294 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:33.294 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:33.294 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:33.294 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 2
2025-05-15 18:06:33.294 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:33.294 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:33.294 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager.connectNative (package:topping_ble_control/device/d900/d900_device_manager.dart:74:9)[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 底层连接函数调用完成, 时间戳: 1747303593294[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.294 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[D900] 连接状态: 调用底层connectNative, 句柄: 10001[0m
2025-05-15 18:06:33.295 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.295 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.295 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.295 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:420:11)[0m
2025-05-15 18:06:33.295 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.295 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 底层连接函数调用完成, 时间戳: 1747303593295[0m
2025-05-15 18:06:33.295 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.309 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleOperationManager.connect (package:topping_ble_control/bluetooth/ble_operation_manager.dart:152:11)[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 开始连接设备，句柄: 10001[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector.connectToDevice (package:topping_ble_control/bluetooth/connection/ble_connector.dart:34:9)[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试连接设备，句柄: 10001[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.310 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[设备] 连接状态: 尝试连接设备 (句柄: 10001)[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:115:9)[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 准备设备连接，句柄: 10001[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:160:9)[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备准备就绪[0m
2025-05-15 18:06:33.311 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.312 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.312 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.312 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._connectWithTimeout (package:topping_ble_control/bluetooth/connection/ble_connector.dart:179:9)[0m
2025-05-15 18:06:33.312 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.312 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试连接设备: QCC5125, 时间戳: 1747303593312[0m
2025-05-15 18:06:33.312 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.313 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: connect
2025-05-15 18:06:33.314 20958-20958 BluetoothGatt           com.example.test1_example            D  connect() - device: XX:XX:XX:XX:02:37, auto: false
2025-05-15 18:06:33.314 20958-20958 BluetoothGatt           com.example.test1_example            D  registerApp()
2025-05-15 18:06:33.314 20958-20958 BluetoothGatt           com.example.test1_example            D  registerApp() - UUID=acfaa33d-d93c-488b-b09b-87d43c8ce673
2025-05-15 18:06:33.318 20958-21287 BluetoothGatt           com.example.test1_example            D  onClientRegistered() - status=0 clientIf=15
2025-05-15 18:06:33.569 20958-21299 BluetoothGatt           com.example.test1_example            D  onClientConnectionState() - status=0 clientIf=15 connected=true device=53:4A:52:FE:02:37
2025-05-15 18:06:33.569 20958-21299 [FBP-Android]           com.example.test1_example            D  [FBP] onConnectionStateChange:connected
2025-05-15 18:06:33.569 20958-21299 [FBP-Android]           com.example.test1_example            D  [FBP]   status: SUCCESS
2025-05-15 18:06:33.571 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.571 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.571 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._setupConnectionStateListener.<anonymous closure> (package:topping_ble_control/bluetooth/connection/ble_connector.dart:65:15)[0m
2025-05-15 18:06:33.571 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.571 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 监听到连接状态变化: BluetoothConnectionState.connected, 时间戳: 1747303593571[0m
2025-05-15 18:06:33.571 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.572 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.572 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.572 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:33.572 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.572 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[QCC5125] 连接状态: 状态变化为 BluetoothConnectionState.connected[0m
2025-05-15 18:06:33.572 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.572 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.572 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.572 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   DeviceDataManager.registerDevice (package:topping_ble_control/registry/device_data_manager.dart:52:11)[0m
2025-05-15 18:06:33.573 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.573 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 注册设备并添加MAC索引: QCC5125, MAC: 53:4A:52:FE:02:37[0m
2025-05-15 18:06:33.573 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.573 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.573 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:33.573 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   DeviceDataManager.clearScanResultsExceptConnected (package:topping_ble_control/registry/device_data_manager.dart:246:9)[0m
2025-05-15 18:06:33.573 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.573 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 DeviceManager: 已清除所有扫描设备，保留QCC5125[0m
2025-05-15 18:06:33.573 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.575 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: connect
2025-05-15 18:06:33.576 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] already connected
2025-05-15 18:06:33.693 20958-21299 BluetoothGatt           com.example.test1_example            D  onConnectionUpdated() - Device=53:4A:52:FE:02:37 interval=24 latency=0 timeout=500 status=59
2025-05-15 18:06:33.804 20958-20958 flutter                 com.example.test1_example            I  data = 5ec7fffe006578da358b4b0a80201445f772c70e2c2acacd84f81ee1c00f2a41887b4f8366f79ec3a9308ea02681f2448692029973b6c19f96becb299940fc6f972f28408074d150153ac6f3e634922e2629bb1b2c694fc10db4ad723f96635ed0da0bac4e221271c3016f
2025-05-15 18:06:33.804 20958-20958 flutter                 com.example.test1_example            I  data1 = 5ec7fffe00b378da258bc90e82301445ffe56d251154064958688c71835014d195a1b496415aa47520847f97e8eee69e737ac86a02ee4c03d535145c5d0349a52c04bf16e36f6840db3613e48fc65d4b062e8006245529b83dbc2827a2fdd960cd6cc31c992c184fd5b31d2b38e8f792d024c8a38a61b4893fbb49c492c7f66d0589e1a0f9ad489b05aef353e88b4bdc618cedca6ff8d4ae7484b9e0e4b88f55c34cb93e3b6459f250a195853c0f86e10b34023d41b437cb16
2025-05-15 18:06:33.805 20958-20958 flutter                 com.example.test1_example            I  result = 9419925525401011202185313975101283220692471141991444422022051322483022519215426513612379131102247158195169481421603812924268134146215311518219315915019020341153642521111514740641281162098021581982432305214646384118727441057919313180173114631509994208218111727834181131951111
2025-05-15 18:06:33.805 20958-20958 flutter                 com.example.test1_example            I  result1 = 94199255254017912021837139201141304820692552291093717846738810414011313180202091491611801506590164117321321271512322382301581151222001062238763213532092933731654441912222711110464219541922814319893756461286368541184611884039162253217962051081952815344247921317929435623224714620836200163138971801376318773196146199246109513722516024917372155517424383232139752209714023720211124821217411613218522422818414385195761856259100892428016114913360151342251152261651805520322
2025-05-15 18:06:33.809 20958-20958 flutter                 com.example.test1_example            I  [38;5;196m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.809 20958-20958 flutter                 com.example.test1_example            I  [38;5;196m│ #0   Log.e (package:topping_ble_control/utils/log_util.dart:32:13)[0m
2025-05-15 18:06:33.809 20958-20958 flutter                 com.example.test1_example            I  [38;5;196m│ #1   _BluetoothAppState._addLog (package:test1_example/main.dart:1136:9)[0m
2025-05-15 18:06:33.809 20958-20958 flutter                 com.example.test1_example            I  [38;5;196m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:33.810 20958-20958 flutter                 com.example.test1_example            I  [38;5;196m│ ⛔ BLE Example 初始化完成 (使用 DeviceFactory)[0m
2025-05-15 18:06:33.810 20958-20958 flutter                 com.example.test1_example            I  [38;5;196m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:33.929 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: requestMtu
2025-05-15 18:06:33.929 20958-20958 BluetoothGatt           com.example.test1_example            D  configureMTU() - device: XX:XX:XX:XX:02:37 mtu: 512
2025-05-15 18:06:34.535 20958-21287 BluetoothGatt           com.example.test1_example            D  onConnectionUpdated() - Device=53:4A:52:FE:02:37 interval=72 latency=4 timeout=400 status=0
2025-05-15 18:06:41.474 20958-21287 BluetoothGatt           com.example.test1_example            D  onConfigureMTU() - Device=53:4A:52:FE:02:37 mtu=263 status=0
2025-05-15 18:06:41.474 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onMtuChanged:
2025-05-15 18:06:41.474 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   mtu: 263
2025-05-15 18:06:41.474 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 18:06:41.483 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: discoverServices
2025-05-15 18:06:41.484 20958-20958 BluetoothGatt           com.example.test1_example            D  discoverServices() - device: XX:XX:XX:XX:02:37
2025-05-15 18:06:41.490 20958-21287 BluetoothGatt           com.example.test1_example            D  onSearchComplete() = Device=53:4A:52:FE:02:37 Status=0
2025-05-15 18:06:41.490 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onServicesDiscovered:
2025-05-15 18:06:41.490 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   count: 5
2025-05-15 18:06:41.490 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   status: 0GATT_SUCCESS
2025-05-15 18:06:41.852 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: requestMtu
2025-05-15 18:06:41.853 20958-20958 BluetoothGatt           com.example.test1_example            D  configureMTU() - device: XX:XX:XX:XX:02:37 mtu: 512
2025-05-15 18:06:41.859 20958-21287 BluetoothGatt           com.example.test1_example            D  onConfigureMTU() - Device=53:4A:52:FE:02:37 mtu=263 status=0
2025-05-15 18:06:41.859 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onMtuChanged:
2025-05-15 18:06:41.859 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   mtu: 263
2025-05-15 18:06:41.859 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 18:06:41.862 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: setNotifyValue
2025-05-15 18:06:41.863 20958-20958 BluetoothGatt           com.example.test1_example            D  setCharacteristicNotification() - uuid: 00002a05-0000-1000-8000-00805f9b34fb enable: true
2025-05-15 18:06:42.005 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onDescriptorWrite:
2025-05-15 18:06:42.006 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 2a05
2025-05-15 18:06:42.006 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   desc: 2902
2025-05-15 18:06:42.006 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 18:06:42.012 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.013 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.013 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._discoverServices (package:topping_ble_control/bluetooth/connection/ble_connector.dart:205:11)[0m
2025-05-15 18:06:42.013 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.013 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 服务发现完成，发现 5 个服务, 时间戳: 1747303602012[0m
2025-05-15 18:06:42.013 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.014 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.014 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.014 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._registerServices (package:topping_ble_control/bluetooth/connection/ble_connector.dart:218:9)[0m
2025-05-15 18:06:42.014 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.014 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 注册 5 个服务[0m
2025-05-15 18:06:42.015 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.015 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.015 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.016 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyConnectionStateChange (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:24:9)[0m
2025-05-15 18:06:42.016 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.016 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 通知连接状态变化: FfiBluetoothProfileState.stateConnected -> FfiBluetoothProfileState.stateConnected nativeObject: -5476376629577531136[0m
2025-05-15 18:06:42.016 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.017 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.017 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.017 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.017 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.017 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[设备] 连接状态: 状态变化为 FfiBluetoothProfileState.stateConnected[0m
2025-05-15 18:06:42.017 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.018 20958-20958 Topping Controller      com.example.test1_example            I  onStateChange; state:0
2025-05-15 18:06:42.018 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.018 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.018 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceBindings._onStateChange (package:topping_ble_control/device/d900/d900_device_bindings.dart:287:9)[0m
2025-05-15 18:06:42.018 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.018 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 Flutter回调: onStateChange, flutterObject: 1747303558296, state: 0[0m
2025-05-15 18:06:42.018 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.019 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.019 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.019 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager._handleStateChange (package:topping_ble_control/device/d900/d900_device_manager.dart:135:9)[0m
2025-05-15 18:06:42.019 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.019 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900: 收到状态变更: BleConnectionState.connectedUnsafe, 设备句柄: 10001, 时间戳: 1747303602018[0m
2025-05-15 18:06:42.019 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.019 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.019 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.019 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager.verify (package:topping_ble_control/device/d900/d900_device_manager.dart:388:9)[0m
2025-05-15 18:06:42.020 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.020 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900: 开始验证设备[0m
2025-05-15 18:06:42.020 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.020 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.020 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.020 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.020 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.020 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 执行命令: 开始验证设备[0m
2025-05-15 18:06:42.020 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.021 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.021 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.021 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.021 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.021 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 开始验证设备: QCC5125[0m
2025-05-15 18:06:42.021 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.022 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.022 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.022 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.startVerifying (package:topping_ble_control/utils/verify_interceptor.dart:67:11)[0m
2025-05-15 18:06:42.022 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.022 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 VerifyInterceptor: 开始验证模式，会话ID：1747303602022_1[0m
2025-05-15 18:06:42.022 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.023 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.023 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.023 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.023 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.023 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔍 ==== 验证开始 ==== 🔍 [会话ID: 1747303602022_1][0m
2025-05-15 18:06:42.023 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.024 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.024 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.024 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.startVerifying (package:topping_ble_control/utils/verify_interceptor.dart:74:11)[0m
2025-05-15 18:06:42.024 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.024 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ====== 开始验证阶段 1 ======[0m
2025-05-15 18:06:42.024 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.025 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.025 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.025 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:166:15)[0m
2025-05-15 18:06:42.025 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.025 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用getService: flutterObject: 1747303593293, uuid: 000090FB-0000-1000-8000-00805F9B34FB[0m
2025-05-15 18:06:42.025 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.027 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.027 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.027 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:152:13)[0m
2025-05-15 18:06:42.027 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.027 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用writeCharacteristic, flutterObject: 1747303593293[0m
2025-05-15 18:06:42.027 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.027 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyServicesDiscovered (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:51:9)[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 通知服务发现完成 nativeObject: -5476376629577531136[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 蓝牙服务发现完成[0m
2025-05-15 18:06:42.028 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.029 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.029 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.029 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:166:15)[0m
2025-05-15 18:06:42.029 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.029 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用getService: flutterObject: 1747303593293, uuid: 000090FB-0000-1000-8000-00805F9B34FB[0m
2025-05-15 18:06:42.029 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.030 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] FlutterGatt::setCharacteristicNotification被调用, UUID: 00009CF1-0000-1000-8000-00805F9B34FB, enable: 1
2025-05-15 18:06:42.030 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] FlutterGatt回调状态:
2025-05-15 18:06:42.030 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb400007871c7c100
2025-05-15 18:06:42.030 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback指针: 0xb4000078ce8fc888
2025-05-15 18:06:42.030 20958-20958 Topping Controller      com.example.test1_example            I    - mFlutterObject: 1747303593293
2025-05-15 18:06:42.030 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback有效
2025-05-15 18:06:42.030 20958-20958 Topping Controller      com.example.test1_example            I    - mFunctions.init: 0x77d7300458
2025-05-15 18:06:42.030 20958-20958 Topping Controller      com.example.test1_example            I    - mFunctions.write_characteristic: 0x77d7300488
2025-05-15 18:06:42.030 20958-20958 Topping Controller      com.example.test1_example            I    - mFunctions.set_characteristic_notification: 0x77d7300490
2025-05-15 18:06:42.030 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.030 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.030 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:157:13)[0m
2025-05-15 18:06:42.030 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.030 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用setCharacteristicNotification, flutterObject: 1747303593293, enable: 1[0m
2025-05-15 18:06:42.030 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.031 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] setCharacteristicNotification结果: 1
2025-05-15 18:06:42.034 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.034 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.034 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.writeCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:38:9)[0m
2025-05-15 18:06:42.034 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.034 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入特征值: 00008efa-0000-1000-8000-00805f9b34fb, 数据: [94, 199, 255, 254, 0, 101, 120, 218, 37, 203, 59, 10, 192, 32, 20, 68, 209, 189, 76, 109, 161, 65, 48, 113, 51, 34, 42, 193, 194, 15, 42, 129, 32, 238, 61, 79, 210, 13, 247, 48, 19, 46, 121, 104, 193, 48, 222, 26, 160, 57, 67, 15, 189, 199, 146, 77, 164, 46, 25, 66, 107, 174, 248, 159, 104, 167, 126, 67, 3, 12, 222, 14, 11, 61, 97, 107, 53, 79, 104, 251, 66, 32, 56, 39, 219, 173, 217, 236, 75, 162, 164, 148, 58, 4, 151, 215, 137, 181, 62, 140, 22, 33, 227, 201, 126, 16, 183][0m
2025-05-15 18:06:42.034 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.writeCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:42:9)[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入特征值: 00008efa-0000-1000-8000-00805f9b34fb, 数据: 5e c7 ff fe 00 65 78 da 25 cb 3b 0a c0 20 14 44 d1 bd 4c 6d a1 41 30 71 33 22 2a c1 c2 0f 2a 81 20 ee 3d 4f d2 0d f7 30 13 2e 79 68 c1 30 de 1a a0 39 43 0f bd c7 92 4d a4 2e 19 42 6b ae f8 9f 68 a7 7e 43 03 0c de 0e 0b 3d 61 6b 35 4f 68 fb 42 20 38 27 db ad d9 ec 4b a2 a4 94 3a 04 97 d7 89 b5 3e 8c 16 21 e3 c9 7e 10 b7,[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 时间戳: 1747303602035[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.035 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 发送到[特征值]: 00008efa-0000-1000-8000-00805f9b34fb - 5e c7 ff fe 00 65 78 da 25 cb 3b 0a c0 20 14 44 d1 bd 4c 6d a1 41 30 71 33 22 2a c1 c2 0f 2a 81 20 ee 3d 4f d2 0d f7 30 13 2e 79 68 c1 30 de 1a a0 39 43 0f bd c7 92 4d a4 2e 19 42 6b ae f8 9f 68 a7 7e 43 03 0c de 0e 0b 3d 61 6b 35 4f 68 fb 42 20 38 27 db ad d9 ec 4b a2 a4 94 3a 04 97 d7 89 b5 3e 8c 16 21 e3 c9 7e 10 b7[0m
2025-05-15 18:06:42.036 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.036 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.036 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.036 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.036 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.036 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 验证数据(发送) [会话ID: 1747303602022_1] [阶段1][0m
2025-05-15 18:06:42.036 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.setCharacteristicNotification (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:321:11)[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设置特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 启用: true, 时间戳: 1747303602038[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNotificationHelper.enableNotifications (package:topping_ble_control/bluetooth/ble_notification_helper.dart:28:11)[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 已启用 DX9 的通知特征[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.038 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._enableNotifications.<anonymous closure> (package:topping_ble_control/bluetooth/connection/ble_connector.dart:248:11)[0m
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备通知启用成功[0m
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  │ #0   Log.d (package:topping_ble_control/utils/log_util.dart:20:13)
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  │ #1   BleCharacteristicHandler.setCharacteristicNotification (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:331:13)
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  │ 🐛 已取消之前的订阅: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:42.039 20958-20958 flutter                 com.example.test1_example            I  └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:42.039 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: writeCharacteristic
2025-05-15 18:06:42.046 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.046 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.046 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager.deleteVerifyLogFile (package:topping_ble_control/utils/app_log_manager.dart:153:13)[0m
2025-05-15 18:06:42.046 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.046 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 已删除旧的验证日志文件[0m
2025-05-15 18:06:42.046 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.050 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.051 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.051 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptSendData (package:topping_ble_control/utils/verify_interceptor.dart:152:11)[0m
2025-05-15 18:06:42.051 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.051 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据内容 [会话ID: 1747303602022_1]: 5e c7 ff fe 00 65 78 da 25 cb 3b 0a c0 20 14 44 d1 bd 4c 6d a1 41 30 71 33 22 2a c1 c2 0f 2a 81 20 ee 3d 4f d2 0d f7 30 13 2e 79 68 c1 30 de 1a a0 39 43 0f bd c7 92 4d a4 2e 19 42 6b ae f8 9f 68 a7 7e 43 03 0c de 0e 0b 3d 61 6b 35 4f 68 fb 42 20 38 27 db ad d9 ec 4b a2 a4 94 3a 04 97 d7 89 b5 3e 8c 16 21 e3 c9 7e 10 b7[0m
2025-05-15 18:06:42.051 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.053 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.053 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.053 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptSendData (package:topping_ble_control/utils/verify_interceptor.dart:161:11)[0m
2025-05-15 18:06:42.053 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.053 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据信息 [会话ID: 1747303602022_1]: 数据长度: 107 字节, 时间: 2025-05-15 18:06:42.053275[0m
2025-05-15 18:06:42.053 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.276 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onCharacteristicWrite:
2025-05-15 18:06:42.276 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 8efa
2025-05-15 18:06:42.276 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 18:06:42.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.281 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler._writeToCharacteristic.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:169:15)[0m
2025-05-15 18:06:42.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入特征值成功，特征值UUID: 00008efa-0000-1000-8000-00805f9b34fb, 时间戳: 1747303602280[0m
2025-05-15 18:06:42.282 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler._writeToCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:268:11)[0m
2025-05-15 18:06:42.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.283 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入操作成功, 特征值UUID: 00008efa-0000-1000-8000-00805f9b34fb, 时间戳: 1747303602282[0m
2025-05-15 18:06:42.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.284 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ 验证结果: 成功 [会话ID: 1747303602022_1] [阶段1][0m
2025-05-15 18:06:42.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 - 原因: 写入结果[0m
2025-05-15 18:06:42.285 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.286 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleOperationManager.writeCharacteristic (package:topping_ble_control/bluetooth/ble_operation_manager.dart:179:11)[0m
2025-05-15 18:06:42.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写特征值成功[0m
2025-05-15 18:06:42.287 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.288 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: discoverServices
2025-05-15 18:06:42.288 20958-20958 BluetoothGatt           com.example.test1_example            D  discoverServices() - device: XX:XX:XX:XX:02:37
2025-05-15 18:06:42.296 20958-21287 BluetoothGatt           com.example.test1_example            D  onSearchComplete() = Device=53:4A:52:FE:02:37 Status=0
2025-05-15 18:06:42.296 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onServicesDiscovered:
2025-05-15 18:06:42.296 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   count: 5
2025-05-15 18:06:42.298 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   status: 0GATT_SUCCESS
2025-05-15 18:06:42.302 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.302 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.302 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptWriteResult (package:topping_ble_control/utils/verify_interceptor.dart:188:11)[0m
2025-05-15 18:06:42.302 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.302 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ ==== 阶段 1 写入成功 ==== [会话ID: 1747303602022_1][0m
2025-05-15 18:06:42.302 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.310 20958-20958 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: setNotifyValue
2025-05-15 18:06:42.310 20958-20958 BluetoothGatt           com.example.test1_example            D  setCharacteristicNotification() - uuid: 00009cf1-0000-1000-8000-00805f9b34fb enable: true
2025-05-15 18:06:42.457 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onDescriptorWrite:
2025-05-15 18:06:42.457 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 9cf1
2025-05-15 18:06:42.457 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   desc: 2902
2025-05-15 18:06:42.457 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 18:06:42.461 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.462 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.462 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.setCharacteristicNotification (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:419:19)[0m
2025-05-15 18:06:42.462 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.462 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 成功启用特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 时间戳: 1747303602460[0m
2025-05-15 18:06:42.462 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.463 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.463 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.463 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.463 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.463 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ 验证结果: 成功 [会话ID: 1747303602022_1] [阶段1][0m
2025-05-15 18:06:42.464 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 - 原因: 写入结果：成功设置特征值通知[0m
2025-05-15 18:06:42.464 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.476 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.476 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.476 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptWriteResult (package:topping_ble_control/utils/verify_interceptor.dart:188:11)[0m
2025-05-15 18:06:42.476 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.476 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ ==== 阶段 1 写入成功 ==== [会话ID: 1747303602022_1][0m
2025-05-15 18:06:42.476 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.819 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onCharacteristicChanged:
2025-05-15 18:06:42.819 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 9cf1
2025-05-15 18:06:42.820 20958-21287 BluetoothGatt           com.example.test1_example            D  onConnectionUpdated() - Device=53:4A:52:FE:02:37 interval=72 latency=4 timeout=400 status=59
2025-05-15 18:06:42.821 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onCharacteristicChanged:
2025-05-15 18:06:42.821 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 9cf1
2025-05-15 18:06:42.822 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onCharacteristicChanged:
2025-05-15 18:06:42.823 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 9cf1
2025-05-15 18:06:42.823 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:356:23)[0m
2025-05-15 18:06:42.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 收到特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 值: 5e c7 00 00 00 3a 78 da 25 cd 5d 6f 82 30 14 06 e0 ff 72 6e 47 b2 52 50 94 c4 0b c3 e2 24 73 d9 30 93 90 dd 98 86 96 82 f2 d9 42 8d 1a ff bb 07 bc 7b f3 bc e7 e3 0e 69 c5 c1 b7 2d e8 af ad 98, 长度: 64, 时间戳: 1747303602822[0m
2025-05-15 18:06:42.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.825 20958-20958 flutter                 com.example.test1_example            I  ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:42.826 20958-20958 flutter                 com.example.test1_example            I  │ #0   Log.d (package:topping_ble_control/utils/log_util.dart:20:13)
2025-05-15 18:06:42.826 20958-20958 flutter                 com.example.test1_example            I  │ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:359:23)
2025-05-15 18:06:42.826 20958-20958 flutter                 com.example.test1_example            I  ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-05-15 18:06:42.826 20958-20958 flutter                 com.example.test1_example            I  │ 🐛 通知详情 - 长度: 64, 原始数据: [94, 199, 0, 0, 0, 58, 120, 218, 37, 205, 93, 111, 130, 48, 20, 6, 224, 255, 114, 110, 71, 178, 82, 80, 148, 196, 11, 195, 226, 36, 115, 217, 48, 147, 144, 221, 152, 134, 150, 130, 242, 217, 66, 141, 26, 255, 187, 7, 188, 123, 243, 188, 231, 227, 14, 105, 197, 193, 183, 45, 232, 175, 173, 152]
2025-05-15 18:06:42.827 20958-20958 flutter                 com.example.test1_example            I  └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:42.828 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.828 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.828 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.829 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.829 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1-0000-1000-8000-00805F9B34FB - 5e c7 00 00 00 3a 78 da 25 cd 5d 6f 82 30 14 06 e0 ff 72 6e 47 b2 52 50 94 c4 0b c3 e2 24 73 d9 30 93 90 dd 98 86 96 82 f2 d9 42 8d 1a ff bb 07 bc 7b f3 bc e7 e3 0e 69 c5 c1 b7 2d e8 af ad 98[0m
2025-05-15 18:06:42.829 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.831 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.831 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.831 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.831 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.831 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 验证数据(响应) [会话ID: 1747303602022_1] [阶段1][0m
2025-05-15 18:06:42.831 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.833 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.833 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.833 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyCharacteristicChanged (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:74:9)[0m
2025-05-15 18:06:42.833 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.833 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 准备通知特征值变化到C++层, uuid: 00009CF1-0000-1000-8000-00805F9B34FB, 值长度: 64, nativeObject: -5476376629577531136[0m
2025-05-15 18:06:42.833 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.834 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.835 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.835 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.835 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.835 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1... - 5e c7 00 00 00 3a 78 da 25 cd 5d 6f 82 30 14 06 e0 ff 72 6e 47 b2 52 50 94 c4 0b c3 e2 24 73 d9 30 93 90 dd 98 86 96 82 f2 d9 42 8d 1a ff bb 07 bc 7b f3 bc e7 e3 0e 69 c5 c1 b7 2d e8 af ad 98[0m
2025-05-15 18:06:42.835 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] flutter_gatt_on_characteristic_changed被调用 - native_object: -5476376629577531136, uuid: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备创建bluetoothGattCharacteristic对象
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据前8字节(或更少): 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  3a 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  78 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  da 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备调用flutterGatt->mCallback->onCharacteristicChanged
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient::onCharacteristicChanged被调用 - UUID: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged开始):
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 0
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] UUID匹配NOTIFY_CHARACTERISTIC_UUID, 准备调用mergeCharacteristicPackage
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据长度: 64, 前8字节(或更少): 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  3a 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  78 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  da 
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 包索引: 0
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 索引为0，清空接收缓冲区
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage处理完成
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage调用完成
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged结束):
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 0
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:42.835 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] onCharacteristicChanged回调调用完成
2025-05-15 18:06:42.836 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:356:23)[0m
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 收到特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 值: 5e c7 00 01 00 3a 82 16 5a 17 4d 7d 2c d0 5d 0b 84 52 69 c3 b1 22 53 ae b4 04 1f c0 02 ce 7a 06 fe 1d b8 30 47 23 d4 b8 82 85 4d c8 d8 a1 29 56 f3 a6 1a c9 f1 88 3b 9b cf ec 05 36 46 a0 aa e9, 长度: 64, 时间戳: 1747303602836[0m
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  │ #0   Log.d (package:topping_ble_control/utils/log_util.dart:20:13)
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  │ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:359:23)
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  │ 🐛 通知详情 - 长度: 64, 原始数据: [94, 199, 0, 1, 0, 58, 130, 22, 90, 23, 77, 125, 44, 208, 93, 11, 132, 82, 105, 195, 177, 34, 83, 174, 180, 4, 31, 192, 2, 206, 122, 6, 254, 29, 184, 48, 71, 35, 212, 184, 130, 133, 77, 200, 216, 161, 41, 86, 243, 166, 26, 201, 241, 136, 59, 155, 207, 236, 5, 54, 70, 160, 170, 233]
2025-05-15 18:06:42.837 20958-20958 flutter                 com.example.test1_example            I  └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1-0000-1000-8000-00805F9B34FB - 5e c7 00 01 00 3a 82 16 5a 17 4d 7d 2c d0 5d 0b 84 52 69 c3 b1 22 53 ae b4 04 1f c0 02 ce 7a 06 fe 1d b8 30 47 23 d4 b8 82 85 4d c8 d8 a1 29 56 f3 a6 1a c9 f1 88 3b 9b cf ec 05 36 46 a0 aa e9[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.838 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 验证数据(响应) [会话ID: 1747303602022_1] [阶段1][0m
2025-05-15 18:06:42.839 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.839 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.839 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.839 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyCharacteristicChanged (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:74:9)[0m
2025-05-15 18:06:42.839 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.839 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 准备通知特征值变化到C++层, uuid: 00009CF1-0000-1000-8000-00805F9B34FB, 值长度: 64, nativeObject: -5476376629577531136[0m
2025-05-15 18:06:42.839 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1... - 5e c7 00 01 00 3a 82 16 5a 17 4d 7d 2c d0 5d 0b 84 52 69 c3 b1 22 53 ae b4 04 1f c0 02 ce 7a 06 fe 1d b8 30 47 23 d4 b8 82 85 4d c8 d8 a1 29 56 f3 a6 1a c9 f1 88 3b 9b cf ec 05 36 46 a0 aa e9[0m
2025-05-15 18:06:42.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] flutter_gatt_on_characteristic_changed被调用 - native_object: -5476376629577531136, uuid: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备创建bluetoothGattCharacteristic对象
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据前8字节(或更少): 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  01 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  3a 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  82 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  16 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备调用flutterGatt->mCallback->onCharacteristicChanged
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient::onCharacteristicChanged被调用 - UUID: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged开始):
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 0
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] UUID匹配NOTIFY_CHARACTERISTIC_UUID, 准备调用mergeCharacteristicPackage
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据长度: 64, 前8字节(或更少): 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  01 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  3a 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  82 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  16 
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 包索引: 1
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage处理完成
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage调用完成
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged结束):
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 0
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:42.840 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] onCharacteristicChanged回调调用完成
2025-05-15 18:06:42.842 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.842 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.842 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:356:23)[0m
2025-05-15 18:06:42.842 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.842 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 收到特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 值: 5e c7 00 02 00 3a 36 b8 8e 4d 28 5a ab 1a 3e a4 fd 0b 29 c5 61 44 5d c8 9a f5 83 c2 c7 b0 cb bb 3d 9f 3b db 44 cb db 56 26 1d 7d d3 ef 71 66 aa 8e 0d f9 97 b3 36 51 50 b2 cd 79 1d 2c dc 20 39, 长度: 64, 时间戳: 1747303602842[0m
2025-05-15 18:06:42.842 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.842 20958-20958 flutter                 com.example.test1_example            I  ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:42.842 20958-20958 flutter                 com.example.test1_example            I  │ #0   Log.d (package:topping_ble_control/utils/log_util.dart:20:13)
2025-05-15 18:06:42.842 20958-20958 flutter                 com.example.test1_example            I  │ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:359:23)
2025-05-15 18:06:42.843 20958-20958 flutter                 com.example.test1_example            I  ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-05-15 18:06:42.843 20958-20958 flutter                 com.example.test1_example            I  │ 🐛 通知详情 - 长度: 64, 原始数据: [94, 199, 0, 2, 0, 58, 54, 184, 142, 77, 40, 90, 171, 26, 62, 164, 253, 11, 41, 197, 97, 68, 93, 200, 154, 245, 131, 194, 199, 176, 203, 187, 61, 159, 59, 219, 68, 203, 219, 86, 38, 29, 125, 211, 239, 113, 102, 170, 142, 13, 249, 151, 179, 54, 81, 80, 178, 205, 121, 29, 44, 220, 32, 57]
2025-05-15 18:06:42.843 20958-20958 flutter                 com.example.test1_example            I  └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:42.843 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.843 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.843 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.843 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.843 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1-0000-1000-8000-00805F9B34FB - 5e c7 00 02 00 3a 36 b8 8e 4d 28 5a ab 1a 3e a4 fd 0b 29 c5 61 44 5d c8 9a f5 83 c2 c7 b0 cb bb 3d 9f 3b db 44 cb db 56 26 1d 7d d3 ef 71 66 aa 8e 0d f9 97 b3 36 51 50 b2 cd 79 1d 2c dc 20 39[0m
2025-05-15 18:06:42.843 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 验证数据(响应) [会话ID: 1747303602022_1] [阶段1][0m
2025-05-15 18:06:42.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyCharacteristicChanged (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:74:9)[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 准备通知特征值变化到C++层, uuid: 00009CF1-0000-1000-8000-00805F9B34FB, 值长度: 64, nativeObject: -5476376629577531136[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1... - 5e c7 00 02 00 3a 36 b8 8e 4d 28 5a ab 1a 3e a4 fd 0b 29 c5 61 44 5d c8 9a f5 83 c2 c7 b0 cb bb 3d 9f 3b db 44 cb db 56 26 1d 7d d3 ef 71 66 aa 8e 0d f9 97 b3 36 51 50 b2 cd 79 1d 2c dc 20 39[0m
2025-05-15 18:06:42.845 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.845 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] flutter_gatt_on_characteristic_changed被调用 - native_object: -5476376629577531136, uuid: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备创建bluetoothGattCharacteristic对象
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据前8字节(或更少): 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  02 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  3a 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  36 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  b8 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备调用flutterGatt->mCallback->onCharacteristicChanged
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient::onCharacteristicChanged被调用 - UUID: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged开始):
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 0
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] UUID匹配NOTIFY_CHARACTERISTIC_UUID, 准备调用mergeCharacteristicPackage
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据长度: 64, 前8字节(或更少): 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  02 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  3a 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  36 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  b8 
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 包索引: 2
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage处理完成
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage调用完成
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged结束):
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 0
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:42.846 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] onCharacteristicChanged回调调用完成
2025-05-15 18:06:42.859 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.859 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.859 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:234:11)[0m
2025-05-15 18:06:42.859 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.859 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据内容 [会话ID: 1747303602022_1]: 5e c7 00 01 00 3a 82 16 5a 17 4d 7d 2c d0 5d 0b 84 52 69 c3 b1 22 53 ae b4 04 1f c0 02 ce 7a 06 fe 1d b8 30 47 23 d4 b8 82 85 4d c8 d8 a1 29 56 f3 a6 1a c9 f1 88 3b 9b cf ec 05 36 46 a0 aa e9[0m
2025-05-15 18:06:42.859 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:234:11)[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据内容 [会话ID: 1747303602022_1]: 5e c7 00 00 00 3a 78 da 25 cd 5d 6f 82 30 14 06 e0 ff 72 6e 47 b2 52 50 94 c4 0b c3 e2 24 73 d9 30 93 90 dd 98 86 96 82 f2 d9 42 8d 1a ff bb 07 bc 7b f3 bc e7 e3 0e 69 c5 c1 b7 2d e8 af ad 98[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:234:11)[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据内容 [会话ID: 1747303602022_1]: 5e c7 00 02 00 3a 36 b8 8e 4d 28 5a ab 1a 3e a4 fd 0b 29 c5 61 44 5d c8 9a f5 83 c2 c7 b0 cb bb 3d 9f 3b db 44 cb db 56 26 1d 7d d3 ef 71 66 aa 8e 0d f9 97 b3 36 51 50 b2 cd 79 1d 2c dc 20 39[0m
2025-05-15 18:06:42.860 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.863 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.863 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.863 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:243:11)[0m
2025-05-15 18:06:42.863 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.863 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据信息 [会话ID: 1747303602022_1]: 数据长度: 64 字节, 时间: 2025-05-15 18:06:42.862926[0m
2025-05-15 18:06:42.863 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.863 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:243:11)[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据信息 [会话ID: 1747303602022_1]: 数据长度: 64 字节, 时间: 2025-05-15 18:06:42.863775[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:243:11)[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据信息 [会话ID: 1747303602022_1]: 数据长度: 64 字节, 时间: 2025-05-15 18:06:42.864506[0m
2025-05-15 18:06:42.864 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.866 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.866 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:42.866 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:249:11)[0m
2025-05-15 18:06:42.866 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:42.866 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 阶段 1 已收到设备响应 [会话ID: 1747303602022_1][0m
2025-05-15 18:06:42.867 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.867 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:42.867 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.536 20958-21287 LOG_FLOWCTRL            com.example.test1_example            W  ==LOGS OVER PROC QUOTA(300), rows(162) bytes(31843) com.example.test1_example DROPPED==
2025-05-15 18:06:43.536 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onCharacteristicWrite:
2025-05-15 18:06:43.537 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 8efa
2025-05-15 18:06:43.537 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 18:06:43.542 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.543 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.543 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler._writeToCharacteristic.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:169:15)[0m
2025-05-15 18:06:43.543 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.543 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入特征值成功，特征值UUID: 00008efa-0000-1000-8000-00805f9b34fb, 时间戳: 1747303603542[0m
2025-05-15 18:06:43.544 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.544 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.545 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.545 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler._writeToCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:268:11)[0m
2025-05-15 18:06:43.545 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.545 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入操作成功, 特征值UUID: 00008efa-0000-1000-8000-00805f9b34fb, 时间戳: 1747303603544[0m
2025-05-15 18:06:43.545 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.546 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.546 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.546 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:43.546 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.546 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ 验证结果: 成功 [会话ID: 1747303602022_1] [阶段1][0m
2025-05-15 18:06:43.547 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 - 原因: 写入结果[0m
2025-05-15 18:06:43.547 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.548 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.548 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.548 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleOperationManager.writeCharacteristic (package:topping_ble_control/bluetooth/ble_operation_manager.dart:179:11)[0m
2025-05-15 18:06:43.548 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.548 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写特征值成功[0m
2025-05-15 18:06:43.548 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.560 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.560 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.560 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptWriteResult (package:topping_ble_control/utils/verify_interceptor.dart:188:11)[0m
2025-05-15 18:06:43.560 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.560 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ ==== 阶段 1 写入成功 ==== [会话ID: 1747303602022_1][0m
2025-05-15 18:06:43.560 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.720 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onCharacteristicChanged:
2025-05-15 18:06:43.720 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 9cf1
2025-05-15 18:06:43.725 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.725 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.725 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:356:23)[0m
2025-05-15 18:06:43.726 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.726 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 收到特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 值: 5e c7 00 00 00 3a 78 da 1d c9 31 0a c0 20 10 00 c1 bf 6c 7d 45 12 48 73 9f 09 a2 12 2c 8c c1 b3 09 e2 df 23 76 cb 4e c7 e7 80 1e 42 fb de 88 ee 82 45 b3 54 9e 2b cd 7f 0a b1 56 5f c2 a4 6d 75, 长度: 64, 时间戳: 1747303603724[0m
2025-05-15 18:06:43.726 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.727 20958-20958 flutter                 com.example.test1_example            I  ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:43.727 20958-20958 flutter                 com.example.test1_example            I  │ #0   Log.d (package:topping_ble_control/utils/log_util.dart:20:13)
2025-05-15 18:06:43.727 20958-20958 flutter                 com.example.test1_example            I  │ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:359:23)
2025-05-15 18:06:43.728 20958-20958 flutter                 com.example.test1_example            I  ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-05-15 18:06:43.728 20958-20958 flutter                 com.example.test1_example            I  │ 🐛 通知详情 - 长度: 64, 原始数据: [94, 199, 0, 0, 0, 58, 120, 218, 29, 201, 49, 10, 192, 32, 16, 0, 193, 191, 108, 125, 69, 18, 72, 115, 159, 9, 162, 18, 44, 140, 193, 179, 9, 226, 223, 35, 118, 203, 78, 199, 231, 128, 30, 66, 251, 222, 136, 238, 130, 69, 179, 84, 158, 43, 205, 127, 10, 177, 86, 95, 194, 164, 109, 117]
2025-05-15 18:06:43.728 20958-20958 flutter                 com.example.test1_example            I  └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:43.729 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.729 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.729 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:43.729 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.729 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1-0000-1000-8000-00805F9B34FB - 5e c7 00 00 00 3a 78 da 1d c9 31 0a c0 20 10 00 c1 bf 6c 7d 45 12 48 73 9f 09 a2 12 2c 8c c1 b3 09 e2 df 23 76 cb 4e c7 e7 80 1e 42 fb de 88 ee 82 45 b3 54 9e 2b cd 7f 0a b1 56 5f c2 a4 6d 75[0m
2025-05-15 18:06:43.730 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.731 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.731 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.732 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:43.732 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.732 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 验证数据(响应) [会话ID: 1747303602022_1] [阶段1][0m
2025-05-15 18:06:43.732 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.733 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.733 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.733 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyCharacteristicChanged (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:74:9)[0m
2025-05-15 18:06:43.734 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.734 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 准备通知特征值变化到C++层, uuid: 00009CF1-0000-1000-8000-00805F9B34FB, 值长度: 64, nativeObject: -5476376629577531136[0m
2025-05-15 18:06:43.734 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.735 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.735 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.735 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:43.735 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.735 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1... - 5e c7 00 00 00 3a 78 da 1d c9 31 0a c0 20 10 00 c1 bf 6c 7d 45 12 48 73 9f 09 a2 12 2c 8c c1 b3 09 e2 df 23 76 cb 4e c7 e7 80 1e 42 fb de 88 ee 82 45 b3 54 9e 2b cd 7f 0a b1 56 5f c2 a4 6d 75[0m
2025-05-15 18:06:43.735 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] flutter_gatt_on_characteristic_changed被调用 - native_object: -5476376629577531136, uuid: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备创建bluetoothGattCharacteristic对象
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据前8字节(或更少): 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  3a 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  78 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  da 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备调用flutterGatt->mCallback->onCharacteristicChanged
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient::onCharacteristicChanged被调用 - UUID: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged开始):
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 0
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] UUID匹配NOTIFY_CHARACTERISTIC_UUID, 准备调用mergeCharacteristicPackage
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据长度: 64, 前8字节(或更少): 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  3a 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  78 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  da 
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 包索引: 0
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 索引为0，清空接收缓冲区
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage处理完成
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage调用完成
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged结束):
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 0
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:43.736 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] onCharacteristicChanged回调调用完成
2025-05-15 18:06:43.750 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.750 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.750 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:234:11)[0m
2025-05-15 18:06:43.750 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.750 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据内容 [会话ID: 1747303602022_1]: 5e c7 00 00 00 3a 78 da 1d c9 31 0a c0 20 10 00 c1 bf 6c 7d 45 12 48 73 9f 09 a2 12 2c 8c c1 b3 09 e2 df 23 76 cb 4e c7 e7 80 1e 42 fb de 88 ee 82 45 b3 54 9e 2b cd 7f 0a b1 56 5f c2 a4 6d 75[0m
2025-05-15 18:06:43.750 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.753 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.753 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.753 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:243:11)[0m
2025-05-15 18:06:43.753 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.753 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据信息 [会话ID: 1747303602022_1]: 数据长度: 64 字节, 时间: 2025-05-15 18:06:43.753154[0m
2025-05-15 18:06:43.753 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.756 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.756 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.756 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:249:11)[0m
2025-05-15 18:06:43.756 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.756 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 阶段 1 已收到设备响应 [会话ID: 1747303602022_1][0m
2025-05-15 18:06:43.756 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.810 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP] onCharacteristicChanged:
2025-05-15 18:06:43.811 20958-21287 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 9cf1
2025-05-15 18:06:43.816 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.816 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.816 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:356:23)[0m
2025-05-15 18:06:43.816 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.817 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 收到特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 值: 5e c7 ff ff 00 12 b6 1b 05 21 b8 e6 d0 3e c6 0f c4 2f 15 01 8c b2 43 1e, 长度: 24, 时间戳: 1747303603815[0m
2025-05-15 18:06:43.817 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.818 20958-20958 flutter                 com.example.test1_example            I  ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:43.818 20958-20958 flutter                 com.example.test1_example            I  │ #0   Log.d (package:topping_ble_control/utils/log_util.dart:20:13)
2025-05-15 18:06:43.818 20958-20958 flutter                 com.example.test1_example            I  │ #1   BleCharacteristicHandler.setCharacteristicNotification.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:359:23)
2025-05-15 18:06:43.818 20958-20958 flutter                 com.example.test1_example            I  ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-05-15 18:06:43.818 20958-20958 flutter                 com.example.test1_example            I  │ 🐛 通知详情 - 长度: 24, 原始数据: [94, 199, 255, 255, 0, 18, 182, 27, 5, 33, 184, 230, 208, 62, 198, 15, 196, 47, 21, 1, 140, 178, 67, 30]
2025-05-15 18:06:43.818 20958-20958 flutter                 com.example.test1_example            I  └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 18:06:43.819 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.820 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.820 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:43.820 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.820 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1-0000-1000-8000-00805F9B34FB - 5e c7 ff ff 00 12 b6 1b 05 21 b8 e6 d0 3e c6 0f c4 2f 15 01 8c b2 43 1e[0m
2025-05-15 18:06:43.820 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.821 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.822 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.822 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:43.822 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.822 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 验证数据(响应) [会话ID: 1747303602022_1] [阶段1][0m
2025-05-15 18:06:43.822 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.823 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyCharacteristicChanged (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:74:9)[0m
2025-05-15 18:06:43.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 准备通知特征值变化到C++层, uuid: 00009CF1-0000-1000-8000-00805F9B34FB, 值长度: 24, nativeObject: -5476376629577531136[0m
2025-05-15 18:06:43.824 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.825 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.825 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.825 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:06:43.826 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.826 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[特征值]: 00009CF1... - 5e c7 ff ff 00 12 b6 1b 05 21 b8 e6 d0 3e c6 0f c4 2f 15 01 8c b2 43 1e[0m
2025-05-15 18:06:43.826 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] flutter_gatt_on_characteristic_changed被调用 - native_object: -5476376629577531136, uuid: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备创建bluetoothGattCharacteristic对象
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据前8字节(或更少): 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  ff 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  ff 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  12 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  b6 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  1b 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 准备调用flutterGatt->mCallback->onCharacteristicChanged
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient::onCharacteristicChanged被调用 - UUID: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged开始):
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 0
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] UUID匹配NOTIFY_CHARACTERISTIC_UUID, 准备调用mergeCharacteristicPackage
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 特征值数据长度: 24, 前8字节(或更少): 
2025-05-15 18:06:43.826 20958-20958 Topping Controller      com.example.test1_example            I  5e 
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  c7 
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  ff 
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  ff 
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  00 
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  12 
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  b6 
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  1b 
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 包索引: 65535
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] 收到最终包，准备验证CRC32
2025-05-15 18:06:43.827 20958-20958 Topping Controller      com.example.test1_example            I  onStateChange; state:1
2025-05-15 18:06:43.828 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.828 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.828 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceBindings._onStateChange (package:topping_ble_control/device/d900/d900_device_bindings.dart:287:9)[0m
2025-05-15 18:06:43.828 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.828 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 Flutter回调: onStateChange, flutterObject: 1747303558296, state: 1[0m
2025-05-15 18:06:43.828 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.829 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.829 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.829 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager._handleStateChange (package:topping_ble_control/device/d900/d900_device_manager.dart:135:9)[0m
2025-05-15 18:06:43.829 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.829 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900: 收到状态变更: BleConnectionState.connected, 设备句柄: 10001, 时间戳: 1747303603828[0m
2025-05-15 18:06:43.829 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage处理完成
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] mergeCharacteristicPackage调用完成
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(onCharacteristicChanged结束):
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce8fc888
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007871c7c100
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce8fc868
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I    - mState: 1
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 18:06:43.830 20958-20958 Topping Controller      com.example.test1_example            I  [BLE日志] onCharacteristicChanged回调调用完成
2025-05-15 18:06:43.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:234:11)[0m
2025-05-15 18:06:43.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据内容 [会话ID: 1747303602022_1]: 5e c7 ff ff 00 12 b6 1b 05 21 b8 e6 d0 3e c6 0f c4 2f 15 01 8c b2 43 1e[0m
2025-05-15 18:06:43.840 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:243:11)[0m
2025-05-15 18:06:43.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据信息 [会话ID: 1747303602022_1]: 数据长度: 24 字节, 时间: 2025-05-15 18:06:43.844335[0m
2025-05-15 18:06:43.844 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.848 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:06:43.849 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:06:43.849 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptResponse (package:topping_ble_control/utils/verify_interceptor.dart:249:11)[0m
2025-05-15 18:06:43.849 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:06:43.849 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 阶段 1 已收到设备响应 [会话ID: 1747303602022_1][0m
2025-05-15 18:06:43.849 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.625 20958-21299 BluetoothGatt           com.example.test1_example            D  onClientConnectionState() - status=8 clientIf=15 connected=false device=53:4A:52:FE:02:37
2025-05-15 18:10:58.626 20958-21299 [FBP-Android]           com.example.test1_example            D  [FBP] onConnectionStateChange:disconnected
2025-05-15 18:10:58.626 20958-21299 [FBP-Android]           com.example.test1_example            D  [FBP]   status: LINK_SUPERVISION_TIMEOUT
2025-05-15 18:10:58.626 20958-21299 BluetoothGatt           com.example.test1_example            D  close()
2025-05-15 18:10:58.626 20958-21299 BluetoothGatt           com.example.test1_example            D  unregisterApp() - mClientIf=15
2025-05-15 18:10:58.633 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.633 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:10:58.633 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._setupConnectionStateListener.<anonymous closure> (package:topping_ble_control/bluetooth/connection/ble_connector.dart:65:15)[0m
2025-05-15 18:10:58.633 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.633 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 监听到连接状态变化: BluetoothConnectionState.disconnected, 时间戳: 1747303858632[0m
2025-05-15 18:10:58.633 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.634 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.634 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:10:58.634 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:10:58.634 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.634 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[QCC5125] 连接状态: 状态变化为 BluetoothConnectionState.disconnected[0m
2025-05-15 18:10:58.635 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.636 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.636 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:10:58.636 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._notifyDisconnected (package:topping_ble_control/bluetooth/connection/ble_connector.dart:254:9)[0m
2025-05-15 18:10:58.636 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.636 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 通知断开连接状态[0m
2025-05-15 18:10:58.636 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.637 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.637 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:10:58.637 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyConnectionStateChange (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:24:9)[0m
2025-05-15 18:10:58.637 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.637 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 通知连接状态变化: FfiBluetoothProfileState.stateDisconnected -> FfiBluetoothProfileState.stateDisconnected nativeObject: -5476376629577531136[0m
2025-05-15 18:10:58.637 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.638 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.638 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:10:58.638 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:10:58.638 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.638 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[设备] 连接状态: 状态变化为 FfiBluetoothProfileState.stateDisconnected[0m
2025-05-15 18:10:58.638 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.639 20958-20958 Topping Controller      com.example.test1_example            I  onStateChange; state:2
2025-05-15 18:10:58.640 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.640 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:10:58.640 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceBindings._onStateChange (package:topping_ble_control/device/d900/d900_device_bindings.dart:287:9)[0m
2025-05-15 18:10:58.640 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.640 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 Flutter回调: onStateChange, flutterObject: 1747303558296, state: 2[0m
2025-05-15 18:10:58.640 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.641 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.641 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:10:58.641 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager._handleStateChange (package:topping_ble_control/device/d900/d900_device_manager.dart:135:9)[0m
2025-05-15 18:10:58.641 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.641 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900: 收到状态变更: BleConnectionState.disconnected, 设备句柄: 10001, 时间戳: 1747303858640[0m
2025-05-15 18:10:58.641 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.643 20958-20958 flutter                 com.example.test1_example            I  [38;5;208m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.643 20958-20958 flutter                 com.example.test1_example            I  [38;5;208m│ #0   Log.w (package:topping_ble_control/utils/log_util.dart:28:13)[0m
2025-05-15 18:10:58.643 20958-20958 flutter                 com.example.test1_example            I  [38;5;208m│ #1   D900DeviceManager._handleStateChange (package:topping_ble_control/device/d900/d900_device_manager.dart:144:13)[0m
2025-05-15 18:10:58.643 20958-20958 flutter                 com.example.test1_example            I  [38;5;208m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.643 20958-20958 flutter                 com.example.test1_example            I  [38;5;208m│ ⚠️ D900: 连接丢失/失败于验证期间，停止验证会话。[0m
2025-05-15 18:10:58.643 20958-20958 flutter                 com.example.test1_example            I  [38;5;208m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.644 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.644 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:10:58.644 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.stopVerifying (package:topping_ble_control/utils/verify_interceptor.dart:95:9)[0m
2025-05-15 18:10:58.644 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.645 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 VerifyInterceptor: 结束验证模式，会话ID：1747303602022_1[0m
2025-05-15 18:10:58.645 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.645 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 18:10:58.646 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 18:10:58.646 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 18:10:58.646 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 18:10:58.646 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✓ ==== 验证结束 ==== ✓ [会话ID: 1747303602022_1][0m
2025-05-15 18:10:58.646 20958-20958 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
