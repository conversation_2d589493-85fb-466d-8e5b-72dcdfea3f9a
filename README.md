# Topping BLE Control

这是一个Flutter插件项目，用于连接和控制Topping蓝牙设备。

## 项目结构

该项目包含以下组件：
- Flutter插件：提供Dart API供Flutter应用调用
- Android原生实现：通过MethodChannel处理蓝牙通信
- BLE库：提供基础的蓝牙低功耗通信功能
- Gaia库：提供与高通GAIA协议的兼容功能
- VMUpgrade库：提供固件升级功能

## 设置项目

1. 已将依赖库从项目引用改为AAR文件引用
2. 在`android/libs`目录下包含了所有需要的AAR文件

## 功能

插件包含以下主要功能：
- 蓝牙设备扫描
- 设备连接与断开
- 设备状态监控
- OTA固件升级

## 开发与编译

### 所需环境
- Flutter SDK
- Android Studio 4.2+
- JDK 8+
- Android SDK 31+

### 编译步骤
1. 运行 `flutter pub get` 安装依赖
2. 运行 `flutter build` 构建项目

## 注意事项

- 插件需要蓝牙权限和位置权限
- 在Android 12及以上版本中，需要新的蓝牙权限（BLUETOOTH_SCAN和BLUETOOTH_CONNECT）

## 项目特点

- 使用Flutter插件架构实现蓝牙通信
- 支持FFI原生代码调用
- 跨平台支持（Android、iOS）
- 完整的蓝牙设备管理功能
- OTA固件升级能力

## 目录结构

```
lib/                 # Flutter插件的Dart代码
  ├── bluetooth/     # 蓝牙相关功能
  ├── model/         # 数据模型
  ├── service/       # 服务实现
  ├── ui/            # UI组件
  └── utils/         # 工具类
android/             # Android平台实现
  ├── src/           # 源代码目录
  │   └── main/      # 主要源代码
  │       ├── kotlin/# Kotlin实现代码
  │       └── AndroidManifest.xml
  └── libs/          # AAR依赖库
src/                 # C/C++源代码（通过FFI调用）
```

## 使用方法

在Flutter应用的pubspec.yaml中添加依赖：

```yaml
dependencies:
  topping_ble_control:
    path: ../path_to_plugin
```

然后在代码中导入并使用：

```dart
import 'package:topping_ble_control/dx5ii_device_manager.dart';

// 使用插件API
final deviceManager = DX5IIDeviceManager();
deviceManager.startScan();
```

## 蓝牙功能

- 设备扫描
- 设备连接/断开
- 服务发现
- 特征值读写
- 连接状态监控

## 开发说明

### FFI 绑定

项目使用 `dart:ffi` 调用原生代码。如需重新生成绑定，运行：

```bash
dart run ffigen --config ffigen.yaml
```

### 平台支持

- Android: 使用 Gradle 和 Android NDK
- iOS/macOS: 使用 Xcode 和 CocoaPods
- Linux/Windows: 使用 CMake

## 许可证

[添加您的许可证信息]

## 贡献指南




